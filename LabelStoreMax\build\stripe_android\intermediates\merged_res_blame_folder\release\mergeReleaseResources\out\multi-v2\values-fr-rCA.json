{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3578,3676,3778,3877,3979,4083,4187,14477", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "3671,3773,3872,3974,4078,4182,4296,14573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,251,318,386,451,526", "endColumns": "89,105,66,67,64,74,67", "endOffsets": "140,246,313,381,446,521,589"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14578,14668,14774,14841,14909,14974,15049", "endColumns": "89,105,66,67,64,74,67", "endOffsets": "14663,14769,14836,14904,14969,15044,15112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,210,272,348,433,494,561,612,697,785,872,976,1080,1167,1247,1329,1413,1517,1612,1682,1774,1863,1948,2055,2137,2229,2306,2405,2488,2595,2695,2791,2883,2993,3075,3163,3285,3400,3483,3548,4311,5050,5129,5243,5354,5409,5520,5632,5706,5812,5918,5974,6062,6112,6195,6313,6388,6465,6532,6598,6646,6734,6836,6915,6963,7012,7081,7139,7204,7405,7601,7750,7818,7907,7993,8101,8200,8301,8384,8480,8560,8674,8756,8857,8911,9056,9108,9163,9232,9302,9377,9447,9521,9611,9687,9743", "endColumns": "72,81,61,75,84,60,66,50,84,87,86,103,103,86,79,81,83,103,94,69,91,88,84,106,81,91,76,98,82,106,99,95,91,109,81,87,121,114,82,64,762,738,78,113,110,54,110,111,73,105,105,55,87,49,82,117,74,76,66,65,47,87,101,78,47,48,68,57,64,200,195,148,67,88,85,107,98,100,82,95,79,113,81,100,53,144,51,54,68,69,74,69,73,89,75,55,78", "endOffsets": "123,205,267,343,428,489,556,607,692,780,867,971,1075,1162,1242,1324,1408,1512,1607,1677,1769,1858,1943,2050,2132,2224,2301,2400,2483,2590,2690,2786,2878,2988,3070,3158,3280,3395,3478,3543,4306,5045,5124,5238,5349,5404,5515,5627,5701,5807,5913,5969,6057,6107,6190,6308,6383,6460,6527,6593,6641,6729,6831,6910,6958,7007,7076,7134,7199,7400,7596,7745,7813,7902,7988,8096,8195,8296,8379,8475,8555,8669,8751,8852,8906,9051,9103,9158,9227,9297,9372,9442,9516,9606,9682,9738,9817"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,321,322,324,325,329,330,332,337,344,345,416,417,418,420,425,438,439,440,441,442,443,444,460", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15117,15190,15272,15334,15410,15495,15556,15872,15923,16008,16096,16330,16516,16620,16774,17131,17419,18453,18695,18790,18860,19019,19108,19193,19360,19661,19753,19830,19929,20012,20119,20219,20315,20407,20517,20680,20958,21080,21864,21947,22012,23765,24504,24583,24697,24808,24863,24974,25086,25160,25266,25372,25428,26061,26111,26194,26474,26549,26626,26693,27221,27622,27710,27812,27891,27939,28252,28321,28379,28444,28645,28841,29191,29259,30188,30274,30470,30569,30987,31070,31237,31882,32461,32543,40515,40569,40714,40880,41487,43932,44002,44077,44147,44221,44311,44387,45912", "endColumns": "72,81,61,75,84,60,66,50,84,87,86,103,103,86,79,81,83,103,94,69,91,88,84,106,81,91,76,98,82,106,99,95,91,109,81,87,121,114,82,64,762,738,78,113,110,54,110,111,73,105,105,55,87,49,82,117,74,76,66,65,47,87,101,78,47,48,68,57,64,200,195,148,67,88,85,107,98,100,82,95,79,113,81,100,53,144,51,54,68,69,74,69,73,89,75,55,78", "endOffsets": "15185,15267,15329,15405,15490,15551,15618,15918,16003,16091,16178,16429,16615,16702,16849,17208,17498,18552,18785,18855,18947,19103,19188,19295,19437,19748,19825,19924,20007,20114,20214,20310,20402,20512,20594,20763,21075,21190,21942,22007,22770,24499,24578,24692,24803,24858,24969,25081,25155,25261,25367,25423,25511,26106,26189,26307,26544,26621,26688,26754,27264,27705,27807,27886,27934,27983,28316,28374,28439,28640,28836,28985,29254,29343,30269,30377,30564,30665,31065,31161,31312,31991,32538,32639,40564,40709,40761,40930,41551,43997,44072,44142,44216,44306,44382,44438,45986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5933", "endColumns": "160", "endOffsets": "6089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1150,1216,1313,1393,1455,1547,1614,1688,1749,1828,1892,1946,2062,2121,2183,2237,2319,2448,2540,2615,2710,2791,2875,3019,3098,3179,3326,3419,3498,3553,3604,3670,3749,3830,3901,3981,4053,4131,4206,4278,4389,4486,4563,4661,4759,4837,4918,5018,5075,5159,5225,5308,5395,5457,5521,5584,5660,5762,5869,5966,6072,6131,6186,6275,6362,6439", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "278,385,493,575,676,773,873,995,1080,1145,1211,1308,1388,1450,1542,1609,1683,1744,1823,1887,1941,2057,2116,2178,2232,2314,2443,2535,2610,2705,2786,2870,3014,3093,3174,3321,3414,3493,3548,3599,3665,3744,3825,3896,3976,4048,4126,4201,4273,4384,4481,4558,4656,4754,4832,4913,5013,5070,5154,5220,5303,5390,5452,5516,5579,5655,5757,5864,5961,6067,6126,6181,6270,6357,6434,6515"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3083,3190,3298,3380,3481,4301,4401,4523,7757,7822,7965,8449,8703,8765,8857,8924,8998,9059,9138,9202,9256,9372,9431,9493,9547,9629,9758,9850,9925,10020,10101,10185,10329,10408,10489,10636,10729,10808,10863,10914,10980,11059,11140,11211,11291,11363,11441,11516,11588,11699,11796,11873,11971,12069,12147,12228,12328,12385,12469,12535,12618,12705,12767,12831,12894,12970,13072,13179,13276,13382,13441,13814,14156,14243,14396", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "328,3185,3293,3375,3476,3573,4396,4518,4603,7817,7883,8057,8524,8760,8852,8919,8993,9054,9133,9197,9251,9367,9426,9488,9542,9624,9753,9845,9920,10015,10096,10180,10324,10403,10484,10631,10724,10803,10858,10909,10975,11054,11135,11206,11286,11358,11436,11511,11583,11694,11791,11868,11966,12064,12142,12223,12323,12380,12464,12530,12613,12700,12762,12826,12889,12965,13067,13174,13271,13377,13436,13491,13898,14238,14315,14472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,325,426,637,684,756,860,935,1208,1271,1366,1440,1495,1559,1632,1721,2045,2112,2179,2234,2289,2383,2533,2646,2705,2792,2880,2973,3044,3147,3449,3530,3609,3686,3748,3810,3888,3963,4042,4151,4256,4355,4473,4576,4650,4729,4820,5018,5228,5361,5508,5570,6437,6545,6610", "endColumns": "106,162,100,210,46,71,103,74,272,62,94,73,54,63,72,88,323,66,66,54,54,93,149,112,58,86,87,92,70,102,301,80,78,76,61,61,77,74,78,108,104,98,117,102,73,78,90,197,209,132,146,61,866,107,64,57", "endOffsets": "157,320,421,632,679,751,855,930,1203,1266,1361,1435,1490,1554,1627,1716,2040,2107,2174,2229,2284,2378,2528,2641,2700,2787,2875,2968,3039,3142,3444,3525,3604,3681,3743,3805,3883,3958,4037,4146,4251,4350,4468,4571,4645,4724,4815,5013,5223,5356,5503,5565,6432,6540,6605,6663"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,313,314,315,316,317,323,326,331,333,334,335,336,339,341,342,346,350,351,353,355,367,392,393,394,395,396,414,421,422,423,424,426,427,428,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21195,21302,21465,21653,22775,22822,22894,22998,23073,23346,23409,25516,25790,25997,26312,26385,26759,27488,27555,28135,29136,29348,29442,29592,29705,29764,30382,30670,31166,31317,31420,31722,31803,32071,32272,32334,32644,33265,33340,33509,33728,35557,38057,38175,38278,38352,38431,40221,40935,41145,41278,41425,41556,42423,42531,44443", "endColumns": "106,162,100,210,46,71,103,74,272,62,94,73,54,63,72,88,323,66,66,54,54,93,149,112,58,86,87,92,70,102,301,80,78,76,61,61,77,74,78,108,104,98,117,102,73,78,90,197,209,132,146,61,866,107,64,57", "endOffsets": "21297,21460,21561,21859,22817,22889,22993,23068,23341,23404,23499,25585,25840,26056,26380,26469,27078,27550,27617,28185,29186,29437,29587,29700,29759,29846,30465,30758,31232,31415,31717,31798,31877,32143,32329,32391,32717,33335,33414,33613,33828,35651,38170,38273,38347,38426,38517,40414,41140,41273,41420,41482,42418,42526,42591,44496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,197,259,341,403,474,532,614,687,754,814", "endColumns": "81,59,61,81,61,70,57,81,72,66,59,69", "endOffsets": "132,192,254,336,398,469,527,609,682,749,809,879"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16248,17071,17213,17275,17357,17648,18179,18371,18622,18952,19300,19591", "endColumns": "81,59,61,81,61,70,57,81,72,66,59,69", "endOffsets": "16325,17126,17270,17352,17414,17714,18232,18448,18690,19014,19355,19656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "480,481", "startColumns": "4,4", "startOffsets": "47801,47889", "endColumns": "87,94", "endOffsets": "47884,47979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,390,496,583,663,757,849,936,1007,1075,1156,1241,1317,1396,1465", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "199,287,385,491,578,658,752,844,931,1002,1070,1151,1236,1312,1391,1460,1582"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,477,478,479", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4608,4707,7392,7490,7670,8529,8609,13496,13588,13675,13746,13903,13984,14320,47531,47610,47679", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "4702,4790,7485,7591,7752,8604,8698,13583,13670,13741,13809,13979,14064,14391,47605,47674,47796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,74", "endOffsets": "262,337"}, "to": {"startLines": "81,482", "startColumns": "4,4", "startOffsets": "8388,47984", "endColumns": "60,78", "endOffsets": "8444,48058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,204", "endColumns": "74,73,76", "endOffsets": "125,199,276"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4795,7596,7888", "endColumns": "74,73,76", "endOffsets": "4865,7665,7960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7285,8062,8164,8283", "endColumns": "106,101,118,104", "endOffsets": "7387,8159,8278,8383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,221,304,391,485,569,652,804,891,1007,1110,1247,1322,1446,1511,1682,1881,2054,2144,2254,2356,2458,2653,2780,2892,3153,3264,3406,3631,3880,3978,4112,4314,4413,4474,4540,4625,4727,4830,4905,5005,5097,5202,5446,5521,5593,5674,5808,5883,5967,6034,6118,6189,6258,6379,6476,6589,6692,6769,6865,6936,7031,7096,7211,7328,7439,7579,7644,7743,7855,8006,8078,8174,8288,8350,8408,8487,8611,8791,9109,9445,9544,9624,9725,9801,9926,10083,10172,10265,10335,10422,10514,10611,10742,10884,10963,11035,11090,11253,11322,11383,11468,11544,11622,11716,11863,11981,12090,12182,12258,12340,12417", "endColumns": "78,86,82,86,93,83,82,151,86,115,102,136,74,123,64,170,198,172,89,109,101,101,194,126,111,260,110,141,224,248,97,133,201,98,60,65,84,101,102,74,99,91,104,243,74,71,80,133,74,83,66,83,70,68,120,96,112,102,76,95,70,94,64,114,116,110,139,64,98,111,150,71,95,113,61,57,78,123,179,317,335,98,79,100,75,124,156,88,92,69,86,91,96,130,141,78,71,54,162,68,60,84,75,77,93,146,117,108,91,75,81,76,157", "endOffsets": "129,216,299,386,480,564,647,799,886,1002,1105,1242,1317,1441,1506,1677,1876,2049,2139,2249,2351,2453,2648,2775,2887,3148,3259,3401,3626,3875,3973,4107,4309,4408,4469,4535,4620,4722,4825,4900,5000,5092,5197,5441,5516,5588,5669,5803,5878,5962,6029,6113,6184,6253,6374,6471,6584,6687,6764,6860,6931,7026,7091,7206,7323,7434,7574,7639,7738,7850,8001,8073,8169,8283,8345,8403,8482,8606,8786,9104,9440,9539,9619,9720,9796,9921,10078,10167,10260,10330,10417,10509,10606,10737,10879,10958,11030,11085,11248,11317,11378,11463,11539,11617,11711,11858,11976,12085,12177,12253,12335,12412,12570"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,320,338,340,343,347,348,349,352,354,356,357,358,359,360,361,362,363,364,365,366,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,415,419,429,430,431,432,433,434,435,436,437,446,447,448,449,450,451,452,453,454,455,456,457,458,459,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15623,15702,15789,21566,23504,23598,23682,25845,27134,27269,27385,30051,31996,32148,32396,32722,32893,33092,33419,33618,33833,33935,34037,34232,34359,34471,34732,34843,34985,35210,35459,35656,35790,35992,36091,36152,36218,36303,36405,36508,36583,36683,36775,36880,37124,37199,37271,37352,37486,37561,37645,37712,37796,37867,37936,38522,38619,38732,38835,38912,39008,39079,39174,39239,39354,39471,39582,39722,39787,39886,39998,40149,40419,40766,42596,42658,42716,42795,42919,43099,43417,43753,43852,44501,44602,44678,44803,44960,45049,45142,45212,45299,45391,45488,45619,45761,45840,45991,46046,46209,46278,46339,46424,46500,46578,46672,46819,46937,47046,47138,47214,47296,47373", "endColumns": "78,86,82,86,93,83,82,151,86,115,102,136,74,123,64,170,198,172,89,109,101,101,194,126,111,260,110,141,224,248,97,133,201,98,60,65,84,101,102,74,99,91,104,243,74,71,80,133,74,83,66,83,70,68,120,96,112,102,76,95,70,94,64,114,116,110,139,64,98,111,150,71,95,113,61,57,78,123,179,317,335,98,79,100,75,124,156,88,92,69,86,91,96,130,141,78,71,54,162,68,60,84,75,77,93,146,117,108,91,75,81,76,157", "endOffsets": "15697,15784,15867,21648,23593,23677,23760,25992,27216,27380,27483,30183,32066,32267,32456,32888,33087,33260,33504,33723,33930,34032,34227,34354,34466,34727,34838,34980,35205,35454,35552,35785,35987,36086,36147,36213,36298,36400,36503,36578,36678,36770,36875,37119,37194,37266,37347,37481,37556,37640,37707,37791,37862,37931,38052,38614,38727,38830,38907,39003,39074,39169,39234,39349,39466,39577,39717,39782,39881,39993,40144,40216,40510,40875,42653,42711,42790,42914,43094,43412,43748,43847,43927,44597,44673,44798,44955,45044,45137,45207,45294,45386,45483,45614,45756,45835,45907,46041,46204,46273,46334,46419,46495,46573,46667,46814,46932,47041,47133,47209,47291,47368,47526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,202,269,354,425,486,558,631,695,763,833,893,953,1027,1091,1162,1225,1290,1355,1439,1520,1612,1710,1828,1910,1961,2011,2108,2170,2245,2316,2427,2516,2628", "endColumns": "64,81,66,84,70,60,71,72,63,67,69,59,59,73,63,70,62,64,64,83,80,91,97,117,81,50,49,96,61,74,70,110,88,111,111", "endOffsets": "115,197,264,349,420,481,553,626,690,758,828,888,948,1022,1086,1157,1220,1285,1350,1434,1515,1607,1705,1823,1905,1956,2006,2103,2165,2240,2311,2422,2511,2623,2735"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,318,319,327,328", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16183,16434,16707,16854,16939,17010,17503,17575,17719,17783,17851,17921,17981,18041,18115,18237,18308,18557,19442,19507,20599,20768,20860,25590,25708,27083,27988,28038,28190,28990,29065,29851,29962,30763,30875", "endColumns": "64,81,66,84,70,60,71,72,63,67,69,59,59,73,63,70,62,64,64,83,80,91,97,117,81,50,49,96,61,74,70,110,88,111,111", "endOffsets": "16243,16511,16769,16934,17005,17066,17570,17643,17778,17846,17916,17976,18036,18110,18174,18303,18366,18617,19502,19586,20675,20855,20953,25703,25785,27129,28033,28130,28247,29060,29131,29957,30046,30870,30982"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,551,661,748,854,984,1069,1149,1240,1333,1431,1526,1626,1719,1812,1907,1998,2089,2175,2285,2396,2499,2610,2718,2825,2984,14069", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,546,656,743,849,979,1064,1144,1235,1328,1426,1521,1621,1714,1807,1902,1993,2084,2170,2280,2391,2494,2605,2713,2820,2979,3078,14151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2303", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,62,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2302,2382"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4870,4976,5156,5286,5395,5566,5699,5820,6094,6289,6401,6586,6722,6882,7061,7134,7201", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,66,83", "endOffsets": "4971,5151,5281,5390,5561,5694,5815,5928,6284,6396,6581,6717,6877,7056,7129,7196,7280"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3578,3676,3778,3877,3979,4083,4187,14477", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "3671,3773,3872,3974,4078,4182,4296,14573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,251,318,386,451,526", "endColumns": "89,105,66,67,64,74,67", "endOffsets": "140,246,313,381,446,521,589"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14578,14668,14774,14841,14909,14974,15049", "endColumns": "89,105,66,67,64,74,67", "endOffsets": "14663,14769,14836,14904,14969,15044,15112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,210,272,348,433,494,561,612,697,785,872,976,1080,1167,1247,1329,1413,1517,1612,1682,1774,1863,1948,2055,2137,2229,2306,2405,2488,2595,2695,2791,2883,2993,3075,3163,3285,3400,3483,3548,4311,5050,5129,5243,5354,5409,5520,5632,5706,5812,5918,5974,6062,6112,6195,6313,6388,6465,6532,6598,6646,6734,6836,6915,6963,7012,7081,7139,7204,7405,7601,7750,7818,7907,7993,8101,8200,8301,8384,8480,8560,8674,8756,8857,8911,9056,9108,9163,9232,9302,9377,9447,9521,9611,9687,9743", "endColumns": "72,81,61,75,84,60,66,50,84,87,86,103,103,86,79,81,83,103,94,69,91,88,84,106,81,91,76,98,82,106,99,95,91,109,81,87,121,114,82,64,762,738,78,113,110,54,110,111,73,105,105,55,87,49,82,117,74,76,66,65,47,87,101,78,47,48,68,57,64,200,195,148,67,88,85,107,98,100,82,95,79,113,81,100,53,144,51,54,68,69,74,69,73,89,75,55,78", "endOffsets": "123,205,267,343,428,489,556,607,692,780,867,971,1075,1162,1242,1324,1408,1512,1607,1677,1769,1858,1943,2050,2132,2224,2301,2400,2483,2590,2690,2786,2878,2988,3070,3158,3280,3395,3478,3543,4306,5045,5124,5238,5349,5404,5515,5627,5701,5807,5913,5969,6057,6107,6190,6308,6383,6460,6527,6593,6641,6729,6831,6910,6958,7007,7076,7134,7199,7400,7596,7745,7813,7902,7988,8096,8195,8296,8379,8475,8555,8669,8751,8852,8906,9051,9103,9158,9227,9297,9372,9442,9516,9606,9682,9738,9817"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,321,322,324,325,329,330,332,337,344,345,416,417,418,420,425,438,439,440,441,442,443,444,460", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15117,15190,15272,15334,15410,15495,15556,15872,15923,16008,16096,16330,16516,16620,16774,17131,17419,18453,18695,18790,18860,19019,19108,19193,19360,19661,19753,19830,19929,20012,20119,20219,20315,20407,20517,20680,20958,21080,21864,21947,22012,23765,24504,24583,24697,24808,24863,24974,25086,25160,25266,25372,25428,26061,26111,26194,26474,26549,26626,26693,27221,27622,27710,27812,27891,27939,28252,28321,28379,28444,28645,28841,29191,29259,30188,30274,30470,30569,30987,31070,31237,31882,32461,32543,40515,40569,40714,40880,41487,43932,44002,44077,44147,44221,44311,44387,45912", "endColumns": "72,81,61,75,84,60,66,50,84,87,86,103,103,86,79,81,83,103,94,69,91,88,84,106,81,91,76,98,82,106,99,95,91,109,81,87,121,114,82,64,762,738,78,113,110,54,110,111,73,105,105,55,87,49,82,117,74,76,66,65,47,87,101,78,47,48,68,57,64,200,195,148,67,88,85,107,98,100,82,95,79,113,81,100,53,144,51,54,68,69,74,69,73,89,75,55,78", "endOffsets": "15185,15267,15329,15405,15490,15551,15618,15918,16003,16091,16178,16429,16615,16702,16849,17208,17498,18552,18785,18855,18947,19103,19188,19295,19437,19748,19825,19924,20007,20114,20214,20310,20402,20512,20594,20763,21075,21190,21942,22007,22770,24499,24578,24692,24803,24858,24969,25081,25155,25261,25367,25423,25511,26106,26189,26307,26544,26621,26688,26754,27264,27705,27807,27886,27934,27983,28316,28374,28439,28640,28836,28985,29254,29343,30269,30377,30564,30665,31065,31161,31312,31991,32538,32639,40564,40709,40761,40930,41551,43997,44072,44142,44216,44306,44382,44438,45986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5933", "endColumns": "160", "endOffsets": "6089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1150,1216,1313,1393,1455,1547,1614,1688,1749,1828,1892,1946,2062,2121,2183,2237,2319,2448,2540,2615,2710,2791,2875,3019,3098,3179,3326,3419,3498,3553,3604,3670,3749,3830,3901,3981,4053,4131,4206,4278,4389,4486,4563,4661,4759,4837,4918,5018,5075,5159,5225,5308,5395,5457,5521,5584,5660,5762,5869,5966,6072,6131,6186,6275,6362,6439", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "278,385,493,575,676,773,873,995,1080,1145,1211,1308,1388,1450,1542,1609,1683,1744,1823,1887,1941,2057,2116,2178,2232,2314,2443,2535,2610,2705,2786,2870,3014,3093,3174,3321,3414,3493,3548,3599,3665,3744,3825,3896,3976,4048,4126,4201,4273,4384,4481,4558,4656,4754,4832,4913,5013,5070,5154,5220,5303,5390,5452,5516,5579,5655,5757,5864,5961,6067,6126,6181,6270,6357,6434,6515"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3083,3190,3298,3380,3481,4301,4401,4523,7757,7822,7965,8449,8703,8765,8857,8924,8998,9059,9138,9202,9256,9372,9431,9493,9547,9629,9758,9850,9925,10020,10101,10185,10329,10408,10489,10636,10729,10808,10863,10914,10980,11059,11140,11211,11291,11363,11441,11516,11588,11699,11796,11873,11971,12069,12147,12228,12328,12385,12469,12535,12618,12705,12767,12831,12894,12970,13072,13179,13276,13382,13441,13814,14156,14243,14396", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "328,3185,3293,3375,3476,3573,4396,4518,4603,7817,7883,8057,8524,8760,8852,8919,8993,9054,9133,9197,9251,9367,9426,9488,9542,9624,9753,9845,9920,10015,10096,10180,10324,10403,10484,10631,10724,10803,10858,10909,10975,11054,11135,11206,11286,11358,11436,11511,11583,11694,11791,11868,11966,12064,12142,12223,12323,12380,12464,12530,12613,12700,12762,12826,12889,12965,13067,13174,13271,13377,13436,13491,13898,14238,14315,14472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,325,426,637,684,756,860,935,1208,1271,1366,1440,1495,1559,1632,1721,2045,2112,2179,2234,2289,2383,2533,2646,2705,2792,2880,2973,3044,3147,3449,3530,3609,3686,3748,3810,3888,3963,4042,4151,4256,4355,4473,4576,4650,4729,4820,5018,5228,5361,5508,5570,6437,6545,6610", "endColumns": "106,162,100,210,46,71,103,74,272,62,94,73,54,63,72,88,323,66,66,54,54,93,149,112,58,86,87,92,70,102,301,80,78,76,61,61,77,74,78,108,104,98,117,102,73,78,90,197,209,132,146,61,866,107,64,57", "endOffsets": "157,320,421,632,679,751,855,930,1203,1266,1361,1435,1490,1554,1627,1716,2040,2107,2174,2229,2284,2378,2528,2641,2700,2787,2875,2968,3039,3142,3444,3525,3604,3681,3743,3805,3883,3958,4037,4146,4251,4350,4468,4571,4645,4724,4815,5013,5223,5356,5503,5565,6432,6540,6605,6663"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,313,314,315,316,317,323,326,331,333,334,335,336,339,341,342,346,350,351,353,355,367,392,393,394,395,396,414,421,422,423,424,426,427,428,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21195,21302,21465,21653,22775,22822,22894,22998,23073,23346,23409,25516,25790,25997,26312,26385,26759,27488,27555,28135,29136,29348,29442,29592,29705,29764,30382,30670,31166,31317,31420,31722,31803,32071,32272,32334,32644,33265,33340,33509,33728,35557,38057,38175,38278,38352,38431,40221,40935,41145,41278,41425,41556,42423,42531,44443", "endColumns": "106,162,100,210,46,71,103,74,272,62,94,73,54,63,72,88,323,66,66,54,54,93,149,112,58,86,87,92,70,102,301,80,78,76,61,61,77,74,78,108,104,98,117,102,73,78,90,197,209,132,146,61,866,107,64,57", "endOffsets": "21297,21460,21561,21859,22817,22889,22993,23068,23341,23404,23499,25585,25840,26056,26380,26469,27078,27550,27617,28185,29186,29437,29587,29700,29759,29846,30465,30758,31232,31415,31717,31798,31877,32143,32329,32391,32717,33335,33414,33613,33828,35651,38170,38273,38347,38426,38517,40414,41140,41273,41420,41482,42418,42526,42591,44496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,197,259,341,403,474,532,614,687,754,814", "endColumns": "81,59,61,81,61,70,57,81,72,66,59,69", "endOffsets": "132,192,254,336,398,469,527,609,682,749,809,879"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16248,17071,17213,17275,17357,17648,18179,18371,18622,18952,19300,19591", "endColumns": "81,59,61,81,61,70,57,81,72,66,59,69", "endOffsets": "16325,17126,17270,17352,17414,17714,18232,18448,18690,19014,19355,19656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "480,481", "startColumns": "4,4", "startOffsets": "47801,47889", "endColumns": "87,94", "endOffsets": "47884,47979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,390,496,583,663,757,849,936,1007,1075,1156,1241,1317,1396,1465", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "199,287,385,491,578,658,752,844,931,1002,1070,1151,1236,1312,1391,1460,1582"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,477,478,479", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4608,4707,7392,7490,7670,8529,8609,13496,13588,13675,13746,13903,13984,14320,47531,47610,47679", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "4702,4790,7485,7591,7752,8604,8698,13583,13670,13741,13809,13979,14064,14391,47605,47674,47796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,74", "endOffsets": "262,337"}, "to": {"startLines": "81,482", "startColumns": "4,4", "startOffsets": "8388,47984", "endColumns": "60,78", "endOffsets": "8444,48058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,204", "endColumns": "74,73,76", "endOffsets": "125,199,276"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4795,7596,7888", "endColumns": "74,73,76", "endOffsets": "4865,7665,7960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7285,8062,8164,8283", "endColumns": "106,101,118,104", "endOffsets": "7387,8159,8278,8383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,221,304,391,485,569,652,804,891,1007,1110,1247,1322,1446,1511,1682,1881,2054,2144,2254,2356,2458,2653,2780,2892,3153,3264,3406,3631,3880,3978,4112,4314,4413,4474,4540,4625,4727,4830,4905,5005,5097,5202,5446,5521,5593,5674,5808,5883,5967,6034,6118,6189,6258,6379,6476,6589,6692,6769,6865,6936,7031,7096,7211,7328,7439,7579,7644,7743,7855,8006,8078,8174,8288,8350,8408,8487,8611,8791,9109,9445,9544,9624,9725,9801,9926,10083,10172,10265,10335,10422,10514,10611,10742,10884,10963,11035,11090,11253,11322,11383,11468,11544,11622,11716,11863,11981,12090,12182,12258,12340,12417", "endColumns": "78,86,82,86,93,83,82,151,86,115,102,136,74,123,64,170,198,172,89,109,101,101,194,126,111,260,110,141,224,248,97,133,201,98,60,65,84,101,102,74,99,91,104,243,74,71,80,133,74,83,66,83,70,68,120,96,112,102,76,95,70,94,64,114,116,110,139,64,98,111,150,71,95,113,61,57,78,123,179,317,335,98,79,100,75,124,156,88,92,69,86,91,96,130,141,78,71,54,162,68,60,84,75,77,93,146,117,108,91,75,81,76,157", "endOffsets": "129,216,299,386,480,564,647,799,886,1002,1105,1242,1317,1441,1506,1677,1876,2049,2139,2249,2351,2453,2648,2775,2887,3148,3259,3401,3626,3875,3973,4107,4309,4408,4469,4535,4620,4722,4825,4900,5000,5092,5197,5441,5516,5588,5669,5803,5878,5962,6029,6113,6184,6253,6374,6471,6584,6687,6764,6860,6931,7026,7091,7206,7323,7434,7574,7639,7738,7850,8001,8073,8169,8283,8345,8403,8482,8606,8786,9104,9440,9539,9619,9720,9796,9921,10078,10167,10260,10330,10417,10509,10606,10737,10879,10958,11030,11085,11248,11317,11378,11463,11539,11617,11711,11858,11976,12085,12177,12253,12335,12412,12570"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,320,338,340,343,347,348,349,352,354,356,357,358,359,360,361,362,363,364,365,366,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,415,419,429,430,431,432,433,434,435,436,437,446,447,448,449,450,451,452,453,454,455,456,457,458,459,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15623,15702,15789,21566,23504,23598,23682,25845,27134,27269,27385,30051,31996,32148,32396,32722,32893,33092,33419,33618,33833,33935,34037,34232,34359,34471,34732,34843,34985,35210,35459,35656,35790,35992,36091,36152,36218,36303,36405,36508,36583,36683,36775,36880,37124,37199,37271,37352,37486,37561,37645,37712,37796,37867,37936,38522,38619,38732,38835,38912,39008,39079,39174,39239,39354,39471,39582,39722,39787,39886,39998,40149,40419,40766,42596,42658,42716,42795,42919,43099,43417,43753,43852,44501,44602,44678,44803,44960,45049,45142,45212,45299,45391,45488,45619,45761,45840,45991,46046,46209,46278,46339,46424,46500,46578,46672,46819,46937,47046,47138,47214,47296,47373", "endColumns": "78,86,82,86,93,83,82,151,86,115,102,136,74,123,64,170,198,172,89,109,101,101,194,126,111,260,110,141,224,248,97,133,201,98,60,65,84,101,102,74,99,91,104,243,74,71,80,133,74,83,66,83,70,68,120,96,112,102,76,95,70,94,64,114,116,110,139,64,98,111,150,71,95,113,61,57,78,123,179,317,335,98,79,100,75,124,156,88,92,69,86,91,96,130,141,78,71,54,162,68,60,84,75,77,93,146,117,108,91,75,81,76,157", "endOffsets": "15697,15784,15867,21648,23593,23677,23760,25992,27216,27380,27483,30183,32066,32267,32456,32888,33087,33260,33504,33723,33930,34032,34227,34354,34466,34727,34838,34980,35205,35454,35552,35785,35987,36086,36147,36213,36298,36400,36503,36578,36678,36770,36875,37119,37194,37266,37347,37481,37556,37640,37707,37791,37862,37931,38052,38614,38727,38830,38907,39003,39074,39169,39234,39349,39466,39577,39717,39782,39881,39993,40144,40216,40510,40875,42653,42711,42790,42914,43094,43412,43748,43847,43927,44597,44673,44798,44955,45044,45137,45207,45294,45386,45483,45614,45756,45835,45907,46041,46204,46273,46334,46419,46495,46573,46667,46814,46932,47041,47133,47209,47291,47368,47526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,202,269,354,425,486,558,631,695,763,833,893,953,1027,1091,1162,1225,1290,1355,1439,1520,1612,1710,1828,1910,1961,2011,2108,2170,2245,2316,2427,2516,2628", "endColumns": "64,81,66,84,70,60,71,72,63,67,69,59,59,73,63,70,62,64,64,83,80,91,97,117,81,50,49,96,61,74,70,110,88,111,111", "endOffsets": "115,197,264,349,420,481,553,626,690,758,828,888,948,1022,1086,1157,1220,1285,1350,1434,1515,1607,1705,1823,1905,1956,2006,2103,2165,2240,2311,2422,2511,2623,2735"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,318,319,327,328", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16183,16434,16707,16854,16939,17010,17503,17575,17719,17783,17851,17921,17981,18041,18115,18237,18308,18557,19442,19507,20599,20768,20860,25590,25708,27083,27988,28038,28190,28990,29065,29851,29962,30763,30875", "endColumns": "64,81,66,84,70,60,71,72,63,67,69,59,59,73,63,70,62,64,64,83,80,91,97,117,81,50,49,96,61,74,70,110,88,111,111", "endOffsets": "16243,16511,16769,16934,17005,17066,17570,17643,17778,17846,17916,17976,18036,18110,18174,18303,18366,18617,19502,19586,20675,20855,20953,25703,25785,27129,28033,28130,28247,29060,29131,29957,30046,30870,30982"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,551,661,748,854,984,1069,1149,1240,1333,1431,1526,1626,1719,1812,1907,1998,2089,2175,2285,2396,2499,2610,2718,2825,2984,14069", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,546,656,743,849,979,1064,1144,1235,1328,1426,1521,1621,1714,1807,1902,1993,2084,2170,2280,2391,2494,2605,2713,2820,2979,3078,14151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2303", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,62,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2302,2382"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4870,4976,5156,5286,5395,5566,5699,5820,6094,6289,6401,6586,6722,6882,7061,7134,7201", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,66,83", "endOffsets": "4971,5151,5281,5390,5561,5694,5815,5928,6284,6396,6581,6717,6877,7056,7129,7196,7280"}}]}]}
//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/models/woocommerce_wrappers/my_woo_customer.dart';
import '/app/services/woocommerce_service.dart';


/// Service for managing WooCommerce customer data
/// This service provides a reliable alternative to WordPress API endpoints
/// that are returning 404 errors
class WooCommerceCustomerService {
  static final WooCommerceCustomerService _instance = WooCommerceCustomerService._internal();
  factory WooCommerceCustomerService() => _instance;
  WooCommerceCustomerService._internal();

  final WooCommerceService _wooService = WooCommerceService();

  /// Get current logged-in customer data using WooCommerce API
  /// This replaces the failing wcCustomerInfo() WordPress API call
  Future<MyWooCustomer?> getCurrentCustomer() async {
    try {
      print('🌐 ===== WOOCOMMERCE CUSTOMER SERVICE: GET CURRENT CUSTOMER =====');
      
      // Get current user ID from storage
      int? currentUserId = await getCurrentUserId();
      if (currentUserId == null) {
        print('❌ No current user ID found');
        return null;
      }
      
      print('👤 Current User ID: $currentUserId');
      print('📍 API Call: WooCommerceService().getCustomer($currentUserId)');
      print('🔧 Expected Endpoint: /wp-json/wc/v3/customers/$currentUserId');
      print('===============================================');

      // Get customer data from WooCommerce API
      WooCustomer wooCustomer = await _wooService.getCustomer(currentUserId);

      print('🌐 ===== WOOCOMMERCE CUSTOMER SERVICE: SUCCESS =====');
      print('✅ Customer data retrieved successfully');
      print('👤 Customer ID: ${wooCustomer.id}');
      print('📧 Email: ${wooCustomer.email}');
      print('👤 Name: ${wooCustomer.firstName} ${wooCustomer.lastName}');
      print('📅 Date Created: ${wooCustomer.dateCreated}');
      print('===============================================');

      // Convert to our safe wrapper
      return MyWooCustomer.fromWooCustomer(wooCustomer);

    } catch (e) {
      print('🌐 ===== WOOCOMMERCE CUSTOMER SERVICE: ERROR =====');
      print('❌ FAILED - Exception during customer data fetch');
      print('📋 Exception Type: ${e.runtimeType}');
      print('📋 Exception Message: $e');
      print('===============================================');
      return null;
    }
  }

  /// Update customer billing and shipping addresses using WooCommerce API
  /// This replaces the failing wpUpdateUserInfo() WordPress API call
  Future<bool> updateCustomerAddresses({
    required int customerId,
    WooBilling? billing,
    WooShipping? shipping,
  }) async {
    try {
      print('🌐 ===== WOOCOMMERCE CUSTOMER SERVICE: UPDATE ADDRESSES =====');
      print('👤 Customer ID: $customerId');
      print('📍 API Call: WooCommerceService().updateCustomer()');
      print('🔧 Expected Endpoint: /wp-json/wc/v3/customers/$customerId');
      
      // Log billing data
      if (billing != null) {
        print('🏠 ===== BILLING ADDRESS DATA =====');
        print('🏠 First Name: ${billing.firstName}');
        print('🏠 Last Name: ${billing.lastName}');
        print('🏠 Company: ${billing.company}');
        print('🏠 Address 1: ${billing.address1}');
        print('🏠 Address 2: ${billing.address2}');
        print('🏠 City: ${billing.city}');
        print('🏠 State: ${billing.state}');
        print('🏠 Postcode: ${billing.postcode}');
        print('🏠 Country: ${billing.country}');
        print('🏠 Email: ${billing.email}');
        print('🏠 Phone: ${billing.phone}');
      }
      
      // Log shipping data
      if (shipping != null) {
        print('🚚 ===== SHIPPING ADDRESS DATA =====');
        print('🚚 First Name: ${shipping.firstName}');
        print('🚚 Last Name: ${shipping.lastName}');
        print('🚚 Company: ${shipping.company}');
        print('🚚 Address 1: ${shipping.address1}');
        print('🚚 Address 2: ${shipping.address2}');
        print('🚚 City: ${shipping.city}');
        print('🚚 State: ${shipping.state}');
        print('🚚 Postcode: ${shipping.postcode}');
        print('🚚 Country: ${shipping.country}');
      }
      print('===============================================');

      // Create customer update object
      WooCustomer customerUpdate = WooCustomer(
        id: customerId,
        billing: billing,
        shipping: shipping,
      );

      // Update customer via WooCommerce API
      WooCustomer updatedCustomer = await _wooService.updateCustomer(customerUpdate);

      print('🌐 ===== WOOCOMMERCE CUSTOMER SERVICE: UPDATE SUCCESS =====');
      print('✅ Customer addresses updated successfully');
      print('👤 Updated Customer ID: ${updatedCustomer.id}');
      print('===============================================');
      return true;
      
    } catch (e) {
      print('🌐 ===== WOOCOMMERCE CUSTOMER SERVICE: UPDATE ERROR =====');
      print('❌ FAILED - Exception during customer address update');
      print('📋 Exception Type: ${e.runtimeType}');
      print('📋 Exception Message: $e');
      
      // Check for specific error types
      if (e.toString().contains('DioException')) {
        print('📋 ERROR TYPE: DioException - Network/API error');
      } else if (e.toString().contains('FormatException')) {
        print('📋 ERROR TYPE: FormatException - JSON parsing error');
      }
      
      print('===============================================');
      return false;
    }
  }

  /// Get current user ID from storage
  /// This uses the same storage key as AuthService
  Future<int?> getCurrentUserId() async {
    try {
      // Use the same key as AuthService._keyUserId
      String? userIdString = await NyStorage.read('user_id');
      if (userIdString != null && userIdString.isNotEmpty) {
        return int.tryParse(userIdString);
      }

      print('❌ No user ID found in storage');
      return null;
    } catch (e) {
      print('❌ Error getting current user ID: $e');
      return null;
    }
  }

  /// Set current user ID in storage
  /// This should be called when user logs in successfully
  Future<void> setCurrentUserId(int userId) async {
    try {
      await NyStorage.save('current_user_id', userId);
      print('✅ Current user ID set: $userId');
    } catch (e) {
      print('❌ Error setting current user ID: $e');
    }
  }

  /// Clear current user data
  /// This should be called when user logs out
  Future<void> clearCurrentUser() async {
    try {
      await NyStorage.delete('current_user_id');
      print('✅ Current user data cleared');
    } catch (e) {
      print('❌ Error clearing current user data: $e');
    }
  }
}

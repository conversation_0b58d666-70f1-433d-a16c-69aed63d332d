//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';

import '/app/models/billing_details.dart';
import '/app/models/customer_address.dart';
import '/app/models/customer_country.dart';

import '/app/models/checkout_session.dart';
import '/app/services/woocommerce_customer_service.dart';
import '/app/models/woocommerce_wrappers/my_woo_customer.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/app_loader_widget.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/customer_address_input.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/switch_address_tab.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:wp_json_api/models/responses/wp_user_info_response.dart';

import 'package:wp_json_api/wp_json_api.dart';
import 'package:validated/validated.dart' as validate;

class AccountShippingDetailsPage extends NyStatefulWidget {
  static RouteView path =
      ("/account-shipping-details", (_) => AccountShippingDetailsPage());

  AccountShippingDetailsPage({super.key})
      : super(child: () => _AccountShippingDetailsPageState());
}

class _AccountShippingDetailsPageState
    extends NyPage<AccountShippingDetailsPage> {
  _AccountShippingDetailsPageState();

  int activeTabIndex = 0;

  // TEXT CONTROLLERS
  final TextEditingController
      // billing
      _txtBillingFirstName = TextEditingController(),
      _txtBillingLastName = TextEditingController(),
      _txtBillingAddressLine = TextEditingController(),
      _txtBillingCity = TextEditingController(),
      _txtBillingPostalCode = TextEditingController(),
      _txtBillingEmailAddress = TextEditingController(),
      _txtBillingPhoneNumber = TextEditingController(),
      // shipping
      _txtShippingFirstName = TextEditingController(),
      _txtShippingLastName = TextEditingController(),
      _txtShippingAddressLine = TextEditingController(),
      _txtShippingCity = TextEditingController(),
      _txtShippingPostalCode = TextEditingController();

  CustomerCountry? _billingCountry, _shippingCountry;
  MyWooCustomer? _currentCustomer;

  Widget? activeTab;

  Widget tabShippingDetails() => CustomerAddressInput(
      txtControllerFirstName: _txtShippingFirstName,
      txtControllerLastName: _txtShippingLastName,
      txtControllerAddressLine: _txtShippingAddressLine,
      txtControllerCity: _txtShippingCity,
      txtControllerPostalCode: _txtShippingPostalCode,
      customerCountry: _shippingCountry,
      onTapCountry: () => _navigateToSelectCountry(type: "shipping"));

  Widget tabBillingDetails() => CustomerAddressInput(
        txtControllerFirstName: _txtBillingFirstName,
        txtControllerLastName: _txtBillingLastName,
        txtControllerAddressLine: _txtBillingAddressLine,
        txtControllerCity: _txtBillingCity,
        txtControllerPostalCode: _txtBillingPostalCode,
        txtControllerEmailAddress: _txtBillingEmailAddress,
        txtControllerPhoneNumber: _txtBillingPhoneNumber,
        customerCountry: _billingCountry,
        onTapCountry: () => _navigateToSelectCountry(type: "billing"),
      );

  @override
  get init => () async {
        await _fetchUserDetails();
        _setDefaultLibyaCountry();
      };

  /// Set Libya as default country for both billing and shipping
  void _setDefaultLibyaCountry() {
    // Create Libya country object
    final libyaCountry = CustomerCountry(
      countryCode: 'LY',
      name: 'Libya',
    );

    // Set Libya as default for both billing and shipping if not already set
    if (_billingCountry == null) {
      _billingCountry = libyaCountry;
      print('✅ Set Libya as default billing country');
    }

    if (_shippingCountry == null) {
      _shippingCountry = libyaCountry;
      print('✅ Set Libya as default shipping country');
    }
  }

  _setFieldsFromCustomerAddress(CustomerAddress? customerAddress,
      {required String type}) {
    assert(type != "");
    if (customerAddress == null) {
      return;
    }
    _setFields(
      firstName: customerAddress.firstName,
      lastName: customerAddress.lastName,
      addressLine: customerAddress.addressLine,
      city: customerAddress.city,
      postalCode: customerAddress.postalCode,
      emailAddress: customerAddress.emailAddress,
      phoneNumber: customerAddress.phoneNumber,
      customerCountry: customerAddress.customerCountry,
      type: type,
    );
  }

  _setFields(
      {required String? firstName,
      required String? lastName,
      required String? addressLine,
      required String? city,
      required String? postalCode,
      required String? emailAddress,
      required String? phoneNumber,
      required CustomerCountry? customerCountry,
      String? type}) {
    if (type == "billing") {
      _txtBillingFirstName.text = firstName ?? "";
      _txtBillingLastName.text = lastName ?? "";
      _txtBillingAddressLine.text = addressLine ?? "";
      _txtBillingCity.text = city ?? "";
      _txtBillingPostalCode.text = postalCode ?? "";
      _txtBillingPhoneNumber.text = phoneNumber ?? "";
      _txtBillingEmailAddress.text = emailAddress ?? "";
      _billingCountry = customerCountry;
    } else if (type == "shipping") {
      _txtShippingFirstName.text = firstName ?? "";
      _txtShippingLastName.text = lastName ?? "";
      _txtShippingAddressLine.text = addressLine ?? "";
      _txtShippingCity.text = city ?? "";
      _txtShippingPostalCode.text = postalCode ?? "";
      _shippingCountry = customerCountry;
    }
  }

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: Text(
          trans("Billing & Shipping Details"),
        ),
        centerTitle: true,
      ),
      body: SafeAreaWidget(
        child: isLoading()
            ? AppLoaderWidget()
            : GestureDetector(
                onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            margin: EdgeInsets.symmetric(vertical: 0),
                            height: 60,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: <Widget>[
                                Padding(
                                  padding: EdgeInsets.symmetric(vertical: 4),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceAround,
                                    children: <Widget>[
                                      SwitchAddressTab(
                                          title: trans("Billing Details"),
                                          currentTabIndex: activeTabIndex,
                                          type: "billing",
                                          onTapAction: () => setState(() {
                                                activeTabIndex = 0;
                                                activeTab = tabBillingDetails();
                                              })),
                                      SwitchAddressTab(
                                          title: trans("Shipping Address"),
                                          currentTabIndex: activeTabIndex,
                                          type: "shipping",
                                          onTapAction: () => setState(() {
                                                activeTabIndex = 1;
                                                activeTab =
                                                    tabShippingDetails();
                                              })),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Flexible(
                            child: Container(
                                decoration: BoxDecoration(
                                  color: ThemeColor.get(context)
                                      .backgroundContainer,
                                  borderRadius: BorderRadius.circular(10),
                                  boxShadow: (Theme.of(context).brightness ==
                                          Brightness.light)
                                      ? wsBoxShadow()
                                      : null,
                                ),
                                padding:
                                    EdgeInsets.only(left: 8, right: 8, top: 8),
                                margin: EdgeInsets.only(top: 8),
                                child: SingleChildScrollView(
                                  child: (activeTab ?? tabBillingDetails()),
                                )),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(top: 16),
                      height: 160,
                      child: Column(
                        children: <Widget>[
                          PrimaryButton(
                            title: trans("USE DETAILS"),
                            action: _useDetailsTapped,
                            isLoading: isLocked('update_details'),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  _useDetailsTapped() async {
    lockRelease('update_details', perform: () async {
      // Billing email is required for Stripe
      String billingEmail = _txtBillingEmailAddress.text;
      if (billingEmail.isNotEmpty && !validate.isEmail(billingEmail)) {
        showToast(
          title: trans("Oops"),
          description: trans("Please enter a valid shipping email"),
          style: ToastNotificationStyleType.warning,
        );
        return;
      }

      CustomerAddress userBillingAddress = _setCustomerAddress(
            firstName: _txtBillingFirstName.text,
            lastName: _txtBillingLastName.text,
            addressLine: _txtBillingAddressLine.text,
            city: _txtBillingCity.text,
            postalCode: _txtBillingPostalCode.text,
            phoneNumber: _txtBillingPhoneNumber.text,
            emailAddress: _txtBillingEmailAddress.text.trim(),
            customerCountry: _billingCountry,
          ),
          userShippingAddress = _setCustomerAddress(
            firstName: _txtShippingFirstName.text,
            lastName: _txtShippingLastName.text,
            addressLine: _txtShippingAddressLine.text,
            city: _txtShippingCity.text,
            postalCode: _txtShippingPostalCode.text,
            customerCountry: _shippingCountry,
          );

      print('🌐 ===== SHIPPING DETAILS: UPDATE ADDRESS API CALL (WOOCOMMERCE) =====');
      print('📍 API Call: WooCommerceCustomerService().updateCustomerAddresses()');
      print('🔧 Expected Endpoint: WooCommerce Customer Update API');

      print('🏠 ===== BILLING ADDRESS DATA =====');
      print('🏠 First Name: ${userBillingAddress.firstName}');
      print('🏠 Last Name: ${userBillingAddress.lastName}');
      print('🏠 Address: ${userBillingAddress.addressLine}');
      print('🏠 City: ${userBillingAddress.city}');
      print('🏠 Postal Code: ${userBillingAddress.postalCode}');
      print('🏠 Phone: ${userBillingAddress.phoneNumber}');
      print('🏠 Email: ${userBillingAddress.emailAddress}');
      print('🏠 Country: ${userBillingAddress.customerCountry?.countryCode}');

      print('🚚 ===== SHIPPING ADDRESS DATA =====');
      print('🚚 First Name: ${userShippingAddress.firstName}');
      print('🚚 Last Name: ${userShippingAddress.lastName}');
      print('🚚 Address: ${userShippingAddress.addressLine}');
      print('🚚 City: ${userShippingAddress.city}');
      print('🚚 Postal Code: ${userShippingAddress.postalCode}');
      print('🚚 Country: ${userShippingAddress.customerCountry?.countryCode}');
      print('===============================================');

      bool updateSuccess = false;
      try {
        WooCommerceCustomerService customerService = WooCommerceCustomerService();

        // Get current customer ID
        String? userIdString = await getCurrentUserId();
        if (userIdString == null) {
          throw Exception('No customer ID found');
        }
        int customerId = int.parse(userIdString);

        // Convert CustomerAddress to WooBilling and WooShipping
        WooBilling billing = WooBilling(
          firstName: userBillingAddress.firstName,
          lastName: userBillingAddress.lastName,
          company: '',
          address1: userBillingAddress.addressLine,
          address2: '',
          city: userBillingAddress.city,
          state: '',
          postcode: userBillingAddress.postalCode,
          country: userBillingAddress.customerCountry?.countryCode ?? 'LY',
          email: userBillingAddress.emailAddress,
          phone: userBillingAddress.phoneNumber,
        );

        WooShipping shipping = WooShipping(
          firstName: userShippingAddress.firstName,
          lastName: userShippingAddress.lastName,
          company: '',
          address1: userShippingAddress.addressLine,
          address2: '',
          city: userShippingAddress.city,
          state: '',
          postcode: userShippingAddress.postalCode,
          country: userShippingAddress.customerCountry?.countryCode ?? 'LY',
        );

        updateSuccess = await customerService.updateCustomerAddresses(
          customerId: customerId,
          billing: billing,
          shipping: shipping,
        );

        print('🌐 ===== SHIPPING DETAILS: UPDATE RESPONSE =====');
        print('✅ SUCCESS - Update API call completed');
        print('📊 Update Success: $updateSuccess');
        print('===============================================');

      } on Exception catch (e) {
        print('🌐 ===== SHIPPING DETAILS: UPDATE ERROR =====');
        print('❌ FAILED - Exception during address update');
        print('📋 Exception Type: ${e.runtimeType}');
        print('📋 Exception Message: $e');

        // Try to get more details if it's a DioException
        if (e.toString().contains('DioException')) {
          print('📋 This appears to be a DioException - network/API error');
        }

        print('📋 Stack Trace: ${StackTrace.current}');
        print('===============================================');

        showToast(
            title: trans("Oops!"),
            description: trans("Something went wrong"),
            style: ToastNotificationStyleType.danger);
      }

      if (updateSuccess) {
        print('🌐 ===== SHIPPING DETAILS: POST-UPDATE REFRESH =====');
        print('✅ Address update successful, refreshing customer data');
        print('===============================================');

        // Refresh customer data to ensure UI shows updated information
        try {
          await _refreshCustomerData();
          print('✅ Customer data refreshed successfully');

          // Update session storage with new address data
          await _updateSessionStorage();
          print('✅ Session storage updated successfully');
        } catch (e) {
          print('⚠️ Warning: Failed to refresh customer data: $e');
          // Continue anyway since the update was successful
        }

        showToast(
            title: trans("Success"),
            description: trans("Account updated"),
            style: ToastNotificationStyleType.success);
        pop();
      }
    });
  }

  /// Refresh customer data after successful address update
  /// This ensures the app's local state is synchronized with the server
  Future<void> _refreshCustomerData() async {
    print('🌐 ===== SHIPPING DETAILS: REFRESHING CUSTOMER DATA =====');
    print('📍 Re-fetching customer data from WooCommerce API');
    print('===============================================');

    try {
      WooCommerceCustomerService customerService = WooCommerceCustomerService();
      MyWooCustomer? refreshedCustomer = await customerService.getCurrentCustomer();

      if (refreshedCustomer != null) {
        _currentCustomer = refreshedCustomer;

        print('✅ Customer data refreshed successfully');
        print('🏠 Updated Billing: ${refreshedCustomer.billing?.address1 ?? "N/A"}');
        print('🚚 Updated Shipping: ${refreshedCustomer.shipping?.address1 ?? "N/A"}');

        // Update form fields with refreshed data
        if (refreshedCustomer.billing != null) {
          _setFieldsFromWooCommerceBilling(refreshedCustomer.billing!);
        }
        if (refreshedCustomer.shipping != null) {
          _setFieldsFromWooCommerceShipping(refreshedCustomer.shipping!);
        }

        // Update UI
        if (mounted) {
          setState(() {});
        }

        print('✅ Form fields updated with refreshed data');
      } else {
        print('⚠️ Warning: Refreshed customer data is null');
      }
    } catch (e) {
      print('❌ Error refreshing customer data: $e');
      rethrow;
    }

    print('===============================================');
  }

  /// Update session storage with the latest address data
  /// This ensures CheckoutSession and other parts of the app have current data
  Future<void> _updateSessionStorage() async {
    print('🌐 ===== SHIPPING DETAILS: UPDATING SESSION STORAGE =====');

    if (_currentCustomer == null) {
      print('⚠️ No current customer data to update session storage');
      return;
    }

    try {
      // Update CheckoutSession with current address data
      final checkoutSession = CheckoutSession.getInstance;

      if (checkoutSession.billingDetails == null) {
        checkoutSession.billingDetails = BillingDetails();
      }

      // Convert WooCommerce billing to CustomerAddress
      if (_currentCustomer!.billing != null) {
        CustomerAddress billingAddress = CustomerAddress();
        final billing = _currentCustomer!.billing!;

        billingAddress.firstName = billing.firstName;
        billingAddress.lastName = billing.lastName;
        billingAddress.addressLine = billing.address1;
        billingAddress.city = billing.city;
        billingAddress.postalCode = billing.postcode;
        billingAddress.phoneNumber = billing.phone;
        billingAddress.emailAddress = billing.email;

        // Set country if available
        if (billing.country != null) {
          billingAddress.customerCountry = CustomerCountry(
            countryCode: billing.country,
            name: billing.country == 'LY' ? 'Libya' : billing.country,
          );
        }

        checkoutSession.billingDetails!.billingAddress = billingAddress;
        await checkoutSession.saveBillingAddress();
        print('✅ Billing address saved to session storage');
      }

      // Convert WooCommerce shipping to CustomerAddress
      if (_currentCustomer!.shipping != null) {
        CustomerAddress shippingAddress = CustomerAddress();
        final shipping = _currentCustomer!.shipping!;

        shippingAddress.firstName = shipping.firstName;
        shippingAddress.lastName = shipping.lastName;
        shippingAddress.addressLine = shipping.address1;
        shippingAddress.city = shipping.city;
        shippingAddress.postalCode = shipping.postcode;

        // Set country if available
        if (shipping.country != null) {
          shippingAddress.customerCountry = CustomerCountry(
            countryCode: shipping.country,
            name: shipping.country == 'LY' ? 'Libya' : shipping.country,
          );
        }

        checkoutSession.billingDetails!.shippingAddress = shippingAddress;
        await checkoutSession.saveShippingAddress();
        print('✅ Shipping address saved to session storage');
      }

      print('✅ Session storage updated successfully');
    } catch (e) {
      print('❌ Error updating session storage: $e');
      rethrow;
    }

    print('===============================================');
  }

  CustomerAddress _setCustomerAddress(
      {required String firstName,
      required String lastName,
      required String addressLine,
      required String city,
      required String postalCode,
      String? emailAddress,
      String? phoneNumber,
      required CustomerCountry? customerCountry}) {
    CustomerAddress customerShippingAddress = CustomerAddress();
    customerShippingAddress.firstName = firstName;
    customerShippingAddress.lastName = lastName;
    customerShippingAddress.addressLine = addressLine;
    customerShippingAddress.city = city;
    customerShippingAddress.postalCode = postalCode;
    if (phoneNumber != null && phoneNumber != "") {
      customerShippingAddress.phoneNumber = phoneNumber;
    }
    customerShippingAddress.customerCountry = customerCountry;
    customerShippingAddress.emailAddress = emailAddress;
    return customerShippingAddress;
  }

  _navigateToSelectCountry({required String type}) {
    // Force Libya only - prevent country selection
    showToastNotification(
      context,
      title: "معلومات", // "Information" in Arabic
      description: "الدولة محددة مسبقاً: ليبيا", // "Country is pre-set: Libya" in Arabic
      icon: Icons.info,
    );

    // Ensure Libya is set
    final libyaCountry = CustomerCountry(
      countryCode: 'LY',
      name: 'Libya',
    );

    if (type == "billing") {
      _billingCountry = libyaCountry;
      activeTab = tabBillingDetails();
    } else if (type == "shipping") {
      _shippingCountry = libyaCountry;
      activeTab = tabShippingDetails();
    }
    setState(() {});
  }

  _fetchUserDetails() async {
    print('🌐 ===== SHIPPING DETAILS: FETCHING USER DETAILS (NEW WOOCOMMERCE SERVICE) =====');
    print('📍 Using WooCommerceCustomerService instead of failing WordPress API');
    print('🔧 Expected Endpoint: WooCommerce Customer API');
    print('===============================================');

    WPUserInfoResponse? wpUserInfoResponse;

    // Try the new WooCommerce customer service first
    try {
      WooCommerceCustomerService customerService = WooCommerceCustomerService();
      _currentCustomer = await customerService.getCurrentCustomer();

      if (_currentCustomer != null) {
        print('🌐 ===== SHIPPING DETAILS: WOOCOMMERCE CUSTOMER SUCCESS =====');
        print('✅ SUCCESS - Customer data retrieved via WooCommerce API');
        print('👤 Customer ID: ${_currentCustomer!.id}');
        print('📧 Email: ${_currentCustomer!.email}');
        print('👤 First Name: ${_currentCustomer!.firstName}');
        print('👤 Last Name: ${_currentCustomer!.lastName}');
        print('🏠 Has Billing Address: ${_currentCustomer!.billing != null}');
        print('🚚 Has Shipping Address: ${_currentCustomer!.shipping != null}');
        print('===============================================');

        // Process WooCommerce customer data directly
        print('🌐 ===== SHIPPING DETAILS: PROCESSING WOOCOMMERCE DATA =====');
        print('✅ Processing WooCommerce customer data directly');
        print('===============================================');

        // Set form fields directly from WooCommerce customer data
        if (_currentCustomer!.billing != null) {
          _setFieldsFromWooCommerceBilling(_currentCustomer!.billing!);
        }
        if (_currentCustomer!.shipping != null) {
          _setFieldsFromWooCommerceShipping(_currentCustomer!.shipping!);
        }

        // Ensure UI is updated with the pre-filled data
        if (mounted) {
          setState(() {});
        }

        print('✅ Form fields pre-filled and UI updated');
        return; // Exit early since we processed the data
      } else {
        print('🌐 ===== SHIPPING DETAILS: WOOCOMMERCE SERVICE FAILED =====');
        print('❌ WooCommerce service returned null customer');
        print('🔄 Will try WordPress API fallback');
        print('===============================================');

        // Fallback to original WordPress API
        wpUserInfoResponse =
            await WPJsonAPI.instance.api((request) => request.wpGetUserInfo());
      }
    } catch (e) {
      print('🌐 ===== SHIPPING DETAILS: WOOCOMMERCE SERVICE ERROR =====');
      print('❌ FAILED - Exception during WooCommerce customer fetch');
      print('📋 Exception Type: ${e.runtimeType}');
      print('📋 Exception Message: $e');
      print('🔄 Will try WordPress API fallback');
      print('===============================================');

      // Fallback to original WordPress API
      try {
        wpUserInfoResponse =
            await WPJsonAPI.instance.api((request) => request.wpGetUserInfo());

        print('🌐 ===== SHIPPING DETAILS: USER DETAILS RESPONSE =====');
        print('✅ SUCCESS - User details retrieved');
        print('📊 Response Status: ${wpUserInfoResponse?.status}');
        print('📦 Response Data Type: ${wpUserInfoResponse?.data.runtimeType}');
        print('📦 Response Data: ${wpUserInfoResponse?.data}');
        print('===============================================');

      } catch (fallbackError) {
        print('🌐 ===== SHIPPING DETAILS: FALLBACK ALSO FAILED =====');
        print('❌ Both WooCommerce and WordPress APIs failed');
        print('📋 Fallback Error: $fallbackError');
        print('===============================================');
      }
    }

    // Handle the final response processing
    if (wpUserInfoResponse != null) {
      print('🌐 ===== SHIPPING DETAILS: PROCESSING RESPONSE =====');
      print('📊 Final Response Status: ${wpUserInfoResponse.status}');
      print('===============================================');
    } else {
      print('🌐 ===== SHIPPING DETAILS: NO RESPONSE AVAILABLE =====');
      print('❌ Both WooCommerce and WordPress APIs failed to provide data');
      print('===============================================');

      showToast(
        title: trans("Oops!"),
        description: trans("Something went wrong"),
        style: ToastNotificationStyleType.danger,
      );
      return;
    }

    // Process the response if we have one
    if (wpUserInfoResponse.status == 200) {
      print('🌐 ===== SHIPPING DETAILS: PROCESSING BILLING DETAILS =====');
      print('✅ Processing billing details from user info response');
      print('===============================================');

      BillingDetails billingDetails =
          await billingDetailsFromWpUserInfoResponse(wpUserInfoResponse);

      print('🌐 ===== SHIPPING DETAILS: SETTING FORM FIELDS =====');
      print('✅ Setting shipping and billing address fields');
      print('===============================================');

      _setFieldsFromCustomerAddress(billingDetails.shippingAddress,
          type: "shipping");
      _setFieldsFromCustomerAddress(billingDetails.billingAddress,
          type: "billing");
    } else {
      print('🌐 ===== SHIPPING DETAILS: NON-200 STATUS OR NULL RESPONSE =====');
      print('❌ Response status is not 200 or response is null');
      print('📊 Status: ${wpUserInfoResponse.status}');
      print('📦 Response: $wpUserInfoResponse');
      print('===============================================');
    }
  }

  /// Set form fields from WooCommerce billing data
  _setFieldsFromWooCommerceBilling(WooBilling billing) {
    print('🌐 ===== SETTING BILLING FIELDS FROM WOOCOMMERCE =====');
    print('✅ Setting billing address fields from WooCommerce data');
    print('===============================================');

    _txtBillingFirstName.text = billing.firstName ?? '';
    _txtBillingLastName.text = billing.lastName ?? '';
    _txtBillingAddressLine.text = billing.address1 ?? '';
    _txtBillingCity.text = billing.city ?? '';
    _txtBillingPostalCode.text = billing.postcode ?? '';
    _txtBillingEmailAddress.text = billing.email ?? '';
    _txtBillingPhoneNumber.text = billing.phone ?? '';

    // Set country if available
    if (billing.country != null && billing.country!.isNotEmpty) {
      _billingCountry = CustomerCountry(
        countryCode: billing.country!,
        name: billing.country!, // You might want to map this to full country name
      );
    }
  }

  /// Set form fields from WooCommerce shipping data
  _setFieldsFromWooCommerceShipping(WooShipping shipping) {
    print('🌐 ===== SETTING SHIPPING FIELDS FROM WOOCOMMERCE =====');
    print('✅ Setting shipping address fields from WooCommerce data');
    print('===============================================');

    _txtShippingFirstName.text = shipping.firstName ?? '';
    _txtShippingLastName.text = shipping.lastName ?? '';
    _txtShippingAddressLine.text = shipping.address1 ?? '';
    _txtShippingCity.text = shipping.city ?? '';
    _txtShippingPostalCode.text = shipping.postcode ?? '';

    // Set country if available
    if (shipping.country != null && shipping.country!.isNotEmpty) {
      _shippingCountry = CustomerCountry(
        countryCode: shipping.country!,
        name: shipping.country!, // You might want to map this to full country name
      );
    }
  }
}

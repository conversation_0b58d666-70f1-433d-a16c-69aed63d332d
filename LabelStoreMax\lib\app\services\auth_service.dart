//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/services/woocommerce_service.dart';
import '/app/services/jwt_auth_service.dart';

/// Enhanced Authentication Service for Phase 2.2
/// Handles WooCommerce customer authentication, session management, and user state
class AuthService {
  static final AuthService _instance = AuthService._internal();
  
  factory AuthService() {
    return _instance;
  }
  
  AuthService._internal();

  // Storage keys for user session data
  static const String _keyUserLoggedIn = 'user_logged_in';
  static const String _keyUserId = 'user_id';
  static const String _keyUserEmail = 'user_email';
  static const String _keyUserFirstName = 'user_first_name';
  static const String _keyUserLastName = 'user_last_name';
  static const String _keyUserPhoneNumber = 'user_phone_number';
  static const String _keyUserCity = 'user_city';
  static const String _keyUserCityArabic = 'user_city_arabic';
  static const String _keyUserDeliveryCost = 'user_delivery_cost';

  /// Authenticate user with email and password using JWT and WooCommerce API
  Future<AuthResult> login(String email, String password) async {
    try {
      // Validate input
      if (email.isEmpty || password.isEmpty) {
        return AuthResult.failure('Email and password are required');
      }

      // Validate email format
      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
        return AuthResult.failure('Please enter a valid email address');
      }

      // Validate password length
      if (password.length < 6) {
        return AuthResult.failure('Password must be at least 6 characters');
      }

      print('=== Attempting JWT Authentication ===');

      // Try JWT authentication first
      final jwtService = JWTAuthService();
      jwtService.initialize();

      final jwtResult = await jwtService.login(email: email, password: password);

      if (jwtResult.success && jwtResult.user != null) {
        print('✅ JWT Authentication successful');

        // Try to get WooCommerce customer data
        List<WooCustomer> customers = await WooCommerceService().getCustomersByEmail(email);
        WooCustomer? customer = customers.isNotEmpty ? customers.first : null;

        if (customer != null) {
          // Store user session data
          await _storeUserSession(customer);
          return AuthResult.success(customer);
        } else {
          // Create WooCommerce customer if not exists
          customer = await _createWooCustomerFromJWT(jwtResult.user!, email, password);
          if (customer != null) {
            await _storeUserSession(customer);
            return AuthResult.success(customer);
          }
        }
      } else {
        print('⚠️ JWT Authentication failed: ${jwtResult.message}');
        return AuthResult.failure('Invalid login credentials');
      }

      // This should never be reached, but added for safety
      return AuthResult.failure('Authentication failed');
    } catch (e) {
      print('Login error: $e');
      return AuthResult.failure('Something went wrong, please try again');
    }
  }

  /// Register new user with pure WooCommerce only
  Future<AuthResult> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String phoneNumber,
    required String city,
    required String cityArabic,
    required double deliveryCost,
  }) async {
    try {
      // Validate input
      if (email.isEmpty || password.isEmpty || firstName.isEmpty || lastName.isEmpty) {
        return AuthResult.failure('All fields are required');
      }

      // Validate email format
      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
        return AuthResult.failure('Please enter a valid email address');
      }

      // Validate phone number format
      if (!phoneNumber.startsWith('+218')) {
        return AuthResult.failure('Phone number must start with +218');
      }

      print('=== Pure WooCommerce Customer Registration ===');

      // Create WooCommerce customer directly (no JWT/WordPress user creation)
      WooCustomer newCustomer = WooCustomer(
        email: email.toLowerCase(),
        firstName: firstName,
        lastName: lastName,
        username: email.split('@')[0], // Use email prefix as username
        password: password, // Include password for customer creation
      );

      // LOG COMPREHENSIVE WOOCOMMERCE CUSTOMER CREATION REQUEST
      print('🌐 ===== WOOCOMMERCE CUSTOMER CREATION REQUEST =====');
      print('📍 FULL URL: https://velvete.ly/wp-json/wc/v3/customers');
      print('🔧 HTTP METHOD: POST');
      print('🔑 AUTHENTICATION: WooCommerce Consumer Key/Secret');
      print('📋 HEADERS: Content-Type: application/json');
      print('📦 COMPLETE JSON REQUEST PAYLOAD:');
      print('   {');
      print('     "email": "${newCustomer.email}",');
      print('     "first_name": "${newCustomer.firstName}",');
      print('     "last_name": "${newCustomer.lastName}",');
      print('     "username": "${newCustomer.username}",');
      print('     "password": "${password.replaceAll(RegExp(r'.'), '*')}", // Masked for security');
      print('     "billing": {},');
      print('     "shipping": {},');
      print('     "meta_data": []');
      print('   }');
      print('====================================================');

      WooCustomer createdCustomer = await WooCommerceService().createCustomer(newCustomer);

      // LOG COMPREHENSIVE WOOCOMMERCE CUSTOMER CREATION RESPONSE
      print('🌐 ===== WOOCOMMERCE CUSTOMER CREATION RESPONSE =====');
      print('✅ SUCCESS - WooCommerce Customer Created Successfully');
      print('📊 Customer ID: ${createdCustomer.id}');
      print('📧 Email: ${createdCustomer.email}');
      print('👤 Full Name: ${createdCustomer.firstName} ${createdCustomer.lastName}');
      print('🔑 Username: ${createdCustomer.username}');
      print('📅 Date Created: ${createdCustomer.dateCreated}');
      print('🔗 Avatar URL: ${createdCustomer.avatarUrl}');
      print('📋 Role: ${createdCustomer.role}');
      print('===================================================');

      if (createdCustomer.id == null) {
        return AuthResult.failure('Failed to create account. Email may already exist.');
      }

      // Store user session with Libyan market data
      await _storeUserSession(createdCustomer,
        phoneNumber: phoneNumber,
        city: city,
        cityArabic: cityArabic,
        deliveryCost: deliveryCost,
      );

      return AuthResult.success(createdCustomer);
    } catch (e) {
      print('🌐 ❌ WooCommerce Registration Error: $e');
      return AuthResult.failure('Something went wrong, please try again');
    }
  }

  /// Check if user is currently logged in
  Future<bool> isLoggedIn() async {
    return await NyStorage.read(_keyUserLoggedIn) ?? false;
  }

  /// Get current user data
  Future<UserSession?> getCurrentUser() async {
    if (!(await isLoggedIn())) {
      return null;
    }

    return UserSession(
      id: await NyStorage.read(_keyUserId),
      email: await NyStorage.read(_keyUserEmail),
      firstName: await NyStorage.read(_keyUserFirstName),
      lastName: await NyStorage.read(_keyUserLastName),
      phoneNumber: await NyStorage.read(_keyUserPhoneNumber),
      city: await NyStorage.read(_keyUserCity),
      cityArabic: await NyStorage.read(_keyUserCityArabic),
      deliveryCost: double.tryParse(await NyStorage.read(_keyUserDeliveryCost) ?? '0') ?? 0.0,
    );
  }

  /// Logout user and clear session
  Future<void> logout() async {
    await NyStorage.delete(_keyUserLoggedIn);
    await NyStorage.delete(_keyUserId);
    await NyStorage.delete(_keyUserEmail);
    await NyStorage.delete(_keyUserFirstName);
    await NyStorage.delete(_keyUserLastName);
    await NyStorage.delete(_keyUserPhoneNumber);
    await NyStorage.delete(_keyUserCity);
    await NyStorage.delete(_keyUserCityArabic);
    await NyStorage.delete(_keyUserDeliveryCost);
  }

  /// Store user session data
  Future<void> _storeUserSession(WooCustomer customer, {
    String? phoneNumber,
    String? city,
    String? cityArabic,
    double? deliveryCost,
  }) async {
    await NyStorage.save(_keyUserLoggedIn, true);
    await NyStorage.save(_keyUserId, customer.id.toString());
    await NyStorage.save(_keyUserEmail, customer.email ?? '');
    await NyStorage.save(_keyUserFirstName, customer.firstName ?? '');
    await NyStorage.save(_keyUserLastName, customer.lastName ?? '');
    
    // Store additional Libyan market data if provided
    if (phoneNumber != null) {
      await NyStorage.save(_keyUserPhoneNumber, phoneNumber);
    }
    if (city != null) {
      await NyStorage.save(_keyUserCity, city);
    }
    if (cityArabic != null) {
      await NyStorage.save(_keyUserCityArabic, cityArabic);
    }
    if (deliveryCost != null) {
      await NyStorage.save(_keyUserDeliveryCost, deliveryCost.toString());
    }
  }

  /// Update user profile
  Future<AuthResult> updateProfile({
    required String firstName,
    required String lastName,
    String? phoneNumber,
    String? city,
    String? cityArabic,
    double? deliveryCost,
  }) async {
    try {
      UserSession? currentUser = await getCurrentUser();
      if (currentUser == null) {
        return AuthResult.failure('User not logged in');
      }

      // Update customer via WooCommerce API
      WooCustomer updatedCustomer = WooCustomer(
        id: int.tryParse(currentUser.id ?? ''),
        email: currentUser.email,
        firstName: firstName,
        lastName: lastName,
      );

      WooCustomer result = await WooCommerceService().updateCustomer(updatedCustomer);

      // Update was successful if we get here without exception

      // Update local session
      await _storeUserSession(result,
        phoneNumber: phoneNumber ?? currentUser.phoneNumber,
        city: city ?? currentUser.city,
        cityArabic: cityArabic ?? currentUser.cityArabic,
        deliveryCost: deliveryCost ?? currentUser.deliveryCost,
      );
      
      return AuthResult.success(result);
    } catch (e) {
      print('Update profile error: $e');
      return AuthResult.failure('Something went wrong, please try again');
    }
  }


  /// Create WooCommerce customer from JWT user data
  Future<WooCustomer?> _createWooCustomerFromJWT(JWTUser jwtUser, String email, String password) async {
    try {
      final wooService = WooCommerceService();

      WooCustomer newCustomer = WooCustomer(
        email: jwtUser.email,
        firstName: jwtUser.firstName,
        lastName: jwtUser.lastName,
        username: email.split('@')[0], // Extract username from email
        password: password, // Include password for customer creation
      );

      WooCustomer createdCustomer = await wooService.createCustomer(newCustomer);
      print('✅ WooCommerce customer created from JWT user');
      return createdCustomer;
    } catch (e) {
      print('❌ Error creating WooCommerce customer: $e');
      return null;
    }
  }
}

/// Authentication result wrapper
class AuthResult {
  final bool success;
  final String? message;
  final WooCustomer? customer;

  AuthResult._(this.success, this.message, this.customer);

  factory AuthResult.success(WooCustomer customer) {
    return AuthResult._(true, null, customer);
  }

  factory AuthResult.failure(String message) {
    return AuthResult._(false, message, null);
  }
}

/// User session data model
class UserSession {
  final String? id;
  final String? email;
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;
  final String? city;
  final String? cityArabic;
  final double deliveryCost;

  UserSession({
    this.id,
    this.email,
    this.firstName,
    this.lastName,
    this.phoneNumber,
    this.city,
    this.cityArabic,
    this.deliveryCost = 0.0,
  });

  String get fullName => '${firstName ?? ''} ${lastName ?? ''}'.trim();
  String get displayCity => cityArabic ?? city ?? '';
  String get formattedDeliveryCost => '${deliveryCost.toStringAsFixed(0)} د.ل';
}

{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "8010,45541", "endColumns": "60,78", "endOffsets": "8066,45615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "45370,45454", "endColumns": "83,86", "endOffsets": "45449,45536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4684,4794,4949,5085,5190,5337,5467,5594,5847,6019,6126,6283,6417,6562,6729,6791,6855", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "4789,4944,5080,5185,5332,5462,5589,5695,6014,6121,6278,6412,6557,6724,6786,6850,6930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,193,255,338,399,474,531,610,682,745,807", "endColumns": "77,59,61,82,60,74,56,78,71,62,61,68", "endOffsets": "128,188,250,333,394,469,526,605,677,740,802,871"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15602,16404,16548,16610,16693,16974,17514,17704,17971,18301,18636,18928", "endColumns": "77,59,61,82,60,74,56,78,71,62,61,68", "endOffsets": "15675,16459,16605,16688,16749,17044,17566,17778,18038,18359,18693,18992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,340,415,492,591,682,778,890,972,1032,1096,1187,1264,1325,1416,1479,1542,1601,1670,1733,1787,1895,1953,2015,2069,2142,2263,2347,2427,2526,2610,2701,2841,2918,2994,3125,3212,3288,3341,3395,3461,3531,3608,3679,3759,3830,3905,3983,4054,4155,4240,4329,4424,4517,4589,4661,4757,4809,4895,4962,5046,5136,5198,5262,5325,5395,5489,5591,5680,5780,5837,5895,5974,6058,6133", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,74,74,76,98,90,95,111,81,59,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,79,98,83,90,139,76,75,130,86,75,52,53,65,69,76,70,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78,83,74,73", "endOffsets": "260,335,410,487,586,677,773,885,967,1027,1091,1182,1259,1320,1411,1474,1537,1596,1665,1728,1782,1890,1948,2010,2064,2137,2258,2342,2422,2521,2605,2696,2836,2913,2989,3120,3207,3283,3336,3390,3456,3526,3603,3674,3754,3825,3900,3978,4049,4150,4235,4324,4419,4512,4584,4656,4752,4804,4890,4957,5041,5131,5193,5257,5320,5390,5484,5586,5675,5775,5832,5890,5969,6053,6128,6202"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3082,3157,3234,3333,4137,4233,4345,7395,7455,7600,8071,8331,8392,8483,8546,8609,8668,8737,8800,8854,8962,9020,9082,9136,9209,9330,9414,9494,9593,9677,9768,9908,9985,10061,10192,10279,10355,10408,10462,10528,10598,10675,10746,10826,10897,10972,11050,11121,11222,11307,11396,11491,11584,11656,11728,11824,11876,11962,12029,12113,12203,12265,12329,12392,12462,12556,12658,12747,12847,12904,13268,13595,13679,13825", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,74,74,76,98,90,95,111,81,59,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,79,98,83,90,139,76,75,130,86,75,52,53,65,69,76,70,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78,83,74,73", "endOffsets": "310,3077,3152,3229,3328,3419,4228,4340,4422,7450,7514,7686,8143,8387,8478,8541,8604,8663,8732,8795,8849,8957,9015,9077,9131,9204,9325,9409,9489,9588,9672,9763,9903,9980,10056,10187,10274,10350,10403,10457,10523,10593,10670,10741,10821,10892,10967,11045,11116,11217,11302,11391,11486,11579,11651,11723,11819,11871,11957,12024,12108,12198,12260,12324,12387,12457,12551,12653,12742,12842,12899,12957,13342,13674,13749,13894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5700", "endColumns": "146", "endOffsets": "5842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3424,3521,3623,3721,3818,3920,4026,13899", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3516,3618,3716,3813,3915,4021,4132,13995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,528,640,725,831,951,1031,1106,1197,1290,1382,1476,1576,1669,1771,1866,1957,2048,2127,2234,2338,2434,2541,2644,2753,2909,13515", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "424,523,635,720,826,946,1026,1101,1192,1285,1377,1471,1571,1664,1766,1861,1952,2043,2122,2229,2333,2429,2536,2639,2748,2904,3002,13590"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,984,1050,1130,1218,1289,1367,1435", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,979,1045,1125,1213,1284,1362,1430,1548"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4427,4520,7035,7130,7311,8148,8231,12962,13050,13134,13202,13347,13427,13754,45106,45184,45252", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "4515,4599,7125,7225,7390,8226,8326,13045,13129,13197,13263,13422,13510,13820,45179,45247,45365"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,207,280,386,470,551,632,763,869,964,1059,1189,1252,1368,1430,1611,1811,1962,2049,2150,2243,2339,2502,2608,2708,2954,3066,3196,3403,3617,3713,3827,4041,4140,4200,4263,4351,4449,4542,4616,4714,4811,4916,5124,5203,5270,5344,5463,5545,5630,5703,5782,5851,5924,6041,6128,6240,6336,6409,6496,6576,6663,6723,6829,6934,7045,7175,7240,7332,7431,7562,7634,7726,7826,7885,7942,8021,8126,8297,8557,8842,8943,9016,9107,9179,9291,9409,9498,9585,9658,9746,9826,9917,10047,10198,10275,10349,10412,10591,10657,10718,10799,10873,10945,11034,11175,11300,11415,11499,11581,11667,11736", "endColumns": "69,81,72,105,83,80,80,130,105,94,94,129,62,115,61,180,199,150,86,100,92,95,162,105,99,245,111,129,206,213,95,113,213,98,59,62,87,97,92,73,97,96,104,207,78,66,73,118,81,84,72,78,68,72,116,86,111,95,72,86,79,86,59,105,104,110,129,64,91,98,130,71,91,99,58,56,78,104,170,259,284,100,72,90,71,111,117,88,86,72,87,79,90,129,150,76,73,62,178,65,60,80,73,71,88,140,124,114,83,81,85,68,160", "endOffsets": "120,202,275,381,465,546,627,758,864,959,1054,1184,1247,1363,1425,1606,1806,1957,2044,2145,2238,2334,2497,2603,2703,2949,3061,3191,3398,3612,3708,3822,4036,4135,4195,4258,4346,4444,4537,4611,4709,4806,4911,5119,5198,5265,5339,5458,5540,5625,5698,5777,5846,5919,6036,6123,6235,6331,6404,6491,6571,6658,6718,6824,6929,7040,7170,7235,7327,7426,7557,7629,7721,7821,7880,7937,8016,8121,8292,8552,8837,8938,9011,9102,9174,9286,9404,9493,9580,9653,9741,9821,9912,10042,10193,10270,10344,10407,10586,10652,10713,10794,10868,10940,11029,11170,11295,11410,11494,11576,11662,11731,11892"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15025,15095,15177,20782,22542,22626,22707,24617,25891,26044,26139,28671,30469,30594,30830,31124,31305,31505,31799,31985,32179,32272,32368,32531,32637,32737,32983,33095,33225,33432,33646,33842,33956,34170,34269,34329,34392,34480,34578,34671,34745,34843,34940,35045,35253,35332,35399,35473,35592,35674,35759,35832,35911,35980,36053,36606,36693,36805,36901,36974,37061,37141,37228,37288,37394,37499,37610,37740,37805,37897,37996,38127,38410,38729,40424,40483,40540,40619,40724,40895,41155,41440,41541,42149,42240,42312,42424,42542,42631,42718,42791,42879,42959,43050,43180,43331,43408,43558,43621,43800,43866,43927,44008,44082,44154,44243,44384,44509,44624,44708,44790,44876,44945", "endColumns": "69,81,72,105,83,80,80,130,105,94,94,129,62,115,61,180,199,150,86,100,92,95,162,105,99,245,111,129,206,213,95,113,213,98,59,62,87,97,92,73,97,96,104,207,78,66,73,118,81,84,72,78,68,72,116,86,111,95,72,86,79,86,59,105,104,110,129,64,91,98,130,71,91,99,58,56,78,104,170,259,284,100,72,90,71,111,117,88,86,72,87,79,90,129,150,76,73,62,178,65,60,80,73,71,88,140,124,114,83,81,85,68,160", "endOffsets": "15090,15172,15245,20883,22621,22702,22783,24743,25992,26134,26229,28796,30527,30705,30887,31300,31500,31651,31881,32081,32267,32363,32526,32632,32732,32978,33090,33220,33427,33641,33737,33951,34165,34264,34324,34387,34475,34573,34666,34740,34838,34935,35040,35248,35327,35394,35468,35587,35669,35754,35827,35906,35975,36048,36165,36688,36800,36896,36969,37056,37136,37223,37283,37389,37494,37605,37735,37800,37892,37991,38122,38194,38497,38824,40478,40535,40614,40719,40890,41150,41435,41536,41609,42235,42307,42419,42537,42626,42713,42786,42874,42954,43045,43175,43326,43403,43477,43616,43795,43861,43922,44003,44077,44149,44238,44379,44504,44619,44703,44785,44871,44940,45101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6935,7691,7797,7904", "endColumns": "99,105,106,105", "endOffsets": "7030,7792,7899,8005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,196,264,350,423,484,556,619,682,750,818,885,945,1020,1084,1154,1217,1302,1366,1446,1520,1599,1684,1787,1866,1917,1966,2039,2103,2167,2240,2336,2418,2512", "endColumns": "62,77,67,85,72,60,71,62,62,67,67,66,59,74,63,69,62,84,63,79,73,78,84,102,78,50,48,72,63,63,72,95,81,93,93", "endOffsets": "113,191,259,345,418,479,551,614,677,745,813,880,940,1015,1079,1149,1212,1297,1361,1441,1515,1594,1679,1782,1861,1912,1961,2034,2098,2162,2235,2331,2413,2507,2601"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15539,15767,16034,16184,16270,16343,16839,16911,17049,17112,17180,17248,17315,17375,17450,17571,17641,17886,18784,18848,19868,20025,20104,24380,24483,25840,26724,26773,26899,27627,27691,28493,28589,29285,29379", "endColumns": "62,77,67,85,72,60,71,62,62,67,67,66,59,74,63,69,62,84,63,79,73,78,84,102,78,50,48,72,63,63,72,95,81,93,93", "endOffsets": "15597,15840,16097,16265,16338,16399,16906,16969,17107,17175,17243,17310,17370,17445,17509,17636,17699,17966,18843,18923,19937,20099,20184,24478,24557,25886,26768,26841,26958,27686,27759,28584,28666,29374,29468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "27968", "endColumns": "84", "endOffsets": "28048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,341,437,661,706,776,865,937,1152,1212,1293,1358,1413,1477,1553,1645,2003,2077,2143,2196,2252,2326,2448,2559,2616,2692,2768,2853,2925,3037,3364,3446,3523,3585,3645,3705,3766,3833,3909,4008,4101,4201,4301,4395,4468,4547,4637,4848,5072,5193,5337,5393,6156,6260,6321", "endColumns": "126,158,95,223,44,69,88,71,214,59,80,64,54,63,75,91,357,73,65,52,55,73,121,110,56,75,75,84,71,111,326,81,76,61,59,59,60,66,75,98,92,99,99,93,72,78,89,210,223,120,143,55,762,103,60,60", "endOffsets": "177,336,432,656,701,771,860,932,1147,1207,1288,1353,1408,1472,1548,1640,1998,2072,2138,2191,2247,2321,2443,2554,2611,2687,2763,2848,2920,3032,3359,3441,3518,3580,3640,3700,3761,3828,3904,4003,4096,4196,4296,4390,4463,4542,4632,4843,5067,5188,5332,5388,6151,6255,6316,6377"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20400,20527,20686,20888,21910,21955,22025,22114,22186,22401,22461,24315,24562,24748,25042,25118,25482,26234,26308,26846,27764,28053,28127,28249,28360,28417,28962,29200,29632,29772,29884,30211,30293,30532,30710,30770,31063,31656,31723,31886,32086,33742,36170,36270,36364,36437,36516,38199,38886,39110,39231,39375,39496,40259,40363,42088", "endColumns": "126,158,95,223,44,69,88,71,214,59,80,64,54,63,75,91,357,73,65,52,55,73,121,110,56,75,75,84,71,111,326,81,76,61,59,59,60,66,75,98,92,99,99,93,72,78,89,210,223,120,143,55,762,103,60,60", "endOffsets": "20522,20681,20777,21107,21950,22020,22109,22181,22396,22456,22537,24375,24612,24807,25113,25205,25835,26303,26369,26894,27815,28122,28244,28355,28412,28488,29033,29280,29699,29879,30206,30288,30365,30589,30765,30825,31119,31718,31794,31980,32174,33837,36265,36359,36432,36511,36601,38405,39105,39226,39370,39426,40254,40358,40419,42144"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,206,268,345,431,491,557,609,689,765,846,933,1035,1122,1204,1288,1373,1476,1572,1641,1734,1821,1900,2006,2092,2185,2261,2361,2439,2536,2623,2706,2789,2887,2963,3046,3155,3257,3326,3393,4055,4693,4770,4876,4975,5030,5114,5198,5266,5355,5447,5502,5582,5630,5706,5812,5881,5951,6018,6084,6131,6214,6311,6387,6433,6481,6563,6621,6688,6862,7016,7145,7213,7293,7367,7454,7534,7616,7687,7775,7843,7942,8023,8113,8168,8291,8340,8397,8462,8524,8594,8661,8730,8817,8885,8936", "endColumns": "70,79,61,76,85,59,65,51,79,75,80,86,101,86,81,83,84,102,95,68,92,86,78,105,85,92,75,99,77,96,86,82,82,97,75,82,108,101,68,66,661,637,76,105,98,54,83,83,67,88,91,54,79,47,75,105,68,69,66,65,46,82,96,75,45,47,81,57,66,173,153,128,67,79,73,86,79,81,70,87,67,98,80,89,54,122,48,56,64,61,69,66,68,86,67,50,75", "endOffsets": "121,201,263,340,426,486,552,604,684,760,841,928,1030,1117,1199,1283,1368,1471,1567,1636,1729,1816,1895,2001,2087,2180,2256,2356,2434,2531,2618,2701,2784,2882,2958,3041,3150,3252,3321,3388,4050,4688,4765,4871,4970,5025,5109,5193,5261,5350,5442,5497,5577,5625,5701,5807,5876,5946,6013,6079,6126,6209,6306,6382,6428,6476,6558,6616,6683,6857,7011,7140,7208,7288,7362,7449,7529,7611,7682,7770,7838,7937,8018,8108,8163,8286,8335,8392,8457,8519,8589,8656,8725,8812,8880,8931,9007"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14523,14594,14674,14736,14813,14899,14959,15250,15302,15382,15458,15680,15845,15947,16102,16464,16754,17783,18043,18139,18208,18364,18451,18530,18698,18997,19090,19166,19266,19344,19441,19528,19611,19694,19792,19942,20189,20298,21112,21181,21248,22788,23426,23503,23609,23708,23763,23847,23931,23999,24088,24180,24235,24812,24860,24936,25210,25279,25349,25416,25997,26374,26457,26554,26630,26676,26963,27045,27103,27170,27344,27498,27820,27888,28801,28875,29038,29118,29473,29544,29704,30370,30892,30973,38502,38557,38680,38829,39431,41614,41676,41746,41813,41882,41969,42037,43482", "endColumns": "70,79,61,76,85,59,65,51,79,75,80,86,101,86,81,83,84,102,95,68,92,86,78,105,85,92,75,99,77,96,86,82,82,97,75,82,108,101,68,66,661,637,76,105,98,54,83,83,67,88,91,54,79,47,75,105,68,69,66,65,46,82,96,75,45,47,81,57,66,173,153,128,67,79,73,86,79,81,70,87,67,98,80,89,54,122,48,56,64,61,69,66,68,86,67,50,75", "endOffsets": "14589,14669,14731,14808,14894,14954,15020,15297,15377,15453,15534,15762,15942,16029,16179,16543,16834,17881,18134,18203,18296,18446,18525,18631,18779,19085,19161,19261,19339,19436,19523,19606,19689,19787,19863,20020,20293,20395,21176,21243,21905,23421,23498,23604,23703,23758,23842,23926,23994,24083,24175,24230,24310,24855,24931,25037,25274,25344,25411,25477,26039,26452,26549,26625,26671,26719,27040,27098,27165,27339,27493,27622,27883,27963,28870,28957,29113,29195,29539,29627,29767,30464,30968,31058,38552,38675,38724,38881,39491,41671,41741,41808,41877,41964,42032,42083,43553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,140,239,308,377,440,511", "endColumns": "84,98,68,68,62,70,66", "endOffsets": "135,234,303,372,435,506,573"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14000,14085,14184,14253,14322,14385,14456", "endColumns": "84,98,68,68,62,70,66", "endOffsets": "14080,14179,14248,14317,14380,14451,14518"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,216", "endColumns": "79,80,80", "endOffsets": "130,211,292"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4604,7230,7519", "endColumns": "79,80,80", "endOffsets": "4679,7306,7595"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "8010,45541", "endColumns": "60,78", "endOffsets": "8066,45615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "45370,45454", "endColumns": "83,86", "endOffsets": "45449,45536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4684,4794,4949,5085,5190,5337,5467,5594,5847,6019,6126,6283,6417,6562,6729,6791,6855", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "4789,4944,5080,5185,5332,5462,5589,5695,6014,6121,6278,6412,6557,6724,6786,6850,6930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,193,255,338,399,474,531,610,682,745,807", "endColumns": "77,59,61,82,60,74,56,78,71,62,61,68", "endOffsets": "128,188,250,333,394,469,526,605,677,740,802,871"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15602,16404,16548,16610,16693,16974,17514,17704,17971,18301,18636,18928", "endColumns": "77,59,61,82,60,74,56,78,71,62,61,68", "endOffsets": "15675,16459,16605,16688,16749,17044,17566,17778,18038,18359,18693,18992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,340,415,492,591,682,778,890,972,1032,1096,1187,1264,1325,1416,1479,1542,1601,1670,1733,1787,1895,1953,2015,2069,2142,2263,2347,2427,2526,2610,2701,2841,2918,2994,3125,3212,3288,3341,3395,3461,3531,3608,3679,3759,3830,3905,3983,4054,4155,4240,4329,4424,4517,4589,4661,4757,4809,4895,4962,5046,5136,5198,5262,5325,5395,5489,5591,5680,5780,5837,5895,5974,6058,6133", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,74,74,76,98,90,95,111,81,59,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,79,98,83,90,139,76,75,130,86,75,52,53,65,69,76,70,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78,83,74,73", "endOffsets": "260,335,410,487,586,677,773,885,967,1027,1091,1182,1259,1320,1411,1474,1537,1596,1665,1728,1782,1890,1948,2010,2064,2137,2258,2342,2422,2521,2605,2696,2836,2913,2989,3120,3207,3283,3336,3390,3456,3526,3603,3674,3754,3825,3900,3978,4049,4150,4235,4324,4419,4512,4584,4656,4752,4804,4890,4957,5041,5131,5193,5257,5320,5390,5484,5586,5675,5775,5832,5890,5969,6053,6128,6202"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3082,3157,3234,3333,4137,4233,4345,7395,7455,7600,8071,8331,8392,8483,8546,8609,8668,8737,8800,8854,8962,9020,9082,9136,9209,9330,9414,9494,9593,9677,9768,9908,9985,10061,10192,10279,10355,10408,10462,10528,10598,10675,10746,10826,10897,10972,11050,11121,11222,11307,11396,11491,11584,11656,11728,11824,11876,11962,12029,12113,12203,12265,12329,12392,12462,12556,12658,12747,12847,12904,13268,13595,13679,13825", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,74,74,76,98,90,95,111,81,59,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,79,98,83,90,139,76,75,130,86,75,52,53,65,69,76,70,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78,83,74,73", "endOffsets": "310,3077,3152,3229,3328,3419,4228,4340,4422,7450,7514,7686,8143,8387,8478,8541,8604,8663,8732,8795,8849,8957,9015,9077,9131,9204,9325,9409,9489,9588,9672,9763,9903,9980,10056,10187,10274,10350,10403,10457,10523,10593,10670,10741,10821,10892,10967,11045,11116,11217,11302,11391,11486,11579,11651,11723,11819,11871,11957,12024,12108,12198,12260,12324,12387,12457,12551,12653,12742,12842,12899,12957,13342,13674,13749,13894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5700", "endColumns": "146", "endOffsets": "5842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3424,3521,3623,3721,3818,3920,4026,13899", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3516,3618,3716,3813,3915,4021,4132,13995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,528,640,725,831,951,1031,1106,1197,1290,1382,1476,1576,1669,1771,1866,1957,2048,2127,2234,2338,2434,2541,2644,2753,2909,13515", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "424,523,635,720,826,946,1026,1101,1192,1285,1377,1471,1571,1664,1766,1861,1952,2043,2122,2229,2333,2429,2536,2639,2748,2904,3002,13590"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,984,1050,1130,1218,1289,1367,1435", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,979,1045,1125,1213,1284,1362,1430,1548"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4427,4520,7035,7130,7311,8148,8231,12962,13050,13134,13202,13347,13427,13754,45106,45184,45252", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "4515,4599,7125,7225,7390,8226,8326,13045,13129,13197,13263,13422,13510,13820,45179,45247,45365"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,207,280,386,470,551,632,763,869,964,1059,1189,1252,1368,1430,1611,1811,1962,2049,2150,2243,2339,2502,2608,2708,2954,3066,3196,3403,3617,3713,3827,4041,4140,4200,4263,4351,4449,4542,4616,4714,4811,4916,5124,5203,5270,5344,5463,5545,5630,5703,5782,5851,5924,6041,6128,6240,6336,6409,6496,6576,6663,6723,6829,6934,7045,7175,7240,7332,7431,7562,7634,7726,7826,7885,7942,8021,8126,8297,8557,8842,8943,9016,9107,9179,9291,9409,9498,9585,9658,9746,9826,9917,10047,10198,10275,10349,10412,10591,10657,10718,10799,10873,10945,11034,11175,11300,11415,11499,11581,11667,11736", "endColumns": "69,81,72,105,83,80,80,130,105,94,94,129,62,115,61,180,199,150,86,100,92,95,162,105,99,245,111,129,206,213,95,113,213,98,59,62,87,97,92,73,97,96,104,207,78,66,73,118,81,84,72,78,68,72,116,86,111,95,72,86,79,86,59,105,104,110,129,64,91,98,130,71,91,99,58,56,78,104,170,259,284,100,72,90,71,111,117,88,86,72,87,79,90,129,150,76,73,62,178,65,60,80,73,71,88,140,124,114,83,81,85,68,160", "endOffsets": "120,202,275,381,465,546,627,758,864,959,1054,1184,1247,1363,1425,1606,1806,1957,2044,2145,2238,2334,2497,2603,2703,2949,3061,3191,3398,3612,3708,3822,4036,4135,4195,4258,4346,4444,4537,4611,4709,4806,4911,5119,5198,5265,5339,5458,5540,5625,5698,5777,5846,5919,6036,6123,6235,6331,6404,6491,6571,6658,6718,6824,6929,7040,7170,7235,7327,7426,7557,7629,7721,7821,7880,7937,8016,8121,8292,8552,8837,8938,9011,9102,9174,9286,9404,9493,9580,9653,9741,9821,9912,10042,10193,10270,10344,10407,10586,10652,10713,10794,10868,10940,11029,11170,11295,11410,11494,11576,11662,11731,11892"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15025,15095,15177,20782,22542,22626,22707,24617,25891,26044,26139,28671,30469,30594,30830,31124,31305,31505,31799,31985,32179,32272,32368,32531,32637,32737,32983,33095,33225,33432,33646,33842,33956,34170,34269,34329,34392,34480,34578,34671,34745,34843,34940,35045,35253,35332,35399,35473,35592,35674,35759,35832,35911,35980,36053,36606,36693,36805,36901,36974,37061,37141,37228,37288,37394,37499,37610,37740,37805,37897,37996,38127,38410,38729,40424,40483,40540,40619,40724,40895,41155,41440,41541,42149,42240,42312,42424,42542,42631,42718,42791,42879,42959,43050,43180,43331,43408,43558,43621,43800,43866,43927,44008,44082,44154,44243,44384,44509,44624,44708,44790,44876,44945", "endColumns": "69,81,72,105,83,80,80,130,105,94,94,129,62,115,61,180,199,150,86,100,92,95,162,105,99,245,111,129,206,213,95,113,213,98,59,62,87,97,92,73,97,96,104,207,78,66,73,118,81,84,72,78,68,72,116,86,111,95,72,86,79,86,59,105,104,110,129,64,91,98,130,71,91,99,58,56,78,104,170,259,284,100,72,90,71,111,117,88,86,72,87,79,90,129,150,76,73,62,178,65,60,80,73,71,88,140,124,114,83,81,85,68,160", "endOffsets": "15090,15172,15245,20883,22621,22702,22783,24743,25992,26134,26229,28796,30527,30705,30887,31300,31500,31651,31881,32081,32267,32363,32526,32632,32732,32978,33090,33220,33427,33641,33737,33951,34165,34264,34324,34387,34475,34573,34666,34740,34838,34935,35040,35248,35327,35394,35468,35587,35669,35754,35827,35906,35975,36048,36165,36688,36800,36896,36969,37056,37136,37223,37283,37389,37494,37605,37735,37800,37892,37991,38122,38194,38497,38824,40478,40535,40614,40719,40890,41150,41435,41536,41609,42235,42307,42419,42537,42626,42713,42786,42874,42954,43045,43175,43326,43403,43477,43616,43795,43861,43922,44003,44077,44149,44238,44379,44504,44619,44703,44785,44871,44940,45101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6935,7691,7797,7904", "endColumns": "99,105,106,105", "endOffsets": "7030,7792,7899,8005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,196,264,350,423,484,556,619,682,750,818,885,945,1020,1084,1154,1217,1302,1366,1446,1520,1599,1684,1787,1866,1917,1966,2039,2103,2167,2240,2336,2418,2512", "endColumns": "62,77,67,85,72,60,71,62,62,67,67,66,59,74,63,69,62,84,63,79,73,78,84,102,78,50,48,72,63,63,72,95,81,93,93", "endOffsets": "113,191,259,345,418,479,551,614,677,745,813,880,940,1015,1079,1149,1212,1297,1361,1441,1515,1594,1679,1782,1861,1912,1961,2034,2098,2162,2235,2331,2413,2507,2601"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15539,15767,16034,16184,16270,16343,16839,16911,17049,17112,17180,17248,17315,17375,17450,17571,17641,17886,18784,18848,19868,20025,20104,24380,24483,25840,26724,26773,26899,27627,27691,28493,28589,29285,29379", "endColumns": "62,77,67,85,72,60,71,62,62,67,67,66,59,74,63,69,62,84,63,79,73,78,84,102,78,50,48,72,63,63,72,95,81,93,93", "endOffsets": "15597,15840,16097,16265,16338,16399,16906,16969,17107,17175,17243,17310,17370,17445,17509,17636,17699,17966,18843,18923,19937,20099,20184,24478,24557,25886,26768,26841,26958,27686,27759,28584,28666,29374,29468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "27968", "endColumns": "84", "endOffsets": "28048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,341,437,661,706,776,865,937,1152,1212,1293,1358,1413,1477,1553,1645,2003,2077,2143,2196,2252,2326,2448,2559,2616,2692,2768,2853,2925,3037,3364,3446,3523,3585,3645,3705,3766,3833,3909,4008,4101,4201,4301,4395,4468,4547,4637,4848,5072,5193,5337,5393,6156,6260,6321", "endColumns": "126,158,95,223,44,69,88,71,214,59,80,64,54,63,75,91,357,73,65,52,55,73,121,110,56,75,75,84,71,111,326,81,76,61,59,59,60,66,75,98,92,99,99,93,72,78,89,210,223,120,143,55,762,103,60,60", "endOffsets": "177,336,432,656,701,771,860,932,1147,1207,1288,1353,1408,1472,1548,1640,1998,2072,2138,2191,2247,2321,2443,2554,2611,2687,2763,2848,2920,3032,3359,3441,3518,3580,3640,3700,3761,3828,3904,4003,4096,4196,4296,4390,4463,4542,4632,4843,5067,5188,5332,5388,6151,6255,6316,6377"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20400,20527,20686,20888,21910,21955,22025,22114,22186,22401,22461,24315,24562,24748,25042,25118,25482,26234,26308,26846,27764,28053,28127,28249,28360,28417,28962,29200,29632,29772,29884,30211,30293,30532,30710,30770,31063,31656,31723,31886,32086,33742,36170,36270,36364,36437,36516,38199,38886,39110,39231,39375,39496,40259,40363,42088", "endColumns": "126,158,95,223,44,69,88,71,214,59,80,64,54,63,75,91,357,73,65,52,55,73,121,110,56,75,75,84,71,111,326,81,76,61,59,59,60,66,75,98,92,99,99,93,72,78,89,210,223,120,143,55,762,103,60,60", "endOffsets": "20522,20681,20777,21107,21950,22020,22109,22181,22396,22456,22537,24375,24612,24807,25113,25205,25835,26303,26369,26894,27815,28122,28244,28355,28412,28488,29033,29280,29699,29879,30206,30288,30365,30589,30765,30825,31119,31718,31794,31980,32174,33837,36265,36359,36432,36511,36601,38405,39105,39226,39370,39426,40254,40358,40419,42144"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,206,268,345,431,491,557,609,689,765,846,933,1035,1122,1204,1288,1373,1476,1572,1641,1734,1821,1900,2006,2092,2185,2261,2361,2439,2536,2623,2706,2789,2887,2963,3046,3155,3257,3326,3393,4055,4693,4770,4876,4975,5030,5114,5198,5266,5355,5447,5502,5582,5630,5706,5812,5881,5951,6018,6084,6131,6214,6311,6387,6433,6481,6563,6621,6688,6862,7016,7145,7213,7293,7367,7454,7534,7616,7687,7775,7843,7942,8023,8113,8168,8291,8340,8397,8462,8524,8594,8661,8730,8817,8885,8936", "endColumns": "70,79,61,76,85,59,65,51,79,75,80,86,101,86,81,83,84,102,95,68,92,86,78,105,85,92,75,99,77,96,86,82,82,97,75,82,108,101,68,66,661,637,76,105,98,54,83,83,67,88,91,54,79,47,75,105,68,69,66,65,46,82,96,75,45,47,81,57,66,173,153,128,67,79,73,86,79,81,70,87,67,98,80,89,54,122,48,56,64,61,69,66,68,86,67,50,75", "endOffsets": "121,201,263,340,426,486,552,604,684,760,841,928,1030,1117,1199,1283,1368,1471,1567,1636,1729,1816,1895,2001,2087,2180,2256,2356,2434,2531,2618,2701,2784,2882,2958,3041,3150,3252,3321,3388,4050,4688,4765,4871,4970,5025,5109,5193,5261,5350,5442,5497,5577,5625,5701,5807,5876,5946,6013,6079,6126,6209,6306,6382,6428,6476,6558,6616,6683,6857,7011,7140,7208,7288,7362,7449,7529,7611,7682,7770,7838,7937,8018,8108,8163,8286,8335,8392,8457,8519,8589,8656,8725,8812,8880,8931,9007"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14523,14594,14674,14736,14813,14899,14959,15250,15302,15382,15458,15680,15845,15947,16102,16464,16754,17783,18043,18139,18208,18364,18451,18530,18698,18997,19090,19166,19266,19344,19441,19528,19611,19694,19792,19942,20189,20298,21112,21181,21248,22788,23426,23503,23609,23708,23763,23847,23931,23999,24088,24180,24235,24812,24860,24936,25210,25279,25349,25416,25997,26374,26457,26554,26630,26676,26963,27045,27103,27170,27344,27498,27820,27888,28801,28875,29038,29118,29473,29544,29704,30370,30892,30973,38502,38557,38680,38829,39431,41614,41676,41746,41813,41882,41969,42037,43482", "endColumns": "70,79,61,76,85,59,65,51,79,75,80,86,101,86,81,83,84,102,95,68,92,86,78,105,85,92,75,99,77,96,86,82,82,97,75,82,108,101,68,66,661,637,76,105,98,54,83,83,67,88,91,54,79,47,75,105,68,69,66,65,46,82,96,75,45,47,81,57,66,173,153,128,67,79,73,86,79,81,70,87,67,98,80,89,54,122,48,56,64,61,69,66,68,86,67,50,75", "endOffsets": "14589,14669,14731,14808,14894,14954,15020,15297,15377,15453,15534,15762,15942,16029,16179,16543,16834,17881,18134,18203,18296,18446,18525,18631,18779,19085,19161,19261,19339,19436,19523,19606,19689,19787,19863,20020,20293,20395,21176,21243,21905,23421,23498,23604,23703,23758,23842,23926,23994,24083,24175,24230,24310,24855,24931,25037,25274,25344,25411,25477,26039,26452,26549,26625,26671,26719,27040,27098,27165,27339,27493,27622,27883,27963,28870,28957,29113,29195,29539,29627,29767,30464,30968,31058,38552,38675,38724,38881,39491,41671,41741,41808,41877,41964,42032,42083,43553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,140,239,308,377,440,511", "endColumns": "84,98,68,68,62,70,66", "endOffsets": "135,234,303,372,435,506,573"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14000,14085,14184,14253,14322,14385,14456", "endColumns": "84,98,68,68,62,70,66", "endOffsets": "14080,14179,14248,14317,14380,14451,14518"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,216", "endColumns": "79,80,80", "endOffsets": "130,211,292"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4604,7230,7519", "endColumns": "79,80,80", "endOffsets": "4679,7306,7595"}}]}]}
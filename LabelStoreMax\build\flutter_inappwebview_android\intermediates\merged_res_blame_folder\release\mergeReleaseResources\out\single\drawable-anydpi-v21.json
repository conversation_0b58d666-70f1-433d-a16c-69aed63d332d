[{"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/drawable-anydpi-v21/ic_call_decline_low.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-core-1.13.1-5:/drawable-anydpi-v21/ic_call_decline_low.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/drawable-anydpi-v21/ic_call_decline.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-core-1.13.1-5:/drawable-anydpi-v21/ic_call_decline.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/drawable-anydpi-v21/ic_call_answer_video_low.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-core-1.13.1-5:/drawable-anydpi-v21/ic_call_answer_video_low.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/drawable-anydpi-v21/ic_call_answer_video.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-core-1.13.1-5:/drawable-anydpi-v21/ic_call_answer_video.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/drawable-anydpi-v21/ic_call_answer.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-core-1.13.1-5:/drawable-anydpi-v21/ic_call_answer.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/drawable-anydpi-v21/ic_call_answer_low.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-core-1.13.1-5:/drawable-anydpi-v21/ic_call_answer_low.xml"}]
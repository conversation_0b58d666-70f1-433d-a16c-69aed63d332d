[{"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_tint_seek_thumb.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_tint_seek_thumb.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_search_url_text.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_search_url_text.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_tint_btn_checkable.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_tint_btn_checkable.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_background_cache_hint_selector_material_light.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_background_cache_hint_selector_material_light.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_secondary_text_material_light.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_secondary_text_material_light.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_hint_foreground_material_dark.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_hint_foreground_material_dark.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_tint_switch_track.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_tint_switch_track.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_btn_colored_borderless_text_material.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_btn_colored_borderless_text_material.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_primary_text_material_light.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_primary_text_material_light.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_background_cache_hint_selector_material_dark.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_background_cache_hint_selector_material_dark.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_primary_text_material_dark.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_primary_text_material_dark.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_secondary_text_material_dark.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_secondary_text_material_dark.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/switch_thumb_material_dark.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/switch_thumb_material_dark.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_primary_text_disable_only_material_dark.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_primary_text_disable_only_material_dark.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_primary_text_disable_only_material_light.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_primary_text_disable_only_material_light.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_hint_foreground_material_light.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_hint_foreground_material_light.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_btn_colored_text_material.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_btn_colored_text_material.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_tint_edittext.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_tint_edittext.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/switch_thumb_material_light.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/switch_thumb_material_light.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_tint_default.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_tint_default.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/color/abc_tint_spinner.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-3:/color/abc_tint_spinner.xml"}]
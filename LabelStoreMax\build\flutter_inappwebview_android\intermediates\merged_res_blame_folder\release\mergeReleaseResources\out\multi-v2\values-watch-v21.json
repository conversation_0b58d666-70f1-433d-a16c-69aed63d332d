{"logs": [{"outputFile": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/values-watch-v21/values-watch-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\260d846a9efc7646c516cdb70fa58786\\transformed\\appcompat-1.6.1\\res\\values-watch-v21\\values-watch-v21.xml", "from": {"startLines": "2,6,10", "startColumns": "4,4,4", "startOffsets": "55,271,499", "endLines": "5,9,13", "endColumns": "12,12,12", "endOffsets": "266,494,724"}}]}, {"outputFile": "com.pichillilorenzo.flutter_inappwebview_android-mergeReleaseResources-30:/values-watch-v21/values-watch-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\260d846a9efc7646c516cdb70fa58786\\transformed\\appcompat-1.6.1\\res\\values-watch-v21\\values-watch-v21.xml", "from": {"startLines": "2,6,10", "startColumns": "4,4,4", "startOffsets": "55,271,499", "endLines": "5,9,13", "endColumns": "12,12,12", "endOffsets": "266,494,724"}}]}]}
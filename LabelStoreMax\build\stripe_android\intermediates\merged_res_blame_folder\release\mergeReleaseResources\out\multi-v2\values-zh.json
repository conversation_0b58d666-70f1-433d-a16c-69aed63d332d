{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-zh/values-zh.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,186,250,323,388,449,512,572,632,698,762,821,879,945,1007,1072,1130,1195,1254,1326,1399,1464,1535,1607,1670,1715,1761,1823,1885,1938,2000,2072,2139,2209", "endColumns": "59,70,63,72,64,60,62,59,59,65,63,58,57,65,61,64,57,64,58,71,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "110,181,245,318,383,444,507,567,627,693,757,816,874,940,1002,1067,1125,1190,1249,1321,1394,1459,1530,1602,1665,1710,1756,1818,1880,1933,1995,2067,2134,2204,2273"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,116,117,131,143,144,146,153,154,164,165,173,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1396,1612,1841,1977,2050,2115,2566,2629,2751,2811,2877,2941,3000,3058,3124,3243,3308,3513,4279,4338,5193,5333,5398,8411,8483,9482,10193,10239,10352,10847,10900,11556,11628,12165,12235", "endColumns": "59,70,63,72,64,60,62,59,59,65,63,58,57,65,61,64,57,64,58,71,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "1451,1678,1900,2045,2110,2171,2624,2684,2806,2872,2936,2995,3053,3119,3181,3303,3361,3573,4333,4405,5261,5393,5464,8478,8541,9522,10234,10296,10409,10895,10957,11623,11690,12230,12299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,180,241,310,381,454,526,604,673,744,814,893,944,1016,1075,1193,1315,1410,1495,1577,1661,1747,1851,1939,2025,2146,2231,2330,2498,2665,2753,2843,2945,3024,3081,3140,3214,3292,3369,3435,3513,3590,3675,3796,3860,3922,3983,4073,4143,4216,4275,4344,4412,4478,4566,4647,4738,4819,4883,4954,5018,5089,5145,5228,5304,5388,5487,5545,5622,5706,5807,5874,5937,6017,6067,6118,6176,6245,6363,6530,6713,6792,6852,6917,6976,7055,7140,7213,7281,7346,7415,7478,7553,7631,7715,7780,7843,7893,8006,8071,8124,8192,8254,8332,8398,8483,8569,8647,8718,8781,8844,8902", "endColumns": "58,65,60,68,70,72,71,77,68,70,69,78,50,71,58,117,121,94,84,81,83,85,103,87,85,120,84,98,167,166,87,89,101,78,56,58,73,77,76,65,77,76,84,120,63,61,60,89,69,72,58,68,67,65,87,80,90,80,63,70,63,70,55,82,75,83,98,57,76,83,100,66,62,79,49,50,57,68,117,166,182,78,59,64,58,78,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,62,62,57,93", "endOffsets": "109,175,236,305,376,449,521,599,668,739,809,888,939,1011,1070,1188,1310,1405,1490,1572,1656,1742,1846,1934,2020,2141,2226,2325,2493,2660,2748,2838,2940,3019,3076,3135,3209,3287,3364,3430,3508,3585,3670,3791,3855,3917,3978,4068,4138,4211,4270,4339,4407,4473,4561,4642,4733,4814,4878,4949,5013,5084,5140,5223,5299,5383,5482,5540,5617,5701,5802,5869,5932,6012,6062,6113,6171,6240,6358,6525,6708,6787,6847,6912,6971,7050,7135,7208,7276,7341,7410,7473,7548,7626,7710,7775,7838,7888,8001,8066,8119,8187,8249,8327,8393,8478,8564,8642,8713,8776,8839,8897,8991"}, "to": {"startLines": "16,17,18,88,100,101,102,119,132,134,135,166,184,186,189,193,194,195,198,200,202,203,204,205,206,207,208,209,210,211,212,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,261,265,275,276,277,278,279,280,281,282,283,292,293,294,295,296,297,298,299,300,301,302,303,304,305,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "961,1020,1086,5946,7064,7135,7208,8599,9527,9640,9711,11695,12993,13098,13286,13550,13668,13790,14013,14188,14347,14431,14517,14621,14709,14795,14916,15001,15100,15268,15435,15600,15690,15792,15871,15928,15987,16061,16139,16216,16282,16360,16437,16522,16643,16707,16769,16830,16920,16990,17063,17122,17191,17259,17325,17820,17901,17992,18073,18137,18208,18272,18343,18399,18482,18558,18642,18741,18799,18876,18960,19061,19229,19458,20360,20410,20461,20519,20588,20706,20873,21056,21135,21662,21727,21786,21865,21950,22023,22091,22156,22225,22288,22363,22441,22525,22590,22715,22765,22878,22943,22996,23064,23126,23204,23270,23355,23441,23519,23590,23653,23716,23774", "endColumns": "58,65,60,68,70,72,71,77,68,70,69,78,50,71,58,117,121,94,84,81,83,85,103,87,85,120,84,98,167,166,87,89,101,78,56,58,73,77,76,65,77,76,84,120,63,61,60,89,69,72,58,68,67,65,87,80,90,80,63,70,63,70,55,82,75,83,98,57,76,83,100,66,62,79,49,50,57,68,117,166,182,78,59,64,58,78,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,62,62,57,93", "endOffsets": "1015,1081,1142,6010,7130,7203,7275,8672,9591,9706,9776,11769,13039,13165,13340,13663,13785,13880,14093,14265,14426,14512,14616,14704,14790,14911,14996,15095,15263,15430,15518,15685,15787,15866,15923,15982,16056,16134,16211,16277,16355,16432,16517,16638,16702,16764,16825,16915,16985,17058,17117,17186,17254,17320,17408,17896,17987,18068,18132,18203,18267,18338,18394,18477,18553,18637,18736,18794,18871,18955,19056,19123,19287,19533,20405,20456,20514,20583,20701,20868,21051,21130,21190,21722,21781,21860,21945,22018,22086,22151,22220,22283,22358,22436,22520,22585,22648,22760,22873,22938,22991,23059,23121,23199,23265,23350,23436,23514,23585,23648,23711,23769,23863"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,183,243,316,374,436,493,560,626,686,743", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "121,178,238,311,369,431,488,555,621,681,738,801"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1456,2176,2304,2364,2437,2689,3186,3366,3578,3862,4151,4410", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "1522,2228,2359,2432,2490,2746,3238,3428,3639,3917,4203,4468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,183,244,305,374,426,484,532,599,665,733,818,902,976,1048,1119,1190,1270,1349,1412,1488,1562,1633,1717,1788,1864,1934,2017,2082,2157,2228,2297,2367,2448,2508,2575,2667,2752,2812,2873,3204,3520,3585,3669,3749,3804,3880,3952,4010,4081,4154,4209,4279,4324,4394,4476,4533,4598,4665,4731,4775,4839,4917,4981,5024,5067,5124,5182,5240,5331,5423,5500,5560,5623,5682,5757,5820,5880,5942,6013,6069,6143,6214,6290,6339,6411,6456,6506,6562,6618,6681,6740,6801,6872,6929,6974", "endColumns": "59,67,60,60,68,51,57,47,66,65,67,84,83,73,71,70,70,79,78,62,75,73,70,83,70,75,69,82,64,74,70,68,69,80,59,66,91,84,59,60,330,315,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,65,43,63,77,63,42,42,56,57,57,90,91,76,59,62,58,74,62,59,61,70,55,73,70,75,48,71,44,49,55,55,62,58,60,70,56,44,61", "endOffsets": "110,178,239,300,369,421,479,527,594,660,728,813,897,971,1043,1114,1185,1265,1344,1407,1483,1557,1628,1712,1783,1859,1929,2012,2077,2152,2223,2292,2362,2443,2503,2570,2662,2747,2807,2868,3199,3515,3580,3664,3744,3799,3875,3947,4005,4076,4149,4204,4274,4319,4389,4471,4528,4593,4660,4726,4770,4834,4912,4976,5019,5062,5119,5177,5235,5326,5418,5495,5555,5618,5677,5752,5815,5875,5937,6008,6064,6138,6209,6285,6334,6406,6451,6501,6557,6613,6676,6735,6796,6867,6924,6969,7031"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,84,90,91,92,103,104,105,106,107,108,109,110,111,112,113,114,121,122,123,126,127,128,129,133,138,139,140,141,142,147,148,149,150,151,152,156,157,167,168,170,171,175,176,178,183,190,191,262,263,264,266,271,284,285,286,287,288,289,290,306", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "532,592,660,721,782,851,903,1147,1195,1262,1328,1527,1683,1767,1905,2233,2495,3433,3644,3723,3786,3922,3996,4067,4208,4473,4549,4619,4702,4767,4842,4913,4982,5052,5133,5266,5469,5561,6127,6187,6248,7280,7596,7661,7745,7825,7880,7956,8028,8086,8157,8230,8285,8741,8786,8856,9083,9140,9205,9272,9596,9901,9965,10043,10107,10150,10414,10471,10529,10587,10678,10770,11013,11073,11774,11833,11975,12038,12304,12366,12497,12919,13345,13416,19292,19341,19413,19538,19935,21195,21251,21314,21373,21434,21505,21562,22653", "endColumns": "59,67,60,60,68,51,57,47,66,65,67,84,83,73,71,70,70,79,78,62,75,73,70,83,70,75,69,82,64,74,70,68,69,80,59,66,91,84,59,60,330,315,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,65,43,63,77,63,42,42,56,57,57,90,91,76,59,62,58,74,62,59,61,70,55,73,70,75,48,71,44,49,55,55,62,58,60,70,56,44,61", "endOffsets": "587,655,716,777,846,898,956,1190,1257,1323,1391,1607,1762,1836,1972,2299,2561,3508,3718,3781,3857,3991,4062,4146,4274,4544,4614,4697,4762,4837,4908,4977,5047,5128,5188,5328,5556,5641,6182,6243,6574,7591,7656,7740,7820,7875,7951,8023,8081,8152,8225,8280,8350,8781,8851,8933,9135,9200,9267,9333,9635,9960,10038,10102,10145,10188,10466,10524,10582,10673,10765,10842,11068,11131,11828,11903,12033,12093,12361,12432,12548,12988,13411,13487,19336,19408,19453,19583,19986,21246,21309,21368,21429,21500,21557,21602,22710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,279,355,467,510,568,643,707,825,880,952,1008,1061,1125,1190,1270,1414,1474,1534,1585,1636,1702,1789,1866,1921,1992,2059,2126,2186,2261,2414,2480,2552,2606,2664,2722,2780,2841,2908,2998,3075,3152,3242,3326,3398,3477,3559,3660,3769,3863,3958,4007,4244,4319,4376", "endColumns": "88,134,75,111,42,57,74,63,117,54,71,55,52,63,64,79,143,59,59,50,50,65,86,76,54,70,66,66,59,74,152,65,71,53,57,57,57,60,66,89,76,76,89,83,71,78,81,100,108,93,94,48,236,74,56,54", "endOffsets": "139,274,350,462,505,563,638,702,820,875,947,1003,1056,1120,1185,1265,1409,1469,1529,1580,1631,1697,1784,1861,1916,1987,2054,2121,2181,2256,2409,2475,2547,2601,2659,2717,2775,2836,2903,2993,3070,3147,3237,3321,3393,3472,3554,3655,3764,3858,3953,4002,4239,4314,4371,4426"}, "to": {"startLines": "85,86,87,89,93,94,95,96,97,98,99,115,118,120,124,125,130,136,137,145,155,159,160,161,162,163,169,172,177,179,180,181,182,185,187,188,192,196,197,199,201,213,238,239,240,241,242,260,267,268,269,270,272,273,274,291", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5646,5735,5870,6015,6579,6622,6680,6755,6819,6937,6992,8355,8546,8677,8938,9003,9338,9781,9841,10301,10962,11200,11266,11353,11430,11485,11908,12098,12437,12553,12628,12781,12847,13044,13170,13228,13492,13885,13946,14098,14270,15523,17413,17503,17587,17659,17738,19128,19588,19697,19791,19886,19991,20228,20303,21607", "endColumns": "88,134,75,111,42,57,74,63,117,54,71,55,52,63,64,79,143,59,59,50,50,65,86,76,54,70,66,66,59,74,152,65,71,53,57,57,57,60,66,89,76,76,89,83,71,78,81,100,108,93,94,48,236,74,56,54", "endOffsets": "5730,5865,5941,6122,6617,6675,6750,6814,6932,6987,7059,8406,8594,8736,8998,9078,9477,9836,9896,10347,11008,11261,11348,11425,11480,11551,11970,12160,12492,12623,12776,12842,12914,13093,13223,13281,13545,13941,14008,14183,14342,15595,17498,17582,17654,17733,17815,19224,19692,19786,19881,19930,20223,20298,20355,21657"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "63", "endOffsets": "114"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "11136", "endColumns": "63", "endOffsets": "11195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,130,214,279,345,405,470", "endColumns": "74,83,64,65,59,64,61", "endOffsets": "125,209,274,340,400,465,527"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-zh/values-zh.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,186,250,323,388,449,512,572,632,698,762,821,879,945,1007,1072,1130,1195,1254,1326,1399,1464,1535,1607,1670,1715,1761,1823,1885,1938,2000,2072,2139,2209", "endColumns": "59,70,63,72,64,60,62,59,59,65,63,58,57,65,61,64,57,64,58,71,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "110,181,245,318,383,444,507,567,627,693,757,816,874,940,1002,1067,1125,1190,1249,1321,1394,1459,1530,1602,1665,1710,1756,1818,1880,1933,1995,2067,2134,2204,2273"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,116,117,131,143,144,146,153,154,164,165,173,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1396,1612,1841,1977,2050,2115,2566,2629,2751,2811,2877,2941,3000,3058,3124,3243,3308,3513,4279,4338,5193,5333,5398,8411,8483,9482,10193,10239,10352,10847,10900,11556,11628,12165,12235", "endColumns": "59,70,63,72,64,60,62,59,59,65,63,58,57,65,61,64,57,64,58,71,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "1451,1678,1900,2045,2110,2171,2624,2684,2806,2872,2936,2995,3053,3119,3181,3303,3361,3573,4333,4405,5261,5393,5464,8478,8541,9522,10234,10296,10409,10895,10957,11623,11690,12230,12299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,180,241,310,381,454,526,604,673,744,814,893,944,1016,1075,1193,1315,1410,1495,1577,1661,1747,1851,1939,2025,2146,2231,2330,2498,2665,2753,2843,2945,3024,3081,3140,3214,3292,3369,3435,3513,3590,3675,3796,3860,3922,3983,4073,4143,4216,4275,4344,4412,4478,4566,4647,4738,4819,4883,4954,5018,5089,5145,5228,5304,5388,5487,5545,5622,5706,5807,5874,5937,6017,6067,6118,6176,6245,6363,6530,6713,6792,6852,6917,6976,7055,7140,7213,7281,7346,7415,7478,7553,7631,7715,7780,7843,7893,8006,8071,8124,8192,8254,8332,8398,8483,8569,8647,8718,8781,8844,8902", "endColumns": "58,65,60,68,70,72,71,77,68,70,69,78,50,71,58,117,121,94,84,81,83,85,103,87,85,120,84,98,167,166,87,89,101,78,56,58,73,77,76,65,77,76,84,120,63,61,60,89,69,72,58,68,67,65,87,80,90,80,63,70,63,70,55,82,75,83,98,57,76,83,100,66,62,79,49,50,57,68,117,166,182,78,59,64,58,78,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,62,62,57,93", "endOffsets": "109,175,236,305,376,449,521,599,668,739,809,888,939,1011,1070,1188,1310,1405,1490,1572,1656,1742,1846,1934,2020,2141,2226,2325,2493,2660,2748,2838,2940,3019,3076,3135,3209,3287,3364,3430,3508,3585,3670,3791,3855,3917,3978,4068,4138,4211,4270,4339,4407,4473,4561,4642,4733,4814,4878,4949,5013,5084,5140,5223,5299,5383,5482,5540,5617,5701,5802,5869,5932,6012,6062,6113,6171,6240,6358,6525,6708,6787,6847,6912,6971,7050,7135,7208,7276,7341,7410,7473,7548,7626,7710,7775,7838,7888,8001,8066,8119,8187,8249,8327,8393,8478,8564,8642,8713,8776,8839,8897,8991"}, "to": {"startLines": "16,17,18,88,100,101,102,119,132,134,135,166,184,186,189,193,194,195,198,200,202,203,204,205,206,207,208,209,210,211,212,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,261,265,275,276,277,278,279,280,281,282,283,292,293,294,295,296,297,298,299,300,301,302,303,304,305,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "961,1020,1086,5946,7064,7135,7208,8599,9527,9640,9711,11695,12993,13098,13286,13550,13668,13790,14013,14188,14347,14431,14517,14621,14709,14795,14916,15001,15100,15268,15435,15600,15690,15792,15871,15928,15987,16061,16139,16216,16282,16360,16437,16522,16643,16707,16769,16830,16920,16990,17063,17122,17191,17259,17325,17820,17901,17992,18073,18137,18208,18272,18343,18399,18482,18558,18642,18741,18799,18876,18960,19061,19229,19458,20360,20410,20461,20519,20588,20706,20873,21056,21135,21662,21727,21786,21865,21950,22023,22091,22156,22225,22288,22363,22441,22525,22590,22715,22765,22878,22943,22996,23064,23126,23204,23270,23355,23441,23519,23590,23653,23716,23774", "endColumns": "58,65,60,68,70,72,71,77,68,70,69,78,50,71,58,117,121,94,84,81,83,85,103,87,85,120,84,98,167,166,87,89,101,78,56,58,73,77,76,65,77,76,84,120,63,61,60,89,69,72,58,68,67,65,87,80,90,80,63,70,63,70,55,82,75,83,98,57,76,83,100,66,62,79,49,50,57,68,117,166,182,78,59,64,58,78,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,62,62,57,93", "endOffsets": "1015,1081,1142,6010,7130,7203,7275,8672,9591,9706,9776,11769,13039,13165,13340,13663,13785,13880,14093,14265,14426,14512,14616,14704,14790,14911,14996,15095,15263,15430,15518,15685,15787,15866,15923,15982,16056,16134,16211,16277,16355,16432,16517,16638,16702,16764,16825,16915,16985,17058,17117,17186,17254,17320,17408,17896,17987,18068,18132,18203,18267,18338,18394,18477,18553,18637,18736,18794,18871,18955,19056,19123,19287,19533,20405,20456,20514,20583,20701,20868,21051,21130,21190,21722,21781,21860,21945,22018,22086,22151,22220,22283,22358,22436,22520,22585,22648,22760,22873,22938,22991,23059,23121,23199,23265,23350,23436,23514,23585,23648,23711,23769,23863"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,183,243,316,374,436,493,560,626,686,743", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "121,178,238,311,369,431,488,555,621,681,738,801"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1456,2176,2304,2364,2437,2689,3186,3366,3578,3862,4151,4410", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "1522,2228,2359,2432,2490,2746,3238,3428,3639,3917,4203,4468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,183,244,305,374,426,484,532,599,665,733,818,902,976,1048,1119,1190,1270,1349,1412,1488,1562,1633,1717,1788,1864,1934,2017,2082,2157,2228,2297,2367,2448,2508,2575,2667,2752,2812,2873,3204,3520,3585,3669,3749,3804,3880,3952,4010,4081,4154,4209,4279,4324,4394,4476,4533,4598,4665,4731,4775,4839,4917,4981,5024,5067,5124,5182,5240,5331,5423,5500,5560,5623,5682,5757,5820,5880,5942,6013,6069,6143,6214,6290,6339,6411,6456,6506,6562,6618,6681,6740,6801,6872,6929,6974", "endColumns": "59,67,60,60,68,51,57,47,66,65,67,84,83,73,71,70,70,79,78,62,75,73,70,83,70,75,69,82,64,74,70,68,69,80,59,66,91,84,59,60,330,315,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,65,43,63,77,63,42,42,56,57,57,90,91,76,59,62,58,74,62,59,61,70,55,73,70,75,48,71,44,49,55,55,62,58,60,70,56,44,61", "endOffsets": "110,178,239,300,369,421,479,527,594,660,728,813,897,971,1043,1114,1185,1265,1344,1407,1483,1557,1628,1712,1783,1859,1929,2012,2077,2152,2223,2292,2362,2443,2503,2570,2662,2747,2807,2868,3199,3515,3580,3664,3744,3799,3875,3947,4005,4076,4149,4204,4274,4319,4389,4471,4528,4593,4660,4726,4770,4834,4912,4976,5019,5062,5119,5177,5235,5326,5418,5495,5555,5618,5677,5752,5815,5875,5937,6008,6064,6138,6209,6285,6334,6406,6451,6501,6557,6613,6676,6735,6796,6867,6924,6969,7031"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,84,90,91,92,103,104,105,106,107,108,109,110,111,112,113,114,121,122,123,126,127,128,129,133,138,139,140,141,142,147,148,149,150,151,152,156,157,167,168,170,171,175,176,178,183,190,191,262,263,264,266,271,284,285,286,287,288,289,290,306", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "532,592,660,721,782,851,903,1147,1195,1262,1328,1527,1683,1767,1905,2233,2495,3433,3644,3723,3786,3922,3996,4067,4208,4473,4549,4619,4702,4767,4842,4913,4982,5052,5133,5266,5469,5561,6127,6187,6248,7280,7596,7661,7745,7825,7880,7956,8028,8086,8157,8230,8285,8741,8786,8856,9083,9140,9205,9272,9596,9901,9965,10043,10107,10150,10414,10471,10529,10587,10678,10770,11013,11073,11774,11833,11975,12038,12304,12366,12497,12919,13345,13416,19292,19341,19413,19538,19935,21195,21251,21314,21373,21434,21505,21562,22653", "endColumns": "59,67,60,60,68,51,57,47,66,65,67,84,83,73,71,70,70,79,78,62,75,73,70,83,70,75,69,82,64,74,70,68,69,80,59,66,91,84,59,60,330,315,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,65,43,63,77,63,42,42,56,57,57,90,91,76,59,62,58,74,62,59,61,70,55,73,70,75,48,71,44,49,55,55,62,58,60,70,56,44,61", "endOffsets": "587,655,716,777,846,898,956,1190,1257,1323,1391,1607,1762,1836,1972,2299,2561,3508,3718,3781,3857,3991,4062,4146,4274,4544,4614,4697,4762,4837,4908,4977,5047,5128,5188,5328,5556,5641,6182,6243,6574,7591,7656,7740,7820,7875,7951,8023,8081,8152,8225,8280,8350,8781,8851,8933,9135,9200,9267,9333,9635,9960,10038,10102,10145,10188,10466,10524,10582,10673,10765,10842,11068,11131,11828,11903,12033,12093,12361,12432,12548,12988,13411,13487,19336,19408,19453,19583,19986,21246,21309,21368,21429,21500,21557,21602,22710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,279,355,467,510,568,643,707,825,880,952,1008,1061,1125,1190,1270,1414,1474,1534,1585,1636,1702,1789,1866,1921,1992,2059,2126,2186,2261,2414,2480,2552,2606,2664,2722,2780,2841,2908,2998,3075,3152,3242,3326,3398,3477,3559,3660,3769,3863,3958,4007,4244,4319,4376", "endColumns": "88,134,75,111,42,57,74,63,117,54,71,55,52,63,64,79,143,59,59,50,50,65,86,76,54,70,66,66,59,74,152,65,71,53,57,57,57,60,66,89,76,76,89,83,71,78,81,100,108,93,94,48,236,74,56,54", "endOffsets": "139,274,350,462,505,563,638,702,820,875,947,1003,1056,1120,1185,1265,1409,1469,1529,1580,1631,1697,1784,1861,1916,1987,2054,2121,2181,2256,2409,2475,2547,2601,2659,2717,2775,2836,2903,2993,3070,3147,3237,3321,3393,3472,3554,3655,3764,3858,3953,4002,4239,4314,4371,4426"}, "to": {"startLines": "85,86,87,89,93,94,95,96,97,98,99,115,118,120,124,125,130,136,137,145,155,159,160,161,162,163,169,172,177,179,180,181,182,185,187,188,192,196,197,199,201,213,238,239,240,241,242,260,267,268,269,270,272,273,274,291", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5646,5735,5870,6015,6579,6622,6680,6755,6819,6937,6992,8355,8546,8677,8938,9003,9338,9781,9841,10301,10962,11200,11266,11353,11430,11485,11908,12098,12437,12553,12628,12781,12847,13044,13170,13228,13492,13885,13946,14098,14270,15523,17413,17503,17587,17659,17738,19128,19588,19697,19791,19886,19991,20228,20303,21607", "endColumns": "88,134,75,111,42,57,74,63,117,54,71,55,52,63,64,79,143,59,59,50,50,65,86,76,54,70,66,66,59,74,152,65,71,53,57,57,57,60,66,89,76,76,89,83,71,78,81,100,108,93,94,48,236,74,56,54", "endOffsets": "5730,5865,5941,6122,6617,6675,6750,6814,6932,6987,7059,8406,8594,8736,8998,9078,9477,9836,9896,10347,11008,11261,11348,11425,11480,11551,11970,12160,12492,12623,12776,12842,12914,13093,13223,13281,13545,13941,14008,14183,14342,15595,17498,17582,17654,17733,17815,19224,19692,19786,19881,19930,20223,20298,20355,21657"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "63", "endOffsets": "114"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "11136", "endColumns": "63", "endOffsets": "11195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,130,214,279,345,405,470", "endColumns": "74,83,64,65,59,64,61", "endOffsets": "125,209,274,340,400,465,527"}}]}]}
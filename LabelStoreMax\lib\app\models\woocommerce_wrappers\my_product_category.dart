//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import 'my_product_category_image.dart';

class MyProductCategory {
  final int id;
  final String name;
  final String slug;
  final int parent;
  final int count;
  final String? description; // Can be null from API
  final int? menuOrder; // Can be null from API
  final MyProductCategoryImage? image; // Confirmed nullable

  MyProductCategory({
    required this.id,
    required this.name,
    required this.slug,
    required this.parent,
    required this.count,
    this.description,
    this.menuOrder,
    this.image,
  });

  factory MyProductCategory.fromJson(Map<String, dynamic> json) {
    return MyProductCategory(
      id: json['id'] ?? 0,
      name: json['name'] as String? ?? 'Untitled Category',
      slug: json['slug'] as String? ?? '',
      parent: json['parent'] ?? 0,
      count: json['count'] ?? 0,
      description: json['description'] as String?,
      menuOrder: json['menu_order'] as int?,
      image: json['image'] != null ? MyProductCategoryImage.fromJson(json['image']) : null,
    );
  }

  factory MyProductCategory.fromWooProductCategory(WooProductCategory wooCategory) {
    return MyProductCategory(
      id: wooCategory.id ?? 0,
      name: wooCategory.name ?? 'Untitled Category',
      slug: wooCategory.slug ?? '',
      parent: wooCategory.parent ?? 0,
      count: wooCategory.count ?? 0,
      description: wooCategory.description,
      menuOrder: wooCategory.menuOrder,
      image: wooCategory.image != null
          ? MyProductCategoryImage.fromWooCategoryImage(wooCategory.image!)
          : null,
    );
  }

  // Helper method to get safe description
  String getSafeDescription({String fallback = ''}) {
    return description ?? fallback;
  }

  // Helper method to get safe menu order
  int getSafeMenuOrder({int fallback = 0}) {
    return menuOrder ?? fallback;
  }
}

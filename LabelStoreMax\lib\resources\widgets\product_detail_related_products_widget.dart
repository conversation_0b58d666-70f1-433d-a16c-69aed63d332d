//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/pages/product_detail_page.dart';
import '/resources/widgets/product_item_container_widget.dart';
import '/app/services/woocommerce_service.dart';
import 'package:nylo_framework/nylo_framework.dart';

import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/models/app_config.dart';

class ProductDetailRelatedProductsWidget extends StatefulWidget {
  const ProductDetailRelatedProductsWidget(
      {super.key, required this.product, required this.appConfig});

  final MyProduct? product;
  final AppConfig? appConfig;

  @override
  State<ProductDetailRelatedProductsWidget> createState() =>
      _ProductDetailRelatedProductsWidgetState();
}

class _ProductDetailRelatedProductsWidgetState
    extends State<ProductDetailRelatedProductsWidget> {
  bool hasRelatedProducts = true;

  @override
  Widget build(BuildContext context) {
    if (widget.appConfig?.showRelatedProducts == false) {
      return SizedBox.shrink();
    }

    if (hasRelatedProducts == false) {
      return SizedBox.shrink();
    }

    return ListView(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      children: [
        Container(
          height: 50,
          padding: EdgeInsets.symmetric(vertical: 4, horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Text(
                trans("Related products"),
                style: Theme.of(context)
                    .textTheme
                    .bodySmall!
                    .copyWith(fontSize: 18),
                textAlign: TextAlign.left,
              ),
            ],
          ),
        ),
        SizedBox(
          height: 300,
          child: NyFutureBuilder<List<MyProduct>>(
            future: fetchRelated(),
            child: (context, relatedProducts) {
              if (relatedProducts == null) return SizedBox.shrink();

              if (relatedProducts.isEmpty) {
                return SizedBox.shrink();
              }
              return ListView(
                shrinkWrap: true,
                scrollDirection: Axis.horizontal,
                children: relatedProducts
                    .map(
                      (product) => SizedBox(
                        width: MediaQuery.of(context).size.width / 2.2,
                        child: ProductItemContainer(
                          product: product,
                          onTap: () {
                            routeTo(ProductDetailPage.path, data: product);
                          },
                        ),
                      ),
                    )
                    .toList(),
              );
            },
          ),
        ),
      ],
    );
  }

  Future<List<MyProduct>> fetchRelated() async {
    if (widget.product?.id == null) {
      setState(() {
        hasRelatedProducts = false;
      });
      return [];
    }

    try {
      print('🔍 Fetching related products for product ${widget.product!.id}...');

      final wooCommerceService = WooCommerceService();
      List<MyProduct> products = [];

      // Strategy 1: Fetch products from the same categories
      if (widget.product!.categories?.isNotEmpty == true) {
        // Get category IDs from the current product
        List<int> categoryIds = widget.product!.categories!
            .map((category) => category.id)
            .toList();

        print('📂 Searching in categories: $categoryIds');

        // Fetch products from the same categories
        products = await wooCommerceService.getProducts(
          perPage: 10,
          category: categoryIds.first, // Use first category for now
        );

        // Remove the current product from related products
        products.removeWhere((product) => product.id == widget.product!.id);

        print('✅ Found ${products.length} related products from categories');
      }

      // Strategy 2: If no category-based products found, get recent products
      if (products.isEmpty) {
        print('📦 No category-based products found, fetching recent products...');

        products = await wooCommerceService.getProducts(
          perPage: 6,
        );

        // Remove the current product from related products
        products.removeWhere((product) => product.id == widget.product!.id);

        print('✅ Found ${products.length} recent products as related');
      }

      // Limit to maximum 5 related products
      if (products.length > 5) {
        products = products.take(5).toList();
      }

      if (products.isEmpty) {
        setState(() {
          hasRelatedProducts = false;
        });
        return [];
      }

      print('✅ Returning ${products.length} related products');
      return products;

    } catch (e) {
      print('❌ Error fetching related products: $e');
      setState(() {
        hasRelatedProducts = false;
      });
      return [];
    }
  }
}

//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/pages/account_detail_page.dart';
import '/resources/pages/account_delete_page.dart';
import '/resources/pages/account_profile_update_page.dart';
import '/resources/pages/account_shipping_details_page.dart';
import '/app/events/logout_event.dart';
import '/app/models/woocommerce_wrappers/my_woo_customer.dart';
import 'package:nylo_framework/nylo_framework.dart';

class AccountDetailSettingsWidget extends StatefulWidget {
  final MyWooCustomer? customer;

  const AccountDetailSettingsWidget({super.key, this.customer});

  @override
  State<AccountDetailSettingsWidget> createState() =>
      _AccountDetailSettingsWidgetState();
}

class _AccountDetailSettingsWidgetState
    extends NyState<AccountDetailSettingsWidget> {

  /// Check if customer is eligible for account deletion
  /// Hide delete option for customers less than 3 months old
  bool _canDeleteAccount() {
    if (widget.customer?.dateCreated == null) {
      // If we don't have registration date, allow deletion for safety
      return true;
    }

    DateTime registrationDate = widget.customer!.dateCreated!;
    DateTime now = DateTime.now();
    Duration accountAge = now.difference(registrationDate);

    // Hide delete option for customers less than 3 months (90 days) old
    return accountAge.inDays >= 90;
  }

  @override
  Widget view(BuildContext context) {
    List<Widget> settingsItems = [
      Card(
        child: ListTile(
            leading: Icon(Icons.account_circle),
            title: Text(trans("Update details")),
            onTap: () =>
                routeTo(AccountProfileUpdatePage.path, onPop: (value) {
                  StateAction.refreshPage(
                      AccountDetailPage.path.nyPageName());
                })),
      ),
      Card(
        child: ListTile(
          leading: Icon(Icons.local_shipping),
          title: Text(trans("Billing/shipping details")),
          onTap: () => routeTo(AccountShippingDetailsPage.path),
        ),
      ),
    ];

    // Conditionally add Delete Account option for customers older than 3 months
    if (_canDeleteAccount()) {
      settingsItems.add(
        Card(
          child: ListTile(
            leading: Icon(Icons.no_accounts_rounded),
            title: Text(trans("Delete Account")),
            onTap: () => routeTo(AccountDeletePage.path),
          ),
        ),
      );
    }

    // Always add Logout option
    settingsItems.add(
      Card(
        child: ListTile(
          leading: Icon(Icons.exit_to_app),
          title: Text(trans("Logout")),
          onTap: () {
            confirmAction(() {
              event<LogoutEvent>();
            }, title: "Are you sure?".tr());
          },
        ),
      ),
    );

    return ListView(children: settingsItems);
  }
}

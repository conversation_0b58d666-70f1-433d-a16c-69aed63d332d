[{"merged": "com.flutter.stripe.stripe_android-release-91:/color-v23/abc_tint_seek_thumb.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-5:/color-v23/abc_tint_seek_thumb.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/color-v23/abc_tint_default.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-5:/color-v23/abc_tint_default.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/color-v23/abc_btn_colored_text_material.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-5:/color-v23/abc_btn_colored_text_material.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/color-v23/abc_btn_colored_borderless_text_material.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-5:/color-v23/abc_btn_colored_borderless_text_material.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/color-v23/abc_color_highlight_material.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-5:/color-v23/abc_color_highlight_material.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/color-v23/abc_tint_spinner.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-5:/color-v23/abc_tint_spinner.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/color-v23/abc_tint_switch_track.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-5:/color-v23/abc_tint_switch_track.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/color-v23/abc_tint_btn_checkable.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-5:/color-v23/abc_tint_btn_checkable.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/color-v23/abc_tint_edittext.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-5:/color-v23/abc_tint_edittext.xml"}]
//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:nylo_framework/nylo_framework.dart';
import 'package:wp_json_api/wp_json_api.dart';
import '/bootstrap/app_helper.dart';

class WPJsonAPIProvider extends NyProvider {
  @override
  boot(Nylo nylo) async {
    print('🌐 ===== WP JSON API PROVIDER: BOOT METHOD CALLED =====');
    print('🌐 ===== WP JSON API PROVIDER: INITIALIZATION =====');

    // Get configuration from AppConfig
    final appConfig = AppHelper.instance.appConfig;

    print('🔍 AppConfig: $appConfig');
    print('🔍 wpLoginEnabled: ${appConfig?.wpLoginEnabled}');
    print('🔍 wpLoginBaseUrl: ${appConfig?.wpLoginBaseUrl}');
    print('🔍 wpLoginWpApiPath: ${appConfig?.wpLoginWpApiPath}');

    if (appConfig?.wpLoginEnabled == 1) {
      String baseUrl = appConfig?.wpLoginBaseUrl ?? 'https://velvete.ly';
      String wpApiPath = appConfig?.wpLoginWpApiPath ?? '/wp-json/wp/v2';
      
      print('📍 WordPress Login Enabled: ${appConfig?.wpLoginEnabled}');
      print('🔧 Base URL: $baseUrl');
      print('🔧 WP API Path: $wpApiPath');
      print('🔧 Full API URL: $baseUrl$wpApiPath');
      print('===============================================');
      
      try {
        // Get the full API URL from environment or construct it
        String fullApiUrl = getEnv('API_BASE_URL', defaultValue: '$baseUrl$wpApiPath');

        print('🔧 Full API URL from env: $fullApiUrl');
        print('🔧 Attempting WPJsonAPI initialization...');

        // CRITICAL FIX: Use the official wp_json_api initialization method
        print('🔧 Environment API_BASE_URL: ${getEnv('API_BASE_URL')}');
        print('🔧 Initializing WPJsonAPI with official init() method...');

        try {
          // Use the correct initialization method from wp_json_api documentation
          WPJsonAPI.instance.init(baseUrl: fullApiUrl);
          print('✅ WPJsonAPI.instance.init() called successfully with: $fullApiUrl');
        } catch (initError) {
          print('❌ WPJsonAPI.instance.init() failed: $initError');
          rethrow;
        }

        // Access the instance after proper initialization
        var wpJsonApiInstance = WPJsonAPI.instance;

        print('🌐 ===== WP JSON API PROVIDER: SUCCESS =====');
        print('✅ WPJsonAPI instance accessed successfully');
        print('📍 Base URL from config: $baseUrl');
        print('📍 API Path from config: $wpApiPath');
        print('📍 Full API URL: $fullApiUrl');
        print('📍 Instance: $wpJsonApiInstance');
        print('🔍 WPJsonAPI.instance hashCode: ${wpJsonApiInstance.hashCode}');
        print('===============================================');

      } catch (e, stackTrace) {
        print('🌐 ===== WP JSON API PROVIDER: ERROR =====');
        print('❌ FAILED - Exception during WPJsonAPI initialization');
        print('📋 Exception: $e');
        print('📋 Stack Trace: $stackTrace');
        print('===============================================');
        rethrow;
      }
    } else {
      print('🌐 ===== WP JSON API PROVIDER: DISABLED =====');
      print('⚠️  WordPress login is disabled in AppConfig');
      print('📍 wpLoginEnabled: ${appConfig?.wpLoginEnabled}');
      print('===============================================');
    }
    
    return nylo;
  }
  
  @override
  afterBoot(Nylo nylo) async {
    // Any post-initialization logic can go here
  }
}

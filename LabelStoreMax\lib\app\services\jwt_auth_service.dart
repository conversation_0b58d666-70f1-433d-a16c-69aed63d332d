//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'dart:convert';

import 'package:nylo_framework/nylo_framework.dart';


/// JWT Authentication Service for WordPress/WooCommerce integration
class JWTAuthService {
  static final JWTAuthService _instance = JWTAuthService._internal();
  factory JWTAuthService() => _instance;
  JWTAuthService._internal();

  late Dio _dio;
  String? _jwtToken;
  Map<String, dynamic>? _currentUser;
  late String _baseUrl;
  late String _jwtApiUrl;

  /// Initialize JWT Auth Service
  void initialize() {
    // Get base URL from environment
    _baseUrl = getEnv('WOO_BASE_URL', defaultValue: 'https://velvete.ly');
    _jwtApiUrl = '$_baseUrl/wp-json/jwt-auth/v1/';

    print('=== JWT Auth Service Initialization ===');
    print('Base URL: $_baseUrl');
    print('JWT API URL: $_jwtApiUrl');
    print('======================================');

    _dio = Dio();
    _dio.options.baseUrl = _jwtApiUrl;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);

    // Add interceptor for automatic token inclusion
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        if (_jwtToken != null) {
          options.headers['Authorization'] = 'Bearer $_jwtToken';
        }
        handler.next(options);
      },
      onError: (DioException e, ErrorInterceptorHandler handler) {
        if (e.response?.statusCode == 403) {
          print('🚨 JWT AUTH INTERCEPTOR ERROR: 403 Forbidden - ${e.requestOptions.path} - ${e.response?.data}');
        }
        return handler.next(e); // Continue processing the error
      },
    ));

    print('✅ JWT Auth Service initialized with dynamic URL: $_jwtApiUrl');
  }

  /// Login with email and password
  Future<JWTAuthResult> login({
    required String email,
    required String password,
  }) async {
    print('=== JWT Login Attempt ===');
    print('Email: $email');
    print('Extracted Username: ${email.split('@')[0]}');
    
    try {
      final loginUrl = '${_jwtApiUrl}token';
      // Extract username from email (part before @)
      String username = email.split('@')[0];
      final loginData = {
        'username': username,
        'password': password,
      };

      // LOG FULL LOGIN REQUEST
      print('🌐 ===== JWT LOGIN API REQUEST =====');
      print('📍 URL: $loginUrl');
      print('🔧 METHOD: POST');
      print('📦 REQUEST PAYLOAD:');
      loginData.forEach((key, value) {
        print('   $key: $value');
      });
      print('==================================');

      final response = await _dio.post('token', data: loginData);

      // LOG FULL LOGIN RESPONSE
      print('🌐 ===== JWT LOGIN API RESPONSE =====');
      print('📍 URL: $loginUrl');
      print('📊 STATUS CODE: ${response.statusCode}');
      print('📋 RESPONSE HEADERS: ${response.headers}');
      print('📦 RESPONSE BODY:');
      print('${response.data}');
      print('===================================');

      if (response.statusCode == 200) {
        final data = response.data;

        _jwtToken = data['token'];

        // Parse the actual response structure - user info is at root level
        _currentUser = {
          'user_email': data['user_email'],
          'user_nicename': data['user_nicename'],
          'user_display_name': data['user_display_name'],
        };

        // Extract user ID from JWT token payload
        int? userId = _extractUserIdFromJWT(_jwtToken!);
        _currentUser!['id'] = userId ?? 0;
        
        // Store token securely
        await NyStorage.save('jwt_token', _jwtToken);
        await NyStorage.save('user_data', jsonEncode(_currentUser));
        
        print('✅ JWT Login successful');
        print('User: ${_currentUser?['user_display_name']}');
        
        return JWTAuthResult(
          success: true,
          token: _jwtToken!,
          user: JWTUser.fromJson(_currentUser!),
          message: 'Login successful',
        );
      } else {
        print('❌ JWT Login failed: ${response.statusCode}');
        return JWTAuthResult(
          success: false,
          message: 'Login failed: ${response.statusMessage}',
        );
      }
    } catch (e) {
      print('❌ JWT Login error: $e');
      return JWTAuthResult(
        success: false,
        message: 'Login error: ${e.toString()}',
      );
    }
  }

  /// Register new user
  Future<JWTAuthResult> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phoneNumber,
    String? city,
  }) async {
    print('=== JWT Registration Attempt ===');
    print('Email: $email, Name: $firstName $lastName');
    
    try {
      // Use pure WordPress user registration (no Ultimate Member)
      final registrationUrl = '$_baseUrl/wp-json/wp/v2/users';
      final registrationData = {
        'username': email.split('@')[0], // Use email prefix as username
        'email': email,
        'password': password,
        'first_name': firstName,
        'last_name': lastName,
        'meta': {
          'phone_number': phoneNumber ?? '',
          'city': city ?? '',
        },
      };

      // LOG FULL REGISTRATION REQUEST
      print('🌐 ===== PURE WORDPRESS REGISTRATION API REQUEST =====');
      print('📍 URL: $registrationUrl');
      print('🔧 METHOD: POST');
      print('📋 HEADERS: Content-Type: application/json');
      print('📦 REQUEST PAYLOAD:');
      registrationData.forEach((key, value) {
        if (key == 'meta') {
          print('   $key:');
          (value as Map).forEach((metaKey, metaValue) {
            print('     $metaKey: $metaValue');
          });
        } else {
          print('   $key: $value');
        }
      });
      print('====================================================');

      // Register user via WordPress REST API
      final registerResponse = await _dio.post(
        registrationUrl,
        data: registrationData,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      // LOG FULL REGISTRATION RESPONSE
      print('🌐 ===== PURE WORDPRESS REGISTRATION API RESPONSE =====');
      print('📍 URL: $registrationUrl');
      print('📊 STATUS CODE: ${registerResponse.statusCode}');
      print('📋 RESPONSE HEADERS: ${registerResponse.headers}');
      print('📦 RESPONSE BODY:');
      print('${registerResponse.data}');
      print('=====================================================');

      // WordPress REST API returns 201 for successful user creation
      if (registerResponse.statusCode == 201) {
        print('✅ WordPress user registration successful');

        // Now login to get JWT token
        return await login(email: email, password: password);
      } else {
        print('❌ WordPress user registration failed: ${registerResponse.statusCode}');
        return JWTAuthResult(
          success: false,
          message: 'Registration failed: ${registerResponse.statusMessage}',
        );
      }
    } catch (e) {
      print('❌ Registration error: $e');
      return JWTAuthResult(
        success: false,
        message: 'Registration error: ${e.toString()}',
      );
    }
  }

  /// Validate current JWT token
  Future<bool> validateToken() async {
    if (_jwtToken == null) {
      await _loadStoredToken();
    }
    
    if (_jwtToken == null) return false;
    
    try {
      final response = await _dio.post('token/validate');
      return response.statusCode == 200;
    } catch (e) {
      print('❌ Token validation failed: $e');
      return false;
    }
  }

  /// Logout user
  Future<void> logout() async {
    print('=== JWT Logout ===');
    
    _jwtToken = null;
    _currentUser = null;
    
    // Clear stored data
    await NyStorage.delete('jwt_token');
    await NyStorage.delete('user_data');
    
    print('✅ JWT Logout successful');
  }

  /// Get current user data
  JWTUser? getCurrentUser() {
    if (_currentUser != null) {
      return JWTUser.fromJson(_currentUser!);
    }
    return null;
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    if (_jwtToken == null) {
      await _loadStoredToken();
    }
    
    if (_jwtToken == null) return false;
    
    return await validateToken();
  }

  /// Load stored token from storage
  Future<void> _loadStoredToken() async {
    _jwtToken = await NyStorage.read('jwt_token');
    final userData = await NyStorage.read('user_data');
    
    if (userData != null) {
      try {
        _currentUser = jsonDecode(userData);
      } catch (e) {
        print('❌ Error parsing stored user data: $e');
      }
    }
  }

  /// Get JWT token
  String? getToken() => _jwtToken;

  /// Extract user ID from JWT token payload
  int? _extractUserIdFromJWT(String token) {
    try {
      // JWT tokens have 3 parts separated by dots: header.payload.signature
      List<String> parts = token.split('.');
      if (parts.length != 3) {
        print('❌ Invalid JWT token format');
        return null;
      }

      // Decode the payload (second part)
      String payload = parts[1];

      // Add padding if needed for base64 decoding
      while (payload.length % 4 != 0) {
        payload += '=';
      }

      // Decode base64 payload
      String decodedPayload = utf8.decode(base64Decode(payload));
      Map<String, dynamic> payloadData = jsonDecode(decodedPayload);

      print('🔍 JWT Payload decoded: $payloadData');

      // Extract user ID from the payload structure
      // Based on the terminal output, it should be at data.user.id
      if (payloadData['data'] != null &&
          payloadData['data']['user'] != null &&
          payloadData['data']['user']['id'] != null) {
        int userId = int.parse(payloadData['data']['user']['id'].toString());
        print('✅ Extracted user ID from JWT: $userId');
        return userId;
      }

      print('❌ Could not find user ID in JWT payload');
      return null;
    } catch (e) {
      print('❌ Error extracting user ID from JWT: $e');
      return null;
    }
  }

  /// Update user profile
  Future<JWTAuthResult> updateProfile({
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? city,
  }) async {
    if (!await isLoggedIn()) {
      return JWTAuthResult(
        success: false,
        message: 'User not logged in',
      );
    }
    
    try {
      final userId = _currentUser?['ID'];
      final response = await _dio.post(
        '$_baseUrl/wp-json/wp/v2/users/$userId',
        data: {
          if (firstName != null) 'first_name': firstName,
          if (lastName != null) 'last_name': lastName,
          'meta': {
            if (phoneNumber != null) 'phone_number': phoneNumber,
            if (city != null) 'city': city,
          },
        },
      );
      
      if (response.statusCode == 200) {
        // Update local user data
        _currentUser = response.data;
        await NyStorage.save('user_data', jsonEncode(_currentUser));
        
        return JWTAuthResult(
          success: true,
          user: JWTUser.fromJson(_currentUser!),
          message: 'Profile updated successfully',
        );
      } else {
        return JWTAuthResult(
          success: false,
          message: 'Profile update failed',
        );
      }
    } catch (e) {
      return JWTAuthResult(
        success: false,
        message: 'Profile update error: ${e.toString()}',
      );
    }
  }
}

/// JWT Authentication Result
class JWTAuthResult {
  final bool success;
  final String? token;
  final JWTUser? user;
  final String message;

  JWTAuthResult({
    required this.success,
    this.token,
    this.user,
    required this.message,
  });
}

/// JWT User Model
class JWTUser {
  final int id;
  final String email;
  final String displayName;
  final String firstName;
  final String lastName;
  final String? phoneNumber;
  final String? city;

  JWTUser({
    required this.id,
    required this.email,
    required this.displayName,
    required this.firstName,
    required this.lastName,
    this.phoneNumber,
    this.city,
  });

  factory JWTUser.fromJson(Map<String, dynamic> json) {
    return JWTUser(
      id: json['ID'] ?? json['id'] ?? 0,
      email: json['user_email'] ?? json['email'] ?? '',
      displayName: json['user_display_name'] ?? json['display_name'] ?? '',
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      phoneNumber: json['meta']?['phone_number'],
      city: json['meta']?['city'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'display_name': displayName,
      'first_name': firstName,
      'last_name': lastName,
      'phone_number': phoneNumber,
      'city': city,
    };
  }
}

<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<selector xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:app="http://schemas.android.com/apk/res-auto">
    <item android:state_enabled="false" android:color="?attr/colorControlNormal" app:alpha="?android:disabledAlpha"/>
    <item android:state_focused="true" android:color="?attr/colorControlActivated"/>
    <item android:state_pressed="true" android:color="?attr/colorControlActivated"/>
    <item android:state_activated="true" android:color="?attr/colorControlActivated"/>
    <item android:state_selected="true" android:color="?attr/colorControlActivated"/>
    <item android:state_checked="true" android:color="?attr/colorControlActivated"/>
    <item android:color="?attr/colorControlNormal"/>
</selector>
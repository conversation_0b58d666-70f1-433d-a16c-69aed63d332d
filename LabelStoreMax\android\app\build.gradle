plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.velvete.ly"
    compileSdk 35
    ndkVersion = "26.3.11579264"

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = 17
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.velvete.ly"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.debug
        }
    }

    // ADD THIS BLOCK INSIDE THE 'android { ... }' BLOCK, OR AT THE END OF THE FILE IF NO 'android' BLOCK EXISTS
    configurations.all {
        resolutionStrategy {
            // Force specific Kotlin standard library versions to ensure compatibility
            force 'org.jetbrains.kotlin:kotlin-stdlib:2.1.10'
            force 'org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.0'
            force 'org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.1.0'
            // Force the Kotlin Bill of Materials (BOM) to ensure consistent transitive dependencies
            force 'org.jetbrains.kotlin:kotlin-bom:2.1.0'
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.5'
}
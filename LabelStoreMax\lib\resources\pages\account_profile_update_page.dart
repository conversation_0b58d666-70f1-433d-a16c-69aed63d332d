//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/services/auth_service.dart';
import '/app/services/woocommerce_customer_service.dart';
import '/app/models/woocommerce_wrappers/my_woo_customer.dart';

class AccountProfileUpdatePage extends NyStatefulWidget {
  static RouteView path =
      ("/account-update", (_) => AccountProfileUpdatePage());

  AccountProfileUpdatePage({super.key})
      : super(child: () => _AccountProfileUpdatePageState());
}

class _AccountProfileUpdatePageState extends NyPage<AccountProfileUpdatePage> {
  final TextEditingController _tfFirstName = TextEditingController(),
      _tfLastName = TextEditingController();

  final WooCommerceCustomerService _customerService = WooCommerceCustomerService();

  @override
  get init => () async {
        await _fetchUserDetails();
      };

  _fetchUserDetails() async {
    print('🌐 ===== PROFILE UPDATE: FETCHING USER DETAILS (WOOCOMMERCE) =====');
    print('📍 API Call: WooCommerceCustomerService().getCurrentCustomer()');
    print('🔧 Expected Endpoint: WooCommerce Customer API');
    print('===============================================');

    try {
      MyWooCustomer? currentCustomer = await _customerService.getCurrentCustomer();

      if (currentCustomer != null) {
        print('🌐 ===== PROFILE UPDATE: USER DETAILS RESPONSE =====');
        print('✅ SUCCESS - Customer details retrieved');
        print('👤 Customer ID: ${currentCustomer.id}');
        print('👤 First Name: ${currentCustomer.firstName}');
        print('👤 Last Name: ${currentCustomer.lastName}');
        print('📧 Email: ${currentCustomer.email}');
        print('===============================================');

        _tfFirstName.text = currentCustomer.firstName ?? '';
        _tfLastName.text = currentCustomer.lastName ?? '';

        print('🌐 ===== PROFILE UPDATE: FIELDS POPULATED =====');
        print('✅ Text fields populated with customer data');
        print('===============================================');
      } else {
        print('🌐 ===== PROFILE UPDATE: NO CUSTOMER DATA =====');
        print('❌ No customer data found');
        print('===============================================');

        showToast(
          title: trans("Error"),
          description: trans("Unable to load user details"),
          style: ToastNotificationStyleType.danger,
        );
      }

    } catch (e, stackTrace) {
      print('🌐 ===== PROFILE UPDATE: ERROR FETCHING USER DETAILS =====');
      print('❌ FAILED - Exception during customer details fetch');
      print('📋 Exception: $e');
      print('📋 Stack Trace: $stackTrace');
      print('===============================================');

      showToast(
        title: trans("Error"),
        description: trans("Unable to load user details"),
        style: ToastNotificationStyleType.danger,
      );
    }
  }

  @override
  Widget view(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            trans("Update Details"),
            style: TextStyle(
              fontSize: 20,
            ),
          ),
          centerTitle: true,
          elevation: 1,
        ),
        body: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: Container(
                  margin: EdgeInsets.all(8),
                  padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Flexible(
                        child: Row(
                          children: [
                            Flexible(
                              child: TextEditingRow(
                                heading: trans("First Name"),
                                controller: _tfFirstName,
                                keyboardType: TextInputType.text,
                              ),
                            ),
                            Flexible(
                              child: TextEditingRow(
                                heading: trans("Last Name"),
                                controller: _tfLastName,
                                keyboardType: TextInputType.text,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 5),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 10),
                      ),
                      PrimaryButton(
                        title: trans("Update Details"),
                        isLoading: isLocked('update_account'),
                        action: _updateDetails,
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ));
  }

  _updateDetails() async {
    String firstName = _tfFirstName.text;
    String lastName = _tfLastName.text;

    validate(
        rules: {
          "first_name": [firstName, "not_empty"],
          "last_name": [lastName, "not_empty"]
        },
        onSuccess: () async {
          print('🌐 ===== PROFILE UPDATE: UPDATING USER DETAILS (WOOCOMMERCE) =====');
          print('📍 API Call: AuthService().updateProfile()');
          print('🔧 Expected Endpoint: WooCommerce Customer Update API');
          print('📦 Update Data:');
          print('   First Name: $firstName');
          print('   Last Name: $lastName');
          print('===============================================');

          try {
            AuthResult result = await AuthService().updateProfile(
              firstName: firstName,
              lastName: lastName,
            );

            print('🌐 ===== PROFILE UPDATE: UPDATE RESPONSE =====');
            print('✅ Update Result: ${result.success ? "SUCCESS" : "FAILED"}');
            print('📋 Message: ${result.message}');
            print('===============================================');

            if (result.success) {
              showToast(
                  title: trans("Success"),
                  description: trans("Account updated"),
                  style: ToastNotificationStyleType.success);
              pop();
            } else {
              showToast(
                  title: trans("Error"),
                  description: result.message ?? trans("Something went wrong"),
                  style: ToastNotificationStyleType.danger);
            }
          } catch (e) {
            print('🌐 ===== PROFILE UPDATE: UPDATE ERROR =====');
            print('❌ FAILED - Exception during profile update');
            print('📋 Exception: $e');
            print('===============================================');

            showToast(
                title: trans("Error"),
                description: trans("Something went wrong"),
                style: ToastNotificationStyleType.danger);
          }
        },
        lockRelease: "update_account");
  }
}

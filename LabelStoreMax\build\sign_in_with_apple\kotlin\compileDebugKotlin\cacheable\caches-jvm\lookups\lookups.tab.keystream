  Activity android.app  Log android.app.Activity  SignInWithApplePlugin android.app.Activity  TAG android.app.Activity  finish android.app.Activity  intent android.app.Activity  onCreate android.app.Activity  packageManager android.app.Activity  packageName android.app.Activity  
startActivity android.app.Activity  startActivityForResult android.app.Activity  Intent android.content  Log android.content.Context  SignInWithApplePlugin android.content.Context  TAG android.content.Context  Log android.content.ContextWrapper  SignInWithApplePlugin android.content.ContextWrapper  TAG android.content.ContextWrapper  packageManager android.content.ContextWrapper  packageName android.content.ContextWrapper  FLAG_ACTIVITY_CLEAR_TOP android.content.Intent  data android.content.Intent  flags android.content.Intent  
setPackage android.content.Intent  getLaunchIntentForPackage !android.content.pm.PackageManager  Uri android.net  parse android.net.Uri  toString android.net.Uri  Bundle 
android.os  Log  android.view.ContextThemeWrapper  SignInWithApplePlugin  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  NonNull androidx.annotation  CustomTabsIntent androidx.browser.customtabs  Builder ,androidx.browser.customtabs.CustomTabsIntent  intent ,androidx.browser.customtabs.CustomTabsIntent  startAnimationBundle ,androidx.browser.customtabs.CustomTabsIntent  build 4androidx.browser.customtabs.CustomTabsIntent.Builder  Activity -com.aboutyou.dart_packages.sign_in_with_apple  
ActivityAware -com.aboutyou.dart_packages.sign_in_with_apple  ActivityPluginBinding -com.aboutyou.dart_packages.sign_in_with_apple  ActivityResultListener -com.aboutyou.dart_packages.sign_in_with_apple  Boolean -com.aboutyou.dart_packages.sign_in_with_apple  Bundle -com.aboutyou.dart_packages.sign_in_with_apple  CustomTabsIntent -com.aboutyou.dart_packages.sign_in_with_apple  
FlutterPlugin -com.aboutyou.dart_packages.sign_in_with_apple  Int -com.aboutyou.dart_packages.sign_in_with_apple  Intent -com.aboutyou.dart_packages.sign_in_with_apple  Log -com.aboutyou.dart_packages.sign_in_with_apple  
MethodCall -com.aboutyou.dart_packages.sign_in_with_apple  MethodCallHandler -com.aboutyou.dart_packages.sign_in_with_apple  
MethodChannel -com.aboutyou.dart_packages.sign_in_with_apple  NonNull -com.aboutyou.dart_packages.sign_in_with_apple  Result -com.aboutyou.dart_packages.sign_in_with_apple  SignInWithAppleCallback -com.aboutyou.dart_packages.sign_in_with_apple  SignInWithApplePlugin -com.aboutyou.dart_packages.sign_in_with_apple  String -com.aboutyou.dart_packages.sign_in_with_apple  TAG -com.aboutyou.dart_packages.sign_in_with_apple  Unit -com.aboutyou.dart_packages.sign_in_with_apple  Uri -com.aboutyou.dart_packages.sign_in_with_apple  lastAuthorizationRequestResult -com.aboutyou.dart_packages.sign_in_with_apple  (triggerMainActivityToHideChromeCustomTab -com.aboutyou.dart_packages.sign_in_with_apple  FlutterPluginBinding ;com.aboutyou.dart_packages.sign_in_with_apple.FlutterPlugin  Log Ecom.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback  SignInWithApplePlugin Ecom.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback  TAG Ecom.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback  finish Ecom.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback  intent Ecom.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback  ActivityPluginBinding Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  Boolean Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  CUSTOM_TABS_REQUEST_CODE Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  	Companion Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  CustomTabsIntent Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  
FlutterPlugin Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  Int Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  Intent Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  
MethodCall Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  
MethodChannel Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  NonNull Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  Result Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  String Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  Unit Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  Uri Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  binding Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  channel Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  lastAuthorizationRequestResult Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  onAttachedToActivity Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  onDetachedFromActivity Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  (triggerMainActivityToHideChromeCustomTab Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  CustomTabsIntent Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  Intent Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  
MethodChannel Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  Uri Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  lastAuthorizationRequestResult Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  (triggerMainActivityToHideChromeCustomTab Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  FlutterPluginBinding Qcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.FlutterPlugin  Log 
io.flutter  e io.flutter.Log  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  addActivityResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  removeActivityResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  ActivityResultListener 'io.flutter.plugin.common.PluginRegistry  	Function0 kotlin  Nothing kotlin  invoke kotlin.Function0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    
//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/pages/product_detail_page.dart';
import '/resources/widgets/product_item_container_widget.dart';
import '/app/services/woocommerce_service.dart';
import 'package:nylo_framework/nylo_framework.dart';

import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/models/app_config.dart';

class ProductDetailUpsellWidget extends StatefulWidget {
  const ProductDetailUpsellWidget(
      {super.key, required this.productIds, required this.appConfig});
  final List<int>? productIds;
  final AppConfig? appConfig;

  @override
  createState() => _ProductDetailUpsellWidgetState();
}

class _ProductDetailUpsellWidgetState
    extends NyState<ProductDetailUpsellWidget> {
  @override
  Widget view(BuildContext context) {
    if ((widget.productIds?.isEmpty ?? false) ||
        widget.appConfig?.showUpsellProducts == false) {
      return SizedBox.shrink();
    }

    return ListView(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      children: [
        Container(
          height: 50,
          padding: EdgeInsets.symmetric(vertical: 4, horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Text(
                trans("${trans('You may also like')}…"),
                style: Theme.of(context)
                    .textTheme
                    .bodySmall!
                    .copyWith(fontSize: 18),
                textAlign: TextAlign.left,
              ),
            ],
          ),
        ),
        SizedBox(
            height: 300,
            child: NyListView(
                shrinkWrap: true,
                scrollDirection: Axis.horizontal,
                child: (context, product) {
                  return SizedBox(
                    width: MediaQuery.of(context).size.width / 2.2,
                    child: ProductItemContainer(
                      product: product,
                      onTap: () {
                        routeTo(ProductDetailPage.path, data: product);
                      },
                    ),
                  );
                },
                data: () async {
                  if (widget.productIds?.isEmpty ?? true) {
                    return <MyProduct>[];
                  }

                  try {
                    print('🛍️ Fetching upsell products: ${widget.productIds}');

                    final wooCommerceService = WooCommerceService();

                    // Fetch products by their IDs
                    List<MyProduct> upsellProducts = await wooCommerceService.getProducts(
                      include: widget.productIds!,
                      perPage: widget.productIds!.length,
                    );

                    print('✅ Found ${upsellProducts.length} upsell products');

                    return upsellProducts;

                  } catch (e) {
                    print('❌ Error fetching upsell products: $e');
                    return <MyProduct>[];
                  }
                })),
      ],
    );
  }
}

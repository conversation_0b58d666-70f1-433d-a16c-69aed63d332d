<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2021 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
-->
<selector xmlns:android="http://schemas.android.com/apk/res/android">
  <!-- Disabled -->
  <item android:alpha="@dimen/m3_comp_radio_button_disabled_unselected_icon_opacity"
      android:color="@macro/m3_comp_radio_button_disabled_unselected_icon_color"
      android:state_enabled="false"/>
  <item android:alpha="@dimen/m3_comp_radio_button_disabled_selected_icon_opacity"
      android:color="@macro/m3_comp_radio_button_disabled_selected_icon_color"
      android:state_enabled="false" android:state_pressed="true"/>

  <!-- Checked -->
  <item android:color="@macro/m3_comp_radio_button_selected_pressed_icon_color"
      android:state_pressed="true" android:state_checked="true"/>
  <item android:color="@macro/m3_comp_radio_button_selected_focus_icon_color"
      android:state_focused="true" android:state_checked="true"/>
  <item android:color="@macro/m3_comp_radio_button_selected_hover_icon_color"
      android:state_hovered="true" android:state_checked="true"/>
  <item android:color="@macro/m3_comp_radio_button_selected_icon_color"
      android:state_checked="true"/>

  <!-- Unchecked -->
  <item android:color="@macro/m3_comp_radio_button_unselected_pressed_icon_color"
      android:state_pressed="true" android:state_checked="true"/>
  <item android:color="@macro/m3_comp_radio_button_unselected_focus_icon_color"
      android:state_focused="true" android:state_checked="true"/>
  <item android:color="@macro/m3_comp_radio_button_unselected_hover_icon_color"
      android:state_hovered="true" android:state_checked="true"/>
  <item android:color="@macro/m3_comp_radio_button_unselected_icon_color"
      android:state_checked="false"/>
</selector>

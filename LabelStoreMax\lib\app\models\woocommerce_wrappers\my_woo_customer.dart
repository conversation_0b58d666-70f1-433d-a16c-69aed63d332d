//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';

/// Custom wrapper for WooCustomer with proper null safety handling
/// This wrapper handles null values gracefully to prevent crashes during JSON parsing
class MyWooCustomer {
  final int? id;
  final DateTime? dateCreated;
  final DateTime? dateModified;
  final String? email;
  final String? firstName;
  final String? lastName;
  final String? role;
  final String? username;
  final String? password;
  final WooBilling? billing;
  final WooShipping? shipping;
  final bool? isPayingCustomer;
  final String? avatarUrl;
  final List<WooMetaData> metaData;

  MyWooCustomer({
    this.id,
    this.dateCreated,
    this.dateModified,
    this.email,
    this.firstName,
    this.lastName,
    this.role,
    this.username,
    this.password,
    this.billing,
    this.shipping,
    this.isPayingCustomer,
    this.avatarUrl,
    required this.metaData,
  });

  /// Factory constructor with robust null safety handling
  factory MyWooCustomer.fromJson(Map<String, dynamic> json) {
    return MyWooCustomer(
      id: json['id'] as int?,
      dateCreated: json['date_created'] != null 
          ? DateTime.tryParse(json['date_created'].toString()) 
          : null,
      dateModified: json['date_modified'] != null 
          ? DateTime.tryParse(json['date_modified'].toString()) 
          : null,
      email: json['email'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      role: json['role'] as String?,
      username: json['username'] as String?,
      password: json['password'] as String?,
      billing: json['billing'] != null 
          ? WooBilling.fromJson(json['billing'] as Map<String, dynamic>) 
          : null,
      shipping: json['shipping'] != null 
          ? WooShipping.fromJson(json['shipping'] as Map<String, dynamic>) 
          : null,
      isPayingCustomer: json['is_paying_customer'] as bool?,
      avatarUrl: json['avatar_url'] as String?,
      // Handle meta_data list with null safety
      metaData: (json['meta_data'] as List<dynamic>?)
          ?.map<WooMetaData>((e) => WooMetaData.fromJson(e as Map<String, dynamic>))
          .toList() ?? <WooMetaData>[],
    );
  }

  /// Convert to standard WooCustomer for API calls
  WooCustomer toWooCustomer() {
    return WooCustomer(
      id: id,
      dateCreated: dateCreated,
      dateModified: dateModified,
      email: email,
      firstName: firstName,
      lastName: lastName,
      role: role,
      username: username,
      password: password,
      billing: billing,
      shipping: shipping,
      isPayingCustomer: isPayingCustomer,
      avatarUrl: avatarUrl,
      metaData: metaData,
    );
  }

  /// Factory constructor from WooCustomer
  factory MyWooCustomer.fromWooCustomer(WooCustomer wooCustomer) {
    return MyWooCustomer(
      id: wooCustomer.id,
      dateCreated: wooCustomer.dateCreated,
      dateModified: wooCustomer.dateModified,
      email: wooCustomer.email,
      firstName: wooCustomer.firstName,
      lastName: wooCustomer.lastName,
      role: wooCustomer.role,
      username: wooCustomer.username,
      password: wooCustomer.password,
      billing: wooCustomer.billing,
      shipping: wooCustomer.shipping,
      isPayingCustomer: wooCustomer.isPayingCustomer,
      avatarUrl: wooCustomer.avatarUrl,
      metaData: wooCustomer.metaData ?? <WooMetaData>[],
    );
  }

  /// Create a copy of this customer with updated fields
  MyWooCustomer copyWith({
    int? id,
    DateTime? dateCreated,
    DateTime? dateModified,
    String? email,
    String? firstName,
    String? lastName,
    String? role,
    String? username,
    String? password,
    WooBilling? billing,
    WooShipping? shipping,
    bool? isPayingCustomer,
    String? avatarUrl,
    List<WooMetaData>? metaData,
  }) {
    return MyWooCustomer(
      id: id ?? this.id,
      dateCreated: dateCreated ?? this.dateCreated,
      dateModified: dateModified ?? this.dateModified,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      role: role ?? this.role,
      username: username ?? this.username,
      password: password ?? this.password,
      billing: billing ?? this.billing,
      shipping: shipping ?? this.shipping,
      isPayingCustomer: isPayingCustomer ?? this.isPayingCustomer,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      metaData: metaData ?? this.metaData,
    );
  }

  /// Helper methods for safe access
  String getSafeEmail({String fallback = ''}) {
    return email ?? fallback;
  }

  String getSafeFirstName({String fallback = ''}) {
    return firstName ?? fallback;
  }

  String getSafeLastName({String fallback = ''}) {
    return lastName ?? fallback;
  }

  String getSafeUsername({String fallback = ''}) {
    return username ?? fallback;
  }

  String getFullName() {
    List<String> nameParts = [];
    if (firstName?.isNotEmpty == true) nameParts.add(firstName!);
    if (lastName?.isNotEmpty == true) nameParts.add(lastName!);
    return nameParts.join(' ');
  }

  bool hasValidId() {
    return id != null && id! > 0;
  }
}

{"logs": [{"outputFile": "app.meedu.flutter_facebook_auth-release-34:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,207,347,483,596,684,769,846,939,1032,1152,1263,1358,1453,1563,1694,1775,1863,2058,2152,2251,2370,2478", "endColumns": "151,139,135,112,87,84,76,92,92,119,110,94,94,109,130,80,87,194,93,98,118,107,160", "endOffsets": "202,342,478,591,679,764,841,934,1027,1147,1258,1353,1448,1558,1689,1770,1858,2053,2147,2246,2365,2473,2634"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3564,3716,3856,3992,4105,4193,4278,4355,4448,4541,4661,4772,4867,4962,5072,5203,5284,5372,5567,5661,5760,5879,5987", "endColumns": "151,139,135,112,87,84,76,92,92,119,110,94,94,109,130,80,87,194,93,98,118,107,160", "endOffsets": "3711,3851,3987,4100,4188,4273,4350,4443,4536,4656,4767,4862,4957,5067,5198,5279,5367,5562,5656,5755,5874,5982,6143"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\961aedd4c86fd3ace531ebe73cca998b\\transformed\\appcompat-1.1.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,906,997,1089,1183,1277,1378,1471,1566,1660,1751,1842,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,82,90,91,93,93,100,92,94,93,90,90,84,109,103,102,107,107,104,164,104,85", "endOffsets": "208,314,420,509,614,735,818,901,992,1084,1178,1272,1373,1466,1561,1655,1746,1837,1922,2032,2136,2239,2347,2455,2560,2725,2830,2916"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,906,997,1089,1183,1277,1378,1471,1566,1660,1751,1842,1927,2037,2141,2244,2352,2460,2565,2730,6148", "endColumns": "107,105,105,88,104,120,82,82,90,91,93,93,100,92,94,93,90,90,84,109,103,102,107,107,104,164,104,85", "endOffsets": "208,314,420,509,614,735,818,901,992,1084,1178,1272,1373,1466,1561,1655,1746,1837,1922,2032,2136,2239,2347,2455,2560,2725,2830,6229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "29,30,31,32,33,34,35,60", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2835,2934,3036,3138,3241,3342,3444,6234", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "2929,3031,3133,3236,3337,3439,3559,6330"}}]}, {"outputFile": "app.meedu.flutter_facebook_auth-mergeReleaseResources-32:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,207,347,483,596,684,769,846,939,1032,1152,1263,1358,1453,1563,1694,1775,1863,2058,2152,2251,2370,2478", "endColumns": "151,139,135,112,87,84,76,92,92,119,110,94,94,109,130,80,87,194,93,98,118,107,160", "endOffsets": "202,342,478,591,679,764,841,934,1027,1147,1258,1353,1448,1558,1689,1770,1858,2053,2147,2246,2365,2473,2634"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3564,3716,3856,3992,4105,4193,4278,4355,4448,4541,4661,4772,4867,4962,5072,5203,5284,5372,5567,5661,5760,5879,5987", "endColumns": "151,139,135,112,87,84,76,92,92,119,110,94,94,109,130,80,87,194,93,98,118,107,160", "endOffsets": "3711,3851,3987,4100,4188,4273,4350,4443,4536,4656,4767,4862,4957,5067,5198,5279,5367,5562,5656,5755,5874,5982,6143"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\961aedd4c86fd3ace531ebe73cca998b\\transformed\\appcompat-1.1.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,906,997,1089,1183,1277,1378,1471,1566,1660,1751,1842,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,82,90,91,93,93,100,92,94,93,90,90,84,109,103,102,107,107,104,164,104,85", "endOffsets": "208,314,420,509,614,735,818,901,992,1084,1178,1272,1373,1466,1561,1655,1746,1837,1922,2032,2136,2239,2347,2455,2560,2725,2830,2916"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,906,997,1089,1183,1277,1378,1471,1566,1660,1751,1842,1927,2037,2141,2244,2352,2460,2565,2730,6148", "endColumns": "107,105,105,88,104,120,82,82,90,91,93,93,100,92,94,93,90,90,84,109,103,102,107,107,104,164,104,85", "endOffsets": "208,314,420,509,614,735,818,901,992,1084,1178,1272,1373,1466,1561,1655,1746,1837,1922,2032,2136,2239,2347,2455,2560,2725,2830,6229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "29,30,31,32,33,34,35,60", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2835,2934,3036,3138,3241,3342,3444,6234", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "2929,3031,3133,3236,3337,3439,3559,6330"}}]}]}
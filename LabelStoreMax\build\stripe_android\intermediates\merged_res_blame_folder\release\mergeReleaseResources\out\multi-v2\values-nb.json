{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,87", "endOffsets": "140,228"}, "to": {"startLines": "473,474", "startColumns": "4,4", "startOffsets": "43897,43987", "endColumns": "89,87", "endOffsets": "43982,44070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5632", "endColumns": "129", "endOffsets": "5757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,963,1027,1107,1189,1259,1336,1403", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,958,1022,1102,1184,1254,1331,1398,1518"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,470,471,472", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4374,4467,6947,7044,7220,8035,8111,12744,12833,12915,12979,13121,13201,13525,43633,43710,43777", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "4462,4543,7039,7139,7303,8106,8194,12828,12910,12974,13038,13196,13278,13590,43705,43772,43892"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,215,294,387,473,551,628,749,842,941,1030,1150,1209,1330,1394,1571,1759,1899,1990,2093,2186,2287,2454,2562,2661,2891,2997,3123,3319,3533,3629,3742,3931,4025,4084,4148,4235,4332,4428,4505,4605,4694,4805,5029,5098,5165,5239,5346,5423,5512,5578,5659,5730,5806,5919,6005,6113,6203,6274,6362,6429,6515,6574,6676,6786,6891,7019,7080,7171,7267,7392,7463,7550,7651,7705,7763,7839,7945,8108,8377,8666,8762,8832,8919,8991,9097,9211,9298,9372,9441,9525,9609,9700,9821,9950,10023,10097,10149,10312,10381,10440,10514,10588,10666,10747,10888,11017,11131,11217,11292,11377,11445", "endColumns": "70,88,78,92,85,77,76,120,92,98,88,119,58,120,63,176,187,139,90,102,92,100,166,107,98,229,105,125,195,213,95,112,188,93,58,63,86,96,95,76,99,88,110,223,68,66,73,106,76,88,65,80,70,75,112,85,107,89,70,87,66,85,58,101,109,104,127,60,90,95,124,70,86,100,53,57,75,105,162,268,288,95,69,86,71,105,113,86,73,68,83,83,90,120,128,72,73,51,162,68,58,73,73,77,80,140,128,113,85,74,84,67,155", "endOffsets": "121,210,289,382,468,546,623,744,837,936,1025,1145,1204,1325,1389,1566,1754,1894,1985,2088,2181,2282,2449,2557,2656,2886,2992,3118,3314,3528,3624,3737,3926,4020,4079,4143,4230,4327,4423,4500,4600,4689,4800,5024,5093,5160,5234,5341,5418,5507,5573,5654,5725,5801,5914,6000,6108,6198,6269,6357,6424,6510,6569,6671,6781,6886,7014,7075,7166,7262,7387,7458,7545,7646,7700,7758,7834,7940,8103,8372,8661,8757,8827,8914,8986,9092,9206,9293,9367,9436,9520,9604,9695,9816,9945,10018,10092,10144,10307,10376,10435,10509,10583,10661,10742,10883,11012,11126,11212,11287,11372,11440,11596"}, "to": {"startLines": "164,165,166,236,248,249,250,267,280,282,283,313,331,333,336,340,341,342,345,347,349,350,351,352,353,354,355,356,357,358,359,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,408,412,422,423,424,425,426,427,428,429,430,439,440,441,442,443,444,445,446,447,448,449,450,451,452,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14237,14308,14397,19959,21736,21822,21900,23830,25034,25173,25272,27723,29446,29566,29809,30102,30279,30467,30758,30949,31145,31238,31339,31506,31614,31713,31943,32049,32175,32371,32585,32762,32875,33064,33158,33217,33281,33368,33465,33561,33638,33738,33827,33938,34162,34231,34298,34372,34479,34556,34645,34711,34792,34863,34939,35489,35575,35683,35773,35844,35932,35999,36085,36144,36246,36356,36461,36589,36650,36741,36837,36962,37210,37530,39073,39127,39185,39261,39367,39530,39799,40088,40184,40784,40871,40943,41049,41163,41250,41324,41393,41477,41561,41652,41773,41902,41975,42129,42181,42344,42413,42472,42546,42620,42698,42779,42920,43049,43163,43249,43324,43409,43477", "endColumns": "70,88,78,92,85,77,76,120,92,98,88,119,58,120,63,176,187,139,90,102,92,100,166,107,98,229,105,125,195,213,95,112,188,93,58,63,86,96,95,76,99,88,110,223,68,66,73,106,76,88,65,80,70,75,112,85,107,89,70,87,66,85,58,101,109,104,127,60,90,95,124,70,86,100,53,57,75,105,162,268,288,95,69,86,71,105,113,86,73,68,83,83,90,120,128,72,73,51,162,68,58,73,73,77,80,140,128,113,85,74,84,67,155", "endOffsets": "14303,14392,14471,20047,21817,21895,21972,23946,25122,25267,25356,27838,29500,29682,29868,30274,30462,30602,30844,31047,31233,31334,31501,31609,31708,31938,32044,32170,32366,32580,32676,32870,33059,33153,33212,33276,33363,33460,33556,33633,33733,33822,33933,34157,34226,34293,34367,34474,34551,34640,34706,34787,34858,34934,35047,35570,35678,35768,35839,35927,35994,36080,36139,36241,36351,36456,36584,36645,36736,36832,36957,37028,37292,37626,39122,39180,39256,39362,39525,39794,40083,40179,40249,40866,40938,41044,41158,41245,41319,41388,41472,41556,41647,41768,41897,41970,42044,42176,42339,42408,42467,42541,42615,42693,42774,42915,43044,43158,43244,43319,43404,43472,43628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4623,4729,4888,5014,5123,5279,5409,5529,5762,5916,6023,6184,6312,6454,6630,6697,6759", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "4724,4883,5009,5118,5274,5404,5524,5627,5911,6018,6179,6307,6449,6625,6692,6754,6832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,322,417,614,662,729,826,895,1124,1189,1287,1353,1408,1472,1546,1636,1931,2005,2071,2124,2177,2269,2395,2502,2559,2643,2719,2802,2867,2966,3242,3319,3396,3457,3517,3579,3639,3710,3790,3890,3983,4064,4167,4264,4337,4416,4501,4678,4870,4988,5117,5173,5833,5932,5997", "endColumns": "112,153,94,196,47,66,96,68,228,64,97,65,54,63,73,89,294,73,65,52,52,91,125,106,56,83,75,82,64,98,275,76,76,60,59,61,59,70,79,99,92,80,102,96,72,78,84,176,191,117,128,55,659,98,64,54", "endOffsets": "163,317,412,609,657,724,821,890,1119,1184,1282,1348,1403,1467,1541,1631,1926,2000,2066,2119,2172,2264,2390,2497,2554,2638,2714,2797,2862,2961,3237,3314,3391,3452,3512,3574,3634,3705,3785,3885,3978,4059,4162,4259,4332,4411,4496,4673,4865,4983,5112,5168,5828,5927,5992,6047"}, "to": {"startLines": "233,234,235,237,241,242,243,244,245,246,247,263,266,268,272,273,278,284,285,293,303,306,307,308,309,310,316,319,324,326,327,328,329,332,334,335,339,343,344,346,348,360,385,386,387,388,389,407,414,415,416,417,419,420,421,438", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19597,19710,19864,20052,21063,21111,21178,21275,21344,21573,21638,23524,23775,23951,24247,24321,24691,25361,25435,25964,26873,27077,27169,27295,27402,27459,28018,28256,28673,28815,28914,29190,29267,29505,29687,29747,30042,30607,30678,30849,31052,32681,35052,35155,35252,35325,35404,37033,37685,37877,37995,38124,38249,38909,39008,40729", "endColumns": "112,153,94,196,47,66,96,68,228,64,97,65,54,63,73,89,294,73,65,52,52,91,125,106,56,83,75,82,64,98,275,76,76,60,59,61,59,70,79,99,92,80,102,96,72,78,84,176,191,117,128,55,659,98,64,54", "endOffsets": "19705,19859,19954,20244,21106,21173,21270,21339,21568,21633,21731,23585,23825,24010,24316,24406,24981,25430,25496,26012,26921,27164,27290,27397,27454,27538,28089,28334,28733,28909,29185,29262,29339,29561,29742,29804,30097,30673,30753,30944,31140,32757,35150,35247,35320,35399,35484,37205,37872,37990,38119,38175,38904,39003,39068,40779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,262,330,407,465,524,577,653,730,811,910,1009,1095,1175,1253,1336,1433,1526,1595,1685,1772,1860,1969,2050,2140,2216,2313,2392,2488,2576,2665,2750,2851,2926,2999,3103,3200,3272,3337,4014,4666,4740,4855,4950,5005,5098,5183,5250,5338,5426,5483,5561,5610,5687,5793,5865,5940,6007,6073,6119,6198,6290,6362,6409,6457,6528,6586,6646,6826,6988,7112,7179,7263,7341,7438,7518,7600,7673,7762,7839,7941,8025,8110,8163,8295,8343,8397,8466,8532,8601,8665,8735,8823,8890,8941", "endColumns": "67,76,61,67,76,57,58,52,75,76,80,98,98,85,79,77,82,96,92,68,89,86,87,108,80,89,75,96,78,95,87,88,84,100,74,72,103,96,71,64,676,651,73,114,94,54,92,84,66,87,87,56,77,48,76,105,71,74,66,65,45,78,91,71,46,47,70,57,59,179,161,123,66,83,77,96,79,81,72,88,76,101,83,84,52,131,47,53,68,65,68,63,69,87,66,50,79", "endOffsets": "118,195,257,325,402,460,519,572,648,725,806,905,1004,1090,1170,1248,1331,1428,1521,1590,1680,1767,1855,1964,2045,2135,2211,2308,2387,2483,2571,2660,2745,2846,2921,2994,3098,3195,3267,3332,4009,4661,4735,4850,4945,5000,5093,5178,5245,5333,5421,5478,5556,5605,5682,5788,5860,5935,6002,6068,6114,6193,6285,6357,6404,6452,6523,6581,6641,6821,6983,7107,7174,7258,7336,7433,7513,7595,7668,7757,7834,7936,8020,8105,8158,8290,8338,8392,8461,8527,8596,8660,8730,8818,8885,8936,9016"}, "to": {"startLines": "157,158,159,160,161,162,163,167,168,169,170,173,175,176,178,183,187,202,205,206,207,209,210,211,213,217,218,219,220,221,222,223,224,225,226,228,231,232,238,239,240,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,274,275,276,277,281,286,287,288,289,290,295,296,297,298,299,300,304,305,314,315,317,318,322,323,325,330,337,338,409,410,411,413,418,431,432,433,434,435,436,437,453", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13768,13836,13913,13975,14043,14120,14178,14476,14529,14605,14682,14906,15083,15182,15335,15684,15971,16988,17232,17325,17394,17550,17637,17725,17894,18197,18287,18363,18460,18539,18635,18723,18812,18897,18998,19147,19396,19500,20249,20321,20386,21977,22629,22703,22818,22913,22968,23061,23146,23213,23301,23389,23446,24015,24064,24141,24411,24483,24558,24625,25127,25501,25580,25672,25744,25791,26081,26152,26210,26270,26450,26612,26926,26993,27843,27921,28094,28174,28511,28584,28738,29344,29873,29957,37297,37350,37482,37631,38180,40254,40320,40389,40453,40523,40611,40678,42049", "endColumns": "67,76,61,67,76,57,58,52,75,76,80,98,98,85,79,77,82,96,92,68,89,86,87,108,80,89,75,96,78,95,87,88,84,100,74,72,103,96,71,64,676,651,73,114,94,54,92,84,66,87,87,56,77,48,76,105,71,74,66,65,45,78,91,71,46,47,70,57,59,179,161,123,66,83,77,96,79,81,72,88,76,101,83,84,52,131,47,53,68,65,68,63,69,87,66,50,79", "endOffsets": "13831,13908,13970,14038,14115,14173,14232,14524,14600,14677,14758,15000,15177,15263,15410,15757,16049,17080,17320,17389,17479,17632,17720,17829,17970,18282,18358,18455,18534,18630,18718,18807,18892,18993,19068,19215,19495,19592,20316,20381,21058,22624,22698,22813,22908,22963,23056,23141,23208,23296,23384,23441,23519,24059,24136,24242,24478,24553,24620,24686,25168,25575,25667,25739,25786,25834,26147,26205,26265,26445,26607,26731,26988,27072,27916,28013,28169,28251,28579,28668,28810,29441,29952,30037,37345,37477,37525,37680,38244,40315,40384,40448,40518,40606,40673,40724,42124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,190,252,337,399,469,528,604,676,742,802", "endColumns": "77,56,61,84,61,69,58,75,71,65,59,68", "endOffsets": "128,185,247,332,394,464,523,599,671,737,797,866"}, "to": {"startLines": "172,182,184,185,186,190,198,201,204,208,212,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14828,15627,15762,15824,15909,16190,16720,16912,17160,17484,17834,18128", "endColumns": "77,56,61,84,61,69,58,75,71,65,59,68", "endOffsets": "14901,15679,15819,15904,15966,16255,16774,16983,17227,17545,17889,18192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,13283", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,13358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,206", "endColumns": "74,75,72", "endOffsets": "125,201,274"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4548,7144,7431", "endColumns": "74,75,72", "endOffsets": "4618,7215,7499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6837,7596,7697,7809", "endColumns": "109,100,111,96", "endOffsets": "6942,7692,7804,7901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,349,416,477,546,613,677,745,817,877,936,1009,1073,1143,1206,1281,1345,1434,1508,1593,1684,1782,1869,1917,1965,2042,2106,2173,2243,2337,2423,2511", "endColumns": "64,77,66,83,66,60,68,66,63,67,71,59,58,72,63,69,62,74,63,88,73,84,90,97,86,47,47,76,63,66,69,93,85,87,83", "endOffsets": "115,193,260,344,411,472,541,608,672,740,812,872,931,1004,1068,1138,1201,1276,1340,1429,1503,1588,1679,1777,1864,1912,1960,2037,2101,2168,2238,2332,2418,2506,2590"}, "to": {"startLines": "171,174,177,179,180,181,188,189,191,192,193,194,195,196,197,199,200,203,214,215,227,229,230,264,265,279,291,292,294,301,302,311,312,320,321", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14763,15005,15268,15415,15499,15566,16054,16123,16260,16324,16392,16464,16524,16583,16656,16779,16849,17085,17975,18039,19073,19220,19305,23590,23688,24986,25839,25887,26017,26736,26803,27543,27637,28339,28427", "endColumns": "64,77,66,83,66,60,68,66,63,67,71,59,58,72,63,69,62,74,63,88,73,84,90,97,86,47,47,76,63,66,69,93,85,87,83", "endOffsets": "14823,15078,15330,15494,15561,15622,16118,16185,16319,16387,16459,16519,16578,16651,16715,16844,16907,17155,18034,18123,19142,19300,19391,23683,23770,25029,25882,25959,26076,26798,26868,27632,27718,28422,28506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3348,3442,3544,3641,3740,3848,3954,13667", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3437,3539,3636,3735,3843,3949,4069,13763"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "81,475", "startColumns": "4,4", "startOffsets": "7906,44075", "endColumns": "60,74", "endOffsets": "7962,44145"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1025,1089,1181,1249,1309,1396,1460,1522,1586,1654,1719,1773,1882,1940,2002,2056,2131,2251,2333,2410,2500,2584,2664,2798,2876,2956,3079,3167,3245,3299,3350,3416,3484,3558,3629,3705,3776,3854,3924,3994,4094,4183,4261,4349,4439,4511,4583,4667,4718,4796,4862,4943,5026,5088,5152,5215,5284,5384,5488,5581,5681,5739,5794,5872,5956,6034", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "256,333,406,493,581,661,760,879,961,1020,1084,1176,1244,1304,1391,1455,1517,1581,1649,1714,1768,1877,1935,1997,2051,2126,2246,2328,2405,2495,2579,2659,2793,2871,2951,3074,3162,3240,3294,3345,3411,3479,3553,3624,3700,3771,3849,3919,3989,4089,4178,4256,4344,4434,4506,4578,4662,4713,4791,4857,4938,5021,5083,5147,5210,5279,5379,5483,5576,5676,5734,5789,5867,5951,6029,6101"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2943,3020,3093,3180,3268,4074,4173,4292,7308,7367,7504,7967,8199,8259,8346,8410,8472,8536,8604,8669,8723,8832,8890,8952,9006,9081,9201,9283,9360,9450,9534,9614,9748,9826,9906,10029,10117,10195,10249,10300,10366,10434,10508,10579,10655,10726,10804,10874,10944,11044,11133,11211,11299,11389,11461,11533,11617,11668,11746,11812,11893,11976,12038,12102,12165,12234,12334,12438,12531,12631,12689,13043,13363,13447,13595", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "306,3015,3088,3175,3263,3343,4168,4287,4369,7362,7426,7591,8030,8254,8341,8405,8467,8531,8599,8664,8718,8827,8885,8947,9001,9076,9196,9278,9355,9445,9529,9609,9743,9821,9901,10024,10112,10190,10244,10295,10361,10429,10503,10574,10650,10721,10799,10869,10939,11039,11128,11206,11294,11384,11456,11528,11612,11663,11741,11807,11888,11971,12033,12097,12160,12229,12329,12433,12526,12626,12684,12739,13116,13442,13520,13662"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,87", "endOffsets": "140,228"}, "to": {"startLines": "473,474", "startColumns": "4,4", "startOffsets": "43897,43987", "endColumns": "89,87", "endOffsets": "43982,44070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5632", "endColumns": "129", "endOffsets": "5757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,963,1027,1107,1189,1259,1336,1403", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,958,1022,1102,1184,1254,1331,1398,1518"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,470,471,472", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4374,4467,6947,7044,7220,8035,8111,12744,12833,12915,12979,13121,13201,13525,43633,43710,43777", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "4462,4543,7039,7139,7303,8106,8194,12828,12910,12974,13038,13196,13278,13590,43705,43772,43892"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,215,294,387,473,551,628,749,842,941,1030,1150,1209,1330,1394,1571,1759,1899,1990,2093,2186,2287,2454,2562,2661,2891,2997,3123,3319,3533,3629,3742,3931,4025,4084,4148,4235,4332,4428,4505,4605,4694,4805,5029,5098,5165,5239,5346,5423,5512,5578,5659,5730,5806,5919,6005,6113,6203,6274,6362,6429,6515,6574,6676,6786,6891,7019,7080,7171,7267,7392,7463,7550,7651,7705,7763,7839,7945,8108,8377,8666,8762,8832,8919,8991,9097,9211,9298,9372,9441,9525,9609,9700,9821,9950,10023,10097,10149,10312,10381,10440,10514,10588,10666,10747,10888,11017,11131,11217,11292,11377,11445", "endColumns": "70,88,78,92,85,77,76,120,92,98,88,119,58,120,63,176,187,139,90,102,92,100,166,107,98,229,105,125,195,213,95,112,188,93,58,63,86,96,95,76,99,88,110,223,68,66,73,106,76,88,65,80,70,75,112,85,107,89,70,87,66,85,58,101,109,104,127,60,90,95,124,70,86,100,53,57,75,105,162,268,288,95,69,86,71,105,113,86,73,68,83,83,90,120,128,72,73,51,162,68,58,73,73,77,80,140,128,113,85,74,84,67,155", "endOffsets": "121,210,289,382,468,546,623,744,837,936,1025,1145,1204,1325,1389,1566,1754,1894,1985,2088,2181,2282,2449,2557,2656,2886,2992,3118,3314,3528,3624,3737,3926,4020,4079,4143,4230,4327,4423,4500,4600,4689,4800,5024,5093,5160,5234,5341,5418,5507,5573,5654,5725,5801,5914,6000,6108,6198,6269,6357,6424,6510,6569,6671,6781,6886,7014,7075,7166,7262,7387,7458,7545,7646,7700,7758,7834,7940,8103,8372,8661,8757,8827,8914,8986,9092,9206,9293,9367,9436,9520,9604,9695,9816,9945,10018,10092,10144,10307,10376,10435,10509,10583,10661,10742,10883,11012,11126,11212,11287,11372,11440,11596"}, "to": {"startLines": "164,165,166,236,248,249,250,267,280,282,283,313,331,333,336,340,341,342,345,347,349,350,351,352,353,354,355,356,357,358,359,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,408,412,422,423,424,425,426,427,428,429,430,439,440,441,442,443,444,445,446,447,448,449,450,451,452,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14237,14308,14397,19959,21736,21822,21900,23830,25034,25173,25272,27723,29446,29566,29809,30102,30279,30467,30758,30949,31145,31238,31339,31506,31614,31713,31943,32049,32175,32371,32585,32762,32875,33064,33158,33217,33281,33368,33465,33561,33638,33738,33827,33938,34162,34231,34298,34372,34479,34556,34645,34711,34792,34863,34939,35489,35575,35683,35773,35844,35932,35999,36085,36144,36246,36356,36461,36589,36650,36741,36837,36962,37210,37530,39073,39127,39185,39261,39367,39530,39799,40088,40184,40784,40871,40943,41049,41163,41250,41324,41393,41477,41561,41652,41773,41902,41975,42129,42181,42344,42413,42472,42546,42620,42698,42779,42920,43049,43163,43249,43324,43409,43477", "endColumns": "70,88,78,92,85,77,76,120,92,98,88,119,58,120,63,176,187,139,90,102,92,100,166,107,98,229,105,125,195,213,95,112,188,93,58,63,86,96,95,76,99,88,110,223,68,66,73,106,76,88,65,80,70,75,112,85,107,89,70,87,66,85,58,101,109,104,127,60,90,95,124,70,86,100,53,57,75,105,162,268,288,95,69,86,71,105,113,86,73,68,83,83,90,120,128,72,73,51,162,68,58,73,73,77,80,140,128,113,85,74,84,67,155", "endOffsets": "14303,14392,14471,20047,21817,21895,21972,23946,25122,25267,25356,27838,29500,29682,29868,30274,30462,30602,30844,31047,31233,31334,31501,31609,31708,31938,32044,32170,32366,32580,32676,32870,33059,33153,33212,33276,33363,33460,33556,33633,33733,33822,33933,34157,34226,34293,34367,34474,34551,34640,34706,34787,34858,34934,35047,35570,35678,35768,35839,35927,35994,36080,36139,36241,36351,36456,36584,36645,36736,36832,36957,37028,37292,37626,39122,39180,39256,39362,39525,39794,40083,40179,40249,40866,40938,41044,41158,41245,41319,41388,41472,41556,41647,41768,41897,41970,42044,42176,42339,42408,42467,42541,42615,42693,42774,42915,43044,43158,43244,43319,43404,43472,43628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4623,4729,4888,5014,5123,5279,5409,5529,5762,5916,6023,6184,6312,6454,6630,6697,6759", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "4724,4883,5009,5118,5274,5404,5524,5627,5911,6018,6179,6307,6449,6625,6692,6754,6832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,322,417,614,662,729,826,895,1124,1189,1287,1353,1408,1472,1546,1636,1931,2005,2071,2124,2177,2269,2395,2502,2559,2643,2719,2802,2867,2966,3242,3319,3396,3457,3517,3579,3639,3710,3790,3890,3983,4064,4167,4264,4337,4416,4501,4678,4870,4988,5117,5173,5833,5932,5997", "endColumns": "112,153,94,196,47,66,96,68,228,64,97,65,54,63,73,89,294,73,65,52,52,91,125,106,56,83,75,82,64,98,275,76,76,60,59,61,59,70,79,99,92,80,102,96,72,78,84,176,191,117,128,55,659,98,64,54", "endOffsets": "163,317,412,609,657,724,821,890,1119,1184,1282,1348,1403,1467,1541,1631,1926,2000,2066,2119,2172,2264,2390,2497,2554,2638,2714,2797,2862,2961,3237,3314,3391,3452,3512,3574,3634,3705,3785,3885,3978,4059,4162,4259,4332,4411,4496,4673,4865,4983,5112,5168,5828,5927,5992,6047"}, "to": {"startLines": "233,234,235,237,241,242,243,244,245,246,247,263,266,268,272,273,278,284,285,293,303,306,307,308,309,310,316,319,324,326,327,328,329,332,334,335,339,343,344,346,348,360,385,386,387,388,389,407,414,415,416,417,419,420,421,438", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19597,19710,19864,20052,21063,21111,21178,21275,21344,21573,21638,23524,23775,23951,24247,24321,24691,25361,25435,25964,26873,27077,27169,27295,27402,27459,28018,28256,28673,28815,28914,29190,29267,29505,29687,29747,30042,30607,30678,30849,31052,32681,35052,35155,35252,35325,35404,37033,37685,37877,37995,38124,38249,38909,39008,40729", "endColumns": "112,153,94,196,47,66,96,68,228,64,97,65,54,63,73,89,294,73,65,52,52,91,125,106,56,83,75,82,64,98,275,76,76,60,59,61,59,70,79,99,92,80,102,96,72,78,84,176,191,117,128,55,659,98,64,54", "endOffsets": "19705,19859,19954,20244,21106,21173,21270,21339,21568,21633,21731,23585,23825,24010,24316,24406,24981,25430,25496,26012,26921,27164,27290,27397,27454,27538,28089,28334,28733,28909,29185,29262,29339,29561,29742,29804,30097,30673,30753,30944,31140,32757,35150,35247,35320,35399,35484,37205,37872,37990,38119,38175,38904,39003,39068,40779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,262,330,407,465,524,577,653,730,811,910,1009,1095,1175,1253,1336,1433,1526,1595,1685,1772,1860,1969,2050,2140,2216,2313,2392,2488,2576,2665,2750,2851,2926,2999,3103,3200,3272,3337,4014,4666,4740,4855,4950,5005,5098,5183,5250,5338,5426,5483,5561,5610,5687,5793,5865,5940,6007,6073,6119,6198,6290,6362,6409,6457,6528,6586,6646,6826,6988,7112,7179,7263,7341,7438,7518,7600,7673,7762,7839,7941,8025,8110,8163,8295,8343,8397,8466,8532,8601,8665,8735,8823,8890,8941", "endColumns": "67,76,61,67,76,57,58,52,75,76,80,98,98,85,79,77,82,96,92,68,89,86,87,108,80,89,75,96,78,95,87,88,84,100,74,72,103,96,71,64,676,651,73,114,94,54,92,84,66,87,87,56,77,48,76,105,71,74,66,65,45,78,91,71,46,47,70,57,59,179,161,123,66,83,77,96,79,81,72,88,76,101,83,84,52,131,47,53,68,65,68,63,69,87,66,50,79", "endOffsets": "118,195,257,325,402,460,519,572,648,725,806,905,1004,1090,1170,1248,1331,1428,1521,1590,1680,1767,1855,1964,2045,2135,2211,2308,2387,2483,2571,2660,2745,2846,2921,2994,3098,3195,3267,3332,4009,4661,4735,4850,4945,5000,5093,5178,5245,5333,5421,5478,5556,5605,5682,5788,5860,5935,6002,6068,6114,6193,6285,6357,6404,6452,6523,6581,6641,6821,6983,7107,7174,7258,7336,7433,7513,7595,7668,7757,7834,7936,8020,8105,8158,8290,8338,8392,8461,8527,8596,8660,8730,8818,8885,8936,9016"}, "to": {"startLines": "157,158,159,160,161,162,163,167,168,169,170,173,175,176,178,183,187,202,205,206,207,209,210,211,213,217,218,219,220,221,222,223,224,225,226,228,231,232,238,239,240,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,274,275,276,277,281,286,287,288,289,290,295,296,297,298,299,300,304,305,314,315,317,318,322,323,325,330,337,338,409,410,411,413,418,431,432,433,434,435,436,437,453", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13768,13836,13913,13975,14043,14120,14178,14476,14529,14605,14682,14906,15083,15182,15335,15684,15971,16988,17232,17325,17394,17550,17637,17725,17894,18197,18287,18363,18460,18539,18635,18723,18812,18897,18998,19147,19396,19500,20249,20321,20386,21977,22629,22703,22818,22913,22968,23061,23146,23213,23301,23389,23446,24015,24064,24141,24411,24483,24558,24625,25127,25501,25580,25672,25744,25791,26081,26152,26210,26270,26450,26612,26926,26993,27843,27921,28094,28174,28511,28584,28738,29344,29873,29957,37297,37350,37482,37631,38180,40254,40320,40389,40453,40523,40611,40678,42049", "endColumns": "67,76,61,67,76,57,58,52,75,76,80,98,98,85,79,77,82,96,92,68,89,86,87,108,80,89,75,96,78,95,87,88,84,100,74,72,103,96,71,64,676,651,73,114,94,54,92,84,66,87,87,56,77,48,76,105,71,74,66,65,45,78,91,71,46,47,70,57,59,179,161,123,66,83,77,96,79,81,72,88,76,101,83,84,52,131,47,53,68,65,68,63,69,87,66,50,79", "endOffsets": "13831,13908,13970,14038,14115,14173,14232,14524,14600,14677,14758,15000,15177,15263,15410,15757,16049,17080,17320,17389,17479,17632,17720,17829,17970,18282,18358,18455,18534,18630,18718,18807,18892,18993,19068,19215,19495,19592,20316,20381,21058,22624,22698,22813,22908,22963,23056,23141,23208,23296,23384,23441,23519,24059,24136,24242,24478,24553,24620,24686,25168,25575,25667,25739,25786,25834,26147,26205,26265,26445,26607,26731,26988,27072,27916,28013,28169,28251,28579,28668,28810,29441,29952,30037,37345,37477,37525,37680,38244,40315,40384,40448,40518,40606,40673,40724,42124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,190,252,337,399,469,528,604,676,742,802", "endColumns": "77,56,61,84,61,69,58,75,71,65,59,68", "endOffsets": "128,185,247,332,394,464,523,599,671,737,797,866"}, "to": {"startLines": "172,182,184,185,186,190,198,201,204,208,212,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14828,15627,15762,15824,15909,16190,16720,16912,17160,17484,17834,18128", "endColumns": "77,56,61,84,61,69,58,75,71,65,59,68", "endOffsets": "14901,15679,15819,15904,15966,16255,16774,16983,17227,17545,17889,18192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,13283", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,13358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,206", "endColumns": "74,75,72", "endOffsets": "125,201,274"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4548,7144,7431", "endColumns": "74,75,72", "endOffsets": "4618,7215,7499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6837,7596,7697,7809", "endColumns": "109,100,111,96", "endOffsets": "6942,7692,7804,7901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,349,416,477,546,613,677,745,817,877,936,1009,1073,1143,1206,1281,1345,1434,1508,1593,1684,1782,1869,1917,1965,2042,2106,2173,2243,2337,2423,2511", "endColumns": "64,77,66,83,66,60,68,66,63,67,71,59,58,72,63,69,62,74,63,88,73,84,90,97,86,47,47,76,63,66,69,93,85,87,83", "endOffsets": "115,193,260,344,411,472,541,608,672,740,812,872,931,1004,1068,1138,1201,1276,1340,1429,1503,1588,1679,1777,1864,1912,1960,2037,2101,2168,2238,2332,2418,2506,2590"}, "to": {"startLines": "171,174,177,179,180,181,188,189,191,192,193,194,195,196,197,199,200,203,214,215,227,229,230,264,265,279,291,292,294,301,302,311,312,320,321", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14763,15005,15268,15415,15499,15566,16054,16123,16260,16324,16392,16464,16524,16583,16656,16779,16849,17085,17975,18039,19073,19220,19305,23590,23688,24986,25839,25887,26017,26736,26803,27543,27637,28339,28427", "endColumns": "64,77,66,83,66,60,68,66,63,67,71,59,58,72,63,69,62,74,63,88,73,84,90,97,86,47,47,76,63,66,69,93,85,87,83", "endOffsets": "14823,15078,15330,15494,15561,15622,16118,16185,16319,16387,16459,16519,16578,16651,16715,16844,16907,17155,18034,18123,19142,19300,19391,23683,23770,25029,25882,25959,26076,26798,26868,27632,27718,28422,28506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3348,3442,3544,3641,3740,3848,3954,13667", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3437,3539,3636,3735,3843,3949,4069,13763"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "81,475", "startColumns": "4,4", "startOffsets": "7906,44075", "endColumns": "60,74", "endOffsets": "7962,44145"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1025,1089,1181,1249,1309,1396,1460,1522,1586,1654,1719,1773,1882,1940,2002,2056,2131,2251,2333,2410,2500,2584,2664,2798,2876,2956,3079,3167,3245,3299,3350,3416,3484,3558,3629,3705,3776,3854,3924,3994,4094,4183,4261,4349,4439,4511,4583,4667,4718,4796,4862,4943,5026,5088,5152,5215,5284,5384,5488,5581,5681,5739,5794,5872,5956,6034", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "256,333,406,493,581,661,760,879,961,1020,1084,1176,1244,1304,1391,1455,1517,1581,1649,1714,1768,1877,1935,1997,2051,2126,2246,2328,2405,2495,2579,2659,2793,2871,2951,3074,3162,3240,3294,3345,3411,3479,3553,3624,3700,3771,3849,3919,3989,4089,4178,4256,4344,4434,4506,4578,4662,4713,4791,4857,4938,5021,5083,5147,5210,5279,5379,5483,5576,5676,5734,5789,5867,5951,6029,6101"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2943,3020,3093,3180,3268,4074,4173,4292,7308,7367,7504,7967,8199,8259,8346,8410,8472,8536,8604,8669,8723,8832,8890,8952,9006,9081,9201,9283,9360,9450,9534,9614,9748,9826,9906,10029,10117,10195,10249,10300,10366,10434,10508,10579,10655,10726,10804,10874,10944,11044,11133,11211,11299,11389,11461,11533,11617,11668,11746,11812,11893,11976,12038,12102,12165,12234,12334,12438,12531,12631,12689,13043,13363,13447,13595", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "306,3015,3088,3175,3263,3343,4168,4287,4369,7362,7426,7591,8030,8254,8341,8405,8467,8531,8599,8664,8718,8827,8885,8947,9001,9076,9196,9278,9355,9445,9529,9609,9743,9821,9901,10024,10112,10190,10244,10295,10361,10429,10503,10574,10650,10721,10799,10869,10939,11039,11128,11206,11294,11384,11456,11528,11612,11663,11741,11807,11888,11971,12033,12097,12160,12229,12329,12433,12526,12626,12684,12739,13116,13442,13520,13662"}}]}]}
import '/bootstrap/app_helper.dart';
import '/routes/router.dart';
import '/app/models/app_config.dart';
import 'package:nylo_framework/nylo_framework.dart';

class RouteProvider implements NyProvider {
  @override
  boot(Nylo nylo) async {
    nylo.addRouter(appRouter());

    return nylo;
  }

  @override
  afterBoot(Nylo nylo) async {
    // Ensure appConfig is initialized with default values if null
    if (AppHelper.instance.appConfig == null) {
      print('⚠️ AppConfig is null, initializing with default config');
      AppHelper.instance.appConfig = AppConfig.defaultConfig();
    }

    // Check appStatus - if null or inactive, route to no-connection
    String? appStatus = AppHelper.instance.appConfig?.appStatus;
    print('🔍 AppConfig.appStatus: $appStatus');

    if (appStatus == null || appStatus.toLowerCase() != 'active') {
      print('⚠️ App status is not active ($appStatus), routing to no-connection');
      nylo.initRoutes(initialRoute: '/no-connection');
      return;
    }

    print('✅ App status is active, initializing normal routes');
    nylo.initRoutes();
  }
}

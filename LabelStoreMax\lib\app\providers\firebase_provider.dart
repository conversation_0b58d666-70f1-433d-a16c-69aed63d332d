import 'package:firebase_core/firebase_core.dart';

import '/bootstrap/app_helper.dart';
import '/firebase_options.dart';
import '/app/services/fcm_service.dart';
import 'package:nylo_framework/nylo_framework.dart';


class FirebaseProvider implements NyProvider {
  @override
  boot(Nylo nylo) async {
    return null;
  }

  @override
  afterBoot(Nylo nylo) async {
    bool? firebaseFcmIsEnabled =
        AppHelper.instance.appConfig?.firebaseFcmIsEnabled;
    firebaseFcmIsEnabled ??= getEnv('FCM_ENABLED', defaultValue: false);

    if (firebaseFcmIsEnabled != true) return;

    // Check if AppConfig is properly initialized before Firebase initialization
    if (AppHelper.instance.appConfig == null) {
      print('Firebase: AppConfig is null, skipping Firebase initialization');
      return;
    }

    // Check if Firebase options are available for the current platform
    if (AppHelper.instance.appConfig?.firebaseOptionsAndroid == null) {
      print('Firebase: Firebase options not configured for Android, skipping initialization');
      return;
    }

    try {
      // Initialize Firebase
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      print('Firebase: Initialized successfully');

      // Initialize FCM service
      await FCMService().initialize();
      print('FCM: Service initialized successfully');

    } catch (e) {
      print('Firebase: Error during initialization: $e');
      printError(e);
    }
  }
}

//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';

class MyProductImage {
  final int id;
  final String? src; // Confirmed nullable - can receive null from API
  final String? name;
  final String? alt;

  MyProductImage({
    required this.id,
    this.src,
    this.name,
    this.alt,
  });

  factory MyProductImage.fromJson(Map<String, dynamic> json) {
    return MyProductImage(
      id: json['id'] ?? 0,
      src: json['src'] as String?,
      name: json['name'] as String?,
      alt: json['alt'] as String?,
    );
  }

  factory MyProductImage.fromWooProductImage(WooProductImage wooImage) {
    return MyProductImage(
      id: wooImage.id ?? 0,
      src: wooImage.src, // Already String? in WooProductImage
      name: wooImage.name, // Already String?
      alt: wooImage.alt, // Already String?
    );
  }

  // Helper method to get a safe image source
  String getSafeImageSrc({String fallback = ''}) {
    return src ?? fallback;
  }

  // Helper method to check if image has valid source
  bool hasValidSrc() {
    return src != null && src!.isNotEmpty;
  }
}

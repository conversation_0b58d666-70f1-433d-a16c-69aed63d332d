{"logs": [{"outputFile": "com.aboutyou.dart_packages.sign_in_with_apple-release-26:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9a6a7d66dade9687bc3ebe9c638fd63d\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,1201", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,1297"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3745e33541868d211c8a5efba657ed39\\transformed\\browser-1.5.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "789,892,993,1102", "endColumns": "102,100,108,98", "endOffsets": "887,988,1097,1196"}}]}, {"outputFile": "com.aboutyou.dart_packages.sign_in_with_apple-mergeReleaseResources-24:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9a6a7d66dade9687bc3ebe9c638fd63d\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,1201", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,1297"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3745e33541868d211c8a5efba657ed39\\transformed\\browser-1.5.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "789,892,993,1102", "endColumns": "102,100,108,98", "endOffsets": "887,988,1097,1196"}}]}]}
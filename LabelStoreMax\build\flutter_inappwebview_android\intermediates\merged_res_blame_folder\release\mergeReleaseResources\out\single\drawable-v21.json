[{"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/drawable-v21/abc_btn_colored_material.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/drawable-v21/abc_btn_colored_material.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/drawable-v21/abc_action_bar_item_background_material.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/drawable-v21/abc_action_bar_item_background_material.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/drawable-v21/notification_action_background.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-core-1.13.1-15:/drawable-v21/notification_action_background.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/drawable-v21/abc_dialog_material_background.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/drawable-v21/abc_dialog_material_background.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/drawable-v21/abc_edit_text_material.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/drawable-v21/abc_edit_text_material.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/drawable-v21/abc_list_divider_material.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/drawable-v21/abc_list_divider_material.xml"}]
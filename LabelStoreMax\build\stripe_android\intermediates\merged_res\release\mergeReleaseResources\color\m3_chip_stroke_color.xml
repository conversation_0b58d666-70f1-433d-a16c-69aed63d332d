<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright 2021 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      https://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<selector xmlns:android="http://schemas.android.com/apk/res/android">
  <!-- Disabled -->
  <item android:alpha="@dimen/material_emphasis_disabled_background" android:color="?attr/colorOnSurface" android:state_enabled="false"/>

  <!-- Selected -->
  <item android:color="@android:color/transparent" android:state_selected="true"/>
  <item android:color="@android:color/transparent" android:state_checked="true"/>

  <!-- Focused -->
  <item android:color="?attr/colorOnSurfaceVariant" android:state_focused="true"/>

  <!-- Other states -->
  <item android:color="?attr/colorOutline"/>
</selector>

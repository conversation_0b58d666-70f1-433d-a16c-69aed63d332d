<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="urn:oasis:names:tc:xliff:document:1.2" xmlns:ns2="http://schemas.android.com/tools">
    <plurals name="mtrl_badge_content_description">
    <item quantity="one">%d new notification</item>
    <item quantity="other">%d new notifications</item>
  </plurals>
    <string msgid="5976598919945601918" name="abc_action_bar_home_description">"Navigate home"</string>
    <string msgid="8388173803310557296" name="abc_action_bar_up_description">"Navigate up"</string>
    <string msgid="3937310113216875497" name="abc_action_menu_overflow_description">"More options"</string>
    <string msgid="4692188335987374352" name="abc_action_mode_done">"Done"</string>
    <string msgid="1189761859438369441" name="abc_activity_chooser_view_see_all">"See all"</string>
    <string msgid="2165779757652331008" name="abc_activitychooserview_choose_application">"Choose an app"</string>
    <string msgid="4215997306490295099" name="abc_capital_off">"OFF"</string>
    <string msgid="884982626291842264" name="abc_capital_on">"ON"</string>
    <string msgid="8833365367933412986" name="abc_menu_alt_shortcut_label">"Alt+"</string>
    <string msgid="2223301931652355242" name="abc_menu_ctrl_shortcut_label">"Ctrl+"</string>
    <string msgid="838001238306846836" name="abc_menu_delete_shortcut_label">"delete"</string>
    <string msgid="7986526966204849475" name="abc_menu_enter_shortcut_label">"enter"</string>
    <string msgid="375214403600139847" name="abc_menu_function_shortcut_label">"Function+"</string>
    <string msgid="4192209724446364286" name="abc_menu_meta_shortcut_label">"Meta+"</string>
    <string msgid="4741552369836443843" name="abc_menu_shift_shortcut_label">"Shift+"</string>
    <string msgid="5473865519181928982" name="abc_menu_space_shortcut_label">"space"</string>
    <string msgid="6180552449598693998" name="abc_menu_sym_shortcut_label">"Sym+"</string>
    <string msgid="5520303668377388990" name="abc_prepend_shortcut_label">"Menu+"</string>
    <string msgid="7208076849092622260" name="abc_search_hint">"Search…"</string>
    <string msgid="3741173234950517107" name="abc_searchview_description_clear">"Clear query"</string>
    <string msgid="693312494995508443" name="abc_searchview_description_query">"Search query"</string>
    <string msgid="3417662926640357176" name="abc_searchview_description_search">"Search"</string>
    <string msgid="1486535517437947103" name="abc_searchview_description_submit">"Submit query"</string>
    <string msgid="2293578557972875415" name="abc_searchview_description_voice">"Voice search"</string>
    <string msgid="8875138169939072951" name="abc_shareactionprovider_share_with">"Share with"</string>
    <string msgid="9055268688411532828" name="abc_shareactionprovider_share_with_application">"Share with <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string>
    <string msgid="1656852541809559762" name="abc_toolbar_collapse_description">"Collapse"</string>
    <string name="bottomsheet_action_collapse">Collapse the bottom sheet</string>
    <string name="bottomsheet_action_expand">Expand the bottom sheet</string>
    <string name="bottomsheet_action_expand_halfway">Expand halfway</string>
    <string name="bottomsheet_drag_handle_clicked">Drag handle double-tapped</string>
    <string name="bottomsheet_drag_handle_content_description">Drag handle</string>
    <string msgid="881409763997275156" name="call_notification_answer_action">"Answer"</string>
    <string msgid="8793775615905189152" name="call_notification_answer_video_action">"Video"</string>
    <string msgid="3229508546291798546" name="call_notification_decline_action">"Decline"</string>
    <string msgid="2659457946726154263" name="call_notification_hang_up_action">"Hang up"</string>
    <string msgid="6107532579223922871" name="call_notification_incoming_text">"Incoming call"</string>
    <string msgid="8623827134497363134" name="call_notification_ongoing_text">"On-going call"</string>
    <string msgid="59049573811482460" name="call_notification_screening_text">"Screening an incoming call"</string>
    <string name="character_counter_content_description">Characters entered %1$d of %2$d</string>
    <string name="character_counter_overflowed_content_description">Character limit exceeded %1$d of %2$d</string>
    <string name="clear_text_end_icon_content_description">Clear text</string>
    <string msgid="406453423630273620" name="close_drawer">"Close navigation menu"</string>
    <string msgid="7573152094250666567" name="close_sheet">"Close sheet"</string>
    <string msgid="5389587048670450460" name="collapsed">"Collapsed"</string>
    <string msgid="2523291102206661146" name="common_google_play_services_enable_button">Enable</string>
    <string msgid="227660514972886228" name="common_google_play_services_enable_text">%1$s won\'t work unless you enable Google Play services.</string>
    <string msgid="5122002158466380389" name="common_google_play_services_enable_title">Enable Google Play services</string>
    <string msgid="7153882981874058840" name="common_google_play_services_install_button">Install</string>
    <string name="common_google_play_services_install_text">%1$s won\'t run without Google Play services, which are missing from your device.</string>
    <string msgid="7215213145546190223" name="common_google_play_services_install_title">Get Google Play services</string>
    <string name="common_google_play_services_notification_channel_name">Google Play services availability</string>
    <string name="common_google_play_services_notification_ticker">Google Play services error</string>
    <string name="common_google_play_services_unknown_issue">%1$s is having trouble with Google Play services. Please try again.</string>
    <string name="common_google_play_services_unsupported_text">%1$s won\'t run without Google Play services, which are not supported by your device.</string>
    <string msgid="6556509956452265614" name="common_google_play_services_update_button">Update</string>
    <string msgid="9053896323427875356" name="common_google_play_services_update_text">%1$s won\'t run unless you update Google Play services.</string>
    <string msgid="6006316683626838685" name="common_google_play_services_update_title">Update Google Play services</string>
    <string name="common_google_play_services_updating_text">%1$s won\'t run without Google Play services, which are currently updating.</string>
    <string name="common_google_play_services_wear_update_text">New version of Google Play services needed. It will update itself shortly.</string>
    <string name="common_open_on_phone">Open on phone</string>
    <string name="common_signin_button_text">Sign In</string>
    <string name="common_signin_button_text_long">Sign in with Google</string>
    <string msgid="3260749812566568062" name="copy_toast_msg">"Link copied to clipboard"</string>
    <string msgid="8038256446254964252" name="default_error_message">"Invalid input"</string>
    <string msgid="6312721426453364202" name="default_popup_window_title">"Pop-up window"</string>
    <string msgid="4057925834421392736" name="dialog">"Dialogue"</string>
    <string msgid="1890207353314751437" name="dropdown_menu">"Drop-down menu"</string>
    <string name="error_a11y_label">Error: Invalid</string>
    <string name="error_icon_content_description">Error</string>
    <string msgid="5974471714631304645" name="expanded">"Expanded"</string>
    <string name="exposed_dropdown_menu_content_description">Show drop-down menu</string>
    <string msgid="4566929209979330987" name="fallback_menu_item_copy_link">"Copy link"</string>
    <string msgid="3413186855122069269" name="fallback_menu_item_open_in_browser">"Open in browser"</string>
    <string msgid="7145444925855055364" name="fallback_menu_item_share_link">"Share link"</string>
    <string name="gpay_logo_description">Google Pay</string>
    <string name="icon_content_description">Dialogue Icon</string>
    <string msgid="6827826412747255547" name="in_progress">"In progress"</string>
    <string msgid="7933458017204019916" name="indeterminate">"Partially ticked"</string>
    <string name="item_view_role_description">Tab</string>
    <string name="material_clock_toggle_content_description">Select a.m. or p.m.</string>
    <string name="material_hour_24h_suffix">%1$s hours</string>
    <string name="material_hour_selection">Select hour</string>
    <string name="material_hour_suffix">%1$s o\'clock</string>
    <string name="material_minute_selection">Select minutes</string>
    <string name="material_minute_suffix">%1$s minutes</string>
    <string name="material_timepicker_am">AM</string>
    <string name="material_timepicker_clock_mode_description">Switch to clock mode for the time input.</string>
    <string name="material_timepicker_hour">Hour</string>
    <string name="material_timepicker_minute">Minute</string>
    <string name="material_timepicker_pm">PM</string>
    <string name="material_timepicker_select_time">Select time</string>
    <string name="material_timepicker_text_input_mode_description">Switch to text input mode for the time input.</string>
    <string name="mtrl_badge_numberless_content_description">New notification</string>
    <string name="mtrl_checkbox_state_description_checked">Ticked</string>
    <string name="mtrl_checkbox_state_description_indeterminate">Partially ticked</string>
    <string name="mtrl_checkbox_state_description_unchecked">Not ticked</string>
    <string name="mtrl_chip_close_icon_content_description">Remove %1$s</string>
    <string name="mtrl_exceed_max_badge_number_content_description" ns2:ignore="PluralsCandidate">More than %1$d new notifications</string>
    <string name="mtrl_picker_a11y_next_month">Change to next month</string>
    <string name="mtrl_picker_a11y_prev_month">Change to previous month</string>
    <string name="mtrl_picker_announce_current_range_selection">Start date selection: %1$s – end date selection: %2$s</string>
    <string name="mtrl_picker_announce_current_selection">Current selection: %1$s</string>
    <string name="mtrl_picker_announce_current_selection_none">none</string>
    <string name="mtrl_picker_cancel">Cancel</string>
    <string name="mtrl_picker_confirm">OK</string>
    <string name="mtrl_picker_date_header_selected">%1$s</string>
    <string name="mtrl_picker_date_header_title">Select date</string>
    <string name="mtrl_picker_date_header_unselected">Selected date</string>
    <string name="mtrl_picker_day_of_week_column_header">%1$s</string>
    <string name="mtrl_picker_end_date_description">End date %1$s</string>
    <string name="mtrl_picker_invalid_format">Invalid format.</string>
    <string name="mtrl_picker_invalid_format_example">Example: %1$s</string>
    <string name="mtrl_picker_invalid_format_use">Use: %1$s</string>
    <string name="mtrl_picker_invalid_range">Invalid range.</string>
    <string name="mtrl_picker_navigate_to_current_year_description">Navigate to current year %1$d</string>
    <string name="mtrl_picker_navigate_to_year_description">Navigate to year %1$d</string>
    <string name="mtrl_picker_out_of_range">Out of range: %1$s</string>
    <string name="mtrl_picker_range_header_only_end_selected">Start date – %1$s</string>
    <string name="mtrl_picker_range_header_only_start_selected">%1$s – End date</string>
    <string name="mtrl_picker_range_header_selected">%1$s – %2$s</string>
    <string name="mtrl_picker_range_header_title">Select range</string>
    <string name="mtrl_picker_range_header_unselected">Start date – End date</string>
    <string name="mtrl_picker_save">Save</string>
    <string name="mtrl_picker_start_date_description">Start date %1$s</string>
    <string name="mtrl_picker_text_input_date_hint">Date</string>
    <string name="mtrl_picker_text_input_date_range_end_hint">End date</string>
    <string name="mtrl_picker_text_input_date_range_start_hint">Start date</string>
    <string name="mtrl_picker_text_input_day_abbr">d</string>
    <string name="mtrl_picker_text_input_month_abbr">m</string>
    <string name="mtrl_picker_text_input_year_abbr">y</string>
    <string name="mtrl_picker_today_description">Today %1$s</string>
    <string name="mtrl_picker_toggle_to_calendar_input_mode">Switch to calendar input mode</string>
    <string name="mtrl_picker_toggle_to_day_selection">Tap to switch to calendar view</string>
    <string name="mtrl_picker_toggle_to_text_input_mode">Switch to text input mode</string>
    <string name="mtrl_picker_toggle_to_year_selection">Tap to switch to year view</string>
    <string name="mtrl_timepicker_cancel">Cancel</string>
    <string name="mtrl_timepicker_confirm">OK</string>
    <string msgid="542007171693138492" name="navigation_menu">"Navigation menu"</string>
    <string msgid="6610465462668679431" name="not_selected">"Not selected"</string>
    <string msgid="875452955155264703" name="off">"Off"</string>
    <string msgid="8655164131929253426" name="on">"On"</string>
    <string name="password_toggle_content_description">Show password</string>
    <string msgid="5941395253238309765" name="range_end">"Range end"</string>
    <string msgid="7097486360902471446" name="range_start">"Range start"</string>
    <string msgid="6264217191555673260" name="search_menu_title">"Search"</string>
    <string name="searchview_clear_text_content_description">Clear text</string>
    <string name="searchview_navigation_content_description">Back</string>
    <string msgid="6043586758067023" name="selected">"Selected"</string>
    <string name="side_sheet_accessibility_pane_title">Side sheet</string>
    <string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string>
    <string name="stripe_3ds2_bzv_issuer_image_description">Issuer Image</string>
    <string name="stripe_3ds2_bzv_payment_system_image_description">Payment System Image</string>
    <string name="stripe_3ds2_czv_whitelist_no_label">No</string>
    <string name="stripe_3ds2_czv_whitelist_yes_label">Yes</string>
    <string name="stripe_3ds2_hzv_cancel_label">Cancel</string>
    <string name="stripe_3ds2_hzv_header_label">Secure Checkout</string>
    <string name="stripe_3ds2_processing">Processing\u2026</string>
    <string name="stripe_acc_label_card_number">Card number</string>
    <string name="stripe_acc_label_card_number_node">%s, Card number</string>
    <string name="stripe_acc_label_cvc_node">%s, CVC</string>
    <string name="stripe_acc_label_expiry_date">Expiry date</string>
    <string name="stripe_acc_label_expiry_date_node">%s, Expiry date</string>
    <string name="stripe_acc_label_zip">ZIP Code</string>
    <string name="stripe_acc_label_zip_short">ZIP</string>
    <string name="stripe_add_bank_account">Add bank account</string>
    <string name="stripe_add_new_payment_method">Add a new payment method</string>
    <string name="stripe_add_payment_method">Add a payment method</string>
    <string name="stripe_added">Added %s</string>
    <string name="stripe_address_city_required">Please enter your city</string>
    <string name="stripe_address_country_invalid">Your country is invalid</string>
    <string name="stripe_address_county_required">Please enter your county</string>
    <string name="stripe_address_label_address">Address</string>
    <string name="stripe_address_label_address_line1">Address line 1</string>
    <string name="stripe_address_label_address_line1_optional">Address line 1 (optional)</string>
    <string name="stripe_address_label_address_line2">Address line 2</string>
    <string name="stripe_address_label_address_line2_optional">Address line 2 (optional)</string>
    <string name="stripe_address_label_address_optional">Address (optional) </string>
    <string name="stripe_address_label_ae_emirate">Emirate</string>
    <string name="stripe_address_label_apt_optional">Apt. (optional)</string>
    <string name="stripe_address_label_au_suburb_or_city">Suburb or city</string>
    <string name="stripe_address_label_bb_jm_parish">Parish</string>
    <string name="stripe_address_label_cedex">Cedex</string>
    <string name="stripe_address_label_city">City</string>
    <string name="stripe_address_label_city_optional">City (optional)</string>
    <string name="stripe_address_label_country">Country</string>
    <string name="stripe_address_label_country_or_region">Country or region</string>
    <string name="stripe_address_label_county">County</string>
    <string name="stripe_address_label_county_optional">County (optional)</string>
    <string name="stripe_address_label_department">Department</string>
    <string name="stripe_address_label_district">District</string>
    <string name="stripe_address_label_full_name">Full name</string>
    <string name="stripe_address_label_hk_area">Area</string>
    <string name="stripe_address_label_ie_eircode">Eircode</string>
    <string name="stripe_address_label_ie_townland">Townland</string>
    <string name="stripe_address_label_in_pin">Pin</string>
    <string name="stripe_address_label_island">Island</string>
    <string name="stripe_address_label_jp_prefecture">Prefecture</string>
    <string name="stripe_address_label_kr_do_si">Do Si</string>
    <string name="stripe_address_label_name">Name</string>
    <string name="stripe_address_label_neighborhood">Neighbourhood</string>
    <string name="stripe_address_label_oblast">Oblast</string>
    <string name="stripe_address_label_phone_number">Phone number</string>
    <string name="stripe_address_label_phone_number_optional">Phone number (optional)</string>
    <string name="stripe_address_label_post_town">Post or Town</string>
    <string name="stripe_address_label_postal_code">Postcode</string>
    <string name="stripe_address_label_postal_code_optional">Postal code (optional)</string>
    <string name="stripe_address_label_postcode">Postcode</string>
    <string name="stripe_address_label_postcode_optional">Postcode (optional)</string>
    <string name="stripe_address_label_province">Province</string>
    <string name="stripe_address_label_province_optional">Province (optional)</string>
    <string name="stripe_address_label_region_generic">State / Province / Region</string>
    <string name="stripe_address_label_region_generic_optional">State / Province / Region (optional)</string>
    <string name="stripe_address_label_state">State</string>
    <string name="stripe_address_label_state_optional">State (optional)</string>
    <string name="stripe_address_label_suburb">Suburb</string>
    <string name="stripe_address_label_village_township">Village or Township</string>
    <string name="stripe_address_label_zip_code">ZIP code</string>
    <string name="stripe_address_label_zip_code_optional">ZIP code (optional)</string>
    <string name="stripe_address_label_zip_postal_code">ZIP/Postal code</string>
    <string name="stripe_address_label_zip_postal_code_optional">ZIP / Postal code (optional)</string>
    <string name="stripe_address_name_required">Please enter your name</string>
    <string name="stripe_address_phone_number_required">Please enter your phone number</string>
    <string name="stripe_address_postal_code_invalid">Your postcode is invalid.</string>
    <string name="stripe_address_postcode_invalid">Your postcode is invalid</string>
    <string name="stripe_address_province_required">Please enter your province</string>
    <string name="stripe_address_region_generic_required">Please enter your State/Province/Region</string>
    <string name="stripe_address_required">Please enter your address</string>
    <string name="stripe_address_search_content_description">Search</string>
    <string name="stripe_address_state_required">Please enter your state</string>
    <string name="stripe_address_zip_invalid">Your ZIP code is invalid.</string>
    <string name="stripe_address_zip_postal_invalid">Your ZIP/Postal code is invalid</string>
    <string name="stripe_affirm_buy_now_pay_later">Pay over time with &lt;img src=\"affirm\">.</string>
    <string name="stripe_affirm_buy_now_pay_later_plaintext">Pay over time with Affirm</string>
    <string name="stripe_afterpay_clearpay_marketing"><![CDATA[Buy now or pay later with <img/>]]></string>
    <string name="stripe_afterpay_clearpay_message"><![CDATA[Pay in <num_installments/> interest-free payments of <installment_price/> with <img/>]]></string>
    <string name="stripe_afterpay_clearpay_subtitle">%1$d interest-free payments of %2$s</string>
    <string name="stripe_afterpay_subtitle">Buy now or pay later with Afterpay</string>
    <string name="stripe_amazon_pay_mandate">By confirming your payment with Amazon Pay, you allow %s to charge your Amazon Pay account for future payments in accordance with their terms.</string>
    <string name="stripe_au_becs_account_name">Name on account</string>
    <string name="stripe_au_becs_bsb_number">BSB number</string>
    <string name="stripe_au_becs_mandate">By providing your bank account details and confirming this payment, you agree to this Direct Debit Request and the &lt;a href=\"https://stripe.com/au-becs-dd-service-agreement/legal\">Direct Debit Request service agreement&lt;/a>, and authorise Stripe Payments Australia Pty Ltd ACN *********** Direct Debit User ID number 507156 (“Stripe”) to debit your account through the Bulk Electronic Clearing System (BECS) on behalf of %s (the \"Merchant\") for any amounts separately communicated to you by the Merchant. You certify that you are either an account holder or an authorised signatory on the account listed above.</string>
    <string name="stripe_back">Back</string>
    <string name="stripe_bacs_account_number">Account number</string>
    <string name="stripe_bacs_account_number_incomplete">Your account number is incomplete.</string>
    <string name="stripe_bacs_bank_account_title">Bank account</string>
    <string name="stripe_bacs_confirm_mandate_label">I understand that Stripe will be collecting Direct Debits on behalf of %s and confirm that I am the account holder and the only person required to authorise debits from this account.</string>
    <string name="stripe_bacs_sort_code">Sort code</string>
    <string name="stripe_bacs_sort_code_incomplete">Your sort code is incomplete.</string>
    <string name="stripe_bank_account_ending_in">Bank account ending in %s</string>
    <string name="stripe_bank_account_plus_last_4">Bank account ···· %s</string>
    <string name="stripe_bank_account_with_last_4">Bank account ····%s</string>
    <string name="stripe_becs_mandate_acceptance">\n    By providing your bank account details and confirming this payment, you agree to this\n    Direct Debit Request and the Direct Debit Request service agreement, and authorise\n    Stripe Payments Australia Pty Ltd ACN *********** Direct Debit User ID number 507156\n    (“Stripe”) to debit your account through the Bulk Electronic Clearing System (BECS) on\n    behalf of %1$s (the \"Merchant\") for any amounts separately communicated to you\n    by the Merchant. You certify that you are either an account holder or an authorised\n    signatory on the account listed above.\n    </string>
    <string name="stripe_becs_widget_account_number">Account number</string>
    <string name="stripe_becs_widget_account_number_incomplete">The account number you entered is incomplete.</string>
    <string name="stripe_becs_widget_account_number_required">Your account number is required.</string>
    <string name="stripe_becs_widget_bsb">BSB</string>
    <string name="stripe_becs_widget_bsb_incomplete">The BSB you entered is incomplete.</string>
    <string name="stripe_becs_widget_bsb_invalid">The BSB you entered is invalid.</string>
    <string name="stripe_becs_widget_email">Email Address</string>
    <string name="stripe_becs_widget_email_invalid">Your email address is invalid.</string>
    <string name="stripe_becs_widget_email_required">Your email address is required.</string>
    <string name="stripe_becs_widget_name">Name</string>
    <string name="stripe_becs_widget_name_required">Your name is required.</string>
    <string name="stripe_billing_details">Billing address</string>
    <string name="stripe_billing_same_as_shipping">Billing address is same as shipping</string>
    <string name="stripe_blank_and_required">This field cannot be blank.</string>
    <string name="stripe_blik_code">BLIK code</string>
    <string name="stripe_blik_confirm_payment">Confirm the payment in your bank\'s app within %s to complete the purchase.</string>
    <string name="stripe_boleto_tax_id_label">CPF/CNPJ</string>
    <string name="stripe_cancel">Cancel</string>
    <string name="stripe_card_brand_choice_no_selection">No selection</string>
    <string name="stripe_card_brand_choice_selection_header">Select card brand (optional)</string>
    <string name="stripe_card_brand_not_accepted">(not accepted)</string>
    <string name="stripe_card_brand_not_accepted_with_brand">%1$s (not accepted)</string>
    <string name="stripe_card_declined">Your card has been declined</string>
    <string name="stripe_card_ending_in">%1$s ending in %2$s</string>
    <string name="stripe_card_plus_last_4">%1$s ···· %2$s</string>
    <string name="stripe_card_with_last_4">%1$s ····%2$s</string>
    <string name="stripe_cash_app_pay_mandate">By continuing, you authorize %1$s to debit your Cash App account for this payment and future payments in accordance with %2$s\'s terms, until this authorization is revoked. You can change this anytime in your Cash App Settings.</string>
    <string name="stripe_change">Change</string>
    <string name="stripe_clearpay_subtitle">Buy now or pay later with Clearpay</string>
    <string name="stripe_close">Close</string>
    <string name="stripe_confirm_close_form_body">Your payment information will not be saved.</string>
    <string name="stripe_confirm_close_form_title">Do you want to close this form?</string>
    <string name="stripe_contact_information">Contact information</string>
    <string name="stripe_continue_button_label">Continue</string>
    <string name="stripe_delete_payment_method">Delete Payment Method?</string>
    <string name="stripe_delete_payment_method_prompt_title">Delete payment method?</string>
    <string name="stripe_disallowed_card_brand">%s is not accepted</string>
    <string name="stripe_done">Done</string>
    <string name="stripe_edit">Edit</string>
    <string name="stripe_email">Email</string>
    <string name="stripe_email_is_invalid">Your email is invalid.</string>
    <string name="stripe_eps_bank">EPS Bank</string>
    <string name="stripe_expiration_date_hint">MM / YY</string>
    <string name="stripe_expired_card">Your card has expired</string>
    <string name="stripe_expiry_date_hint">MM/YY</string>
    <string name="stripe_expiry_label_short">Expiry</string>
    <string name="stripe_failure_connection_error">We are experiencing issues connecting to our payments provider. Please check your internet connection and try again.</string>
    <string name="stripe_failure_reason_authentication">We are unable to authenticate your payment method. Please choose a different payment method and try again.</string>
    <string name="stripe_failure_reason_timed_out">Timed out authenticating your payment method - please try again</string>
    <string name="stripe_field_required">This is required</string>
    <string name="stripe_form_label_optional">%s (optional)</string>
    <string name="stripe_fpx_bank">FPX Bank</string>
    <string name="stripe_fpx_bank_offline">%s - Offline</string>
    <string name="stripe_generic_decline">Your payment method was declined.</string>
    <string name="stripe_iban_incomplete">The IBAN you entered is incomplete.</string>
    <string name="stripe_iban_invalid_country">The IBAN you entered is invalid; \"%s\" is not a supported country code.</string>
    <string name="stripe_iban_invalid_start">Your IBAN should start with a two-letter country code.</string>
    <string name="stripe_ideal_bank">iDEAL Bank</string>
    <string name="stripe_incomplete_blik_code">Your BLIK code is incomplete.</string>
    <string name="stripe_incomplete_expiry_date">Your card\'s expiration date is incomplete.</string>
    <string name="stripe_incomplete_phone_number">Incomplete phone number</string>
    <string name="stripe_inline_sign_up_header">Save your info for secure 1-click checkout with Link</string>
    <string name="stripe_internal_error">An internal error occurred.</string>
    <string name="stripe_invalid_bank_account_iban">The IBAN you entered is invalid.</string>
    <string name="stripe_invalid_blik_code">Your BLIK code is invalid.</string>
    <string name="stripe_invalid_card_number">Your card number is invalid.</string>
    <string name="stripe_invalid_cvc">Your card\'s security code is invalid.</string>
    <string name="stripe_invalid_email_address">Invalid email address: %s</string>
    <string name="stripe_invalid_expiry_month">Your card\'s expiration month is invalid.</string>
    <string name="stripe_invalid_expiry_year">Your card\'s expiration year is invalid.</string>
    <string name="stripe_invalid_owner_name">Your name is invalid.</string>
    <string name="stripe_invalid_shipping_information">Invalid Shipping Address</string>
    <string name="stripe_invalid_upi_id">Invalid UPI ID</string>
    <string name="stripe_invalid_zip">Your postcode is incomplete.</string>
    <string name="stripe_klarna_buy_now_pay_later">Buy now or pay later with Klarna.</string>
    <string name="stripe_klarna_mandate">By continuing to Klarna, you allow %1$s to charge your Klarna account for future payments in accordance with their terms and Klarna\'s terms. You can change this at any time in your Klarna app or by reaching out to %2$s.</string>
    <string name="stripe_klarna_pay_later">Pay later with Klarna</string>
    <string name="stripe_konbini_confirmation_number_label">Phone</string>
    <string name="stripe_link_simple_secure_payments">Simple, secure one-click payments</string>
    <string name="stripe_log_out">Log out of Link</string>
    <string name="stripe_name_on_card">Name on card</string>
    <string name="stripe_network_error_message">An error occurred. Check your connection and try again.</string>
    <string name="stripe_p24_bank">Przelewy24 Bank</string>
    <string name="stripe_pay_button_amount">Pay %s</string>
    <string name="stripe_pay_with_link">Pay with Link</string>
    <string name="stripe_payment_method_add_new_card">Add new card…</string>
    <string name="stripe_payment_method_add_new_fpx">Select Bank Account (FPX)…</string>
    <string name="stripe_payment_method_bank">Bank</string>
    <string name="stripe_paymentsheet_ach_continue_mandate"><![CDATA[By continuing, you agree to authorize payments pursuant to <terms>these terms</terms>.]]></string>
    <string name="stripe_paymentsheet_ach_save_mandate"><![CDATA[By saving your bank account for %s you agree to authorize payments pursuant to <terms>these terms</terms>.]]></string>
    <string name="stripe_paymentsheet_ach_something_went_wrong">Something went wrong when linking your account.\nPlease try again later.</string>
    <string name="stripe_paymentsheet_add_card">Add card</string>
    <string name="stripe_paymentsheet_add_new_card">Add new card</string>
    <string name="stripe_paymentsheet_add_payment_method_button_label">+ Add</string>
    <string name="stripe_paymentsheet_add_payment_method_card_information">Card information</string>
    <string name="stripe_paymentsheet_add_payment_method_title">Add your payment information</string>
    <string name="stripe_paymentsheet_add_us_bank_account">Add US bank account</string>
    <string name="stripe_paymentsheet_address_element_primary_button">Save address</string>
    <string name="stripe_paymentsheet_address_element_shipping_address">Shipping Address</string>
    <string name="stripe_paymentsheet_bacs_email_mandate">An email will be sent to %s within three business days to confirm the setup of this debit instruction.</string>
    <string name="stripe_paymentsheet_bacs_mandate_title">Confirm your Direct Debit details</string>
    <string name="stripe_paymentsheet_bacs_modify_details_button_label">Modify details</string>
    <string name="stripe_paymentsheet_bacs_notice_mandate">Additionally, you will receive two days advance notice via email of any amount to be debited under this instruction. Payments will show as \'%s\' on your bank statement.</string>
    <string name="stripe_paymentsheet_bacs_protection_mandate">Your payments are protected by the %s.</string>
    <string name="stripe_paymentsheet_bank_account_details_cannot_be_changed">Bank account details cannot be changed.</string>
    <string name="stripe_paymentsheet_bank_payment_promo_for_payment"><![CDATA[Get %s back when you pay by bank. <a href=\"https://link.com/promotion-terms\">See&#160;terms</a>]]></string>
    <string name="stripe_paymentsheet_bank_payment_promo_for_setup"><![CDATA[Get %s back when you pay for the first time with your bank. <a href=\"https://link.com/promotion-terms\">See&#160;terms</a>]]></string>
    <string name="stripe_paymentsheet_bank_payment_promo_ineligible">No %s promo</string>
    <string name="stripe_paymentsheet_buy_using_upi_id">Buy using a UPI ID</string>
    <string name="stripe_paymentsheet_card_details_cannot_be_changed">Card details cannot be changed.</string>
    <string name="stripe_paymentsheet_card_mandate">By providing your card information, you allow %s to charge your card for future payments in accordance with their terms.</string>
    <string name="stripe_paymentsheet_choose_payment_method">Choose a payment method</string>
    <string name="stripe_paymentsheet_close">Close</string>
    <string name="stripe_paymentsheet_confirm">Confirm</string>
    <string name="stripe_paymentsheet_confirm_your_cvc">Confirm your CVC</string>
    <string name="stripe_paymentsheet_enter_address_manually">Enter address manually</string>
    <string name="stripe_paymentsheet_manage_bank_account">Manage US bank account</string>
    <string name="stripe_paymentsheet_manage_card">Manage card</string>
    <string name="stripe_paymentsheet_manage_payment_methods">Manage payment methods</string>
    <string name="stripe_paymentsheet_manage_sepa_debit">Manage SEPA debit</string>
    <string name="stripe_paymentsheet_manage_your_payment_methods">Manage your payment methods</string>
    <string name="stripe_paymentsheet_microdeposit">Stripe will deposit $0.01 to your account in 1-2 business days. Then you’ll get an email with instructions to complete payment to %s.</string>
    <string name="stripe_paymentsheet_modify_pm">Edit %s</string>
    <string name="stripe_paymentsheet_new_card">New card</string>
    <string name="stripe_paymentsheet_new_pm">New payment method</string>
    <string name="stripe_paymentsheet_only_card_brand_can_be_changed">Only card brand can be changed.</string>
    <string name="stripe_paymentsheet_or_pay_using">Or pay using</string>
    <string name="stripe_paymentsheet_or_pay_with_card">Or pay with a card</string>
    <string name="stripe_paymentsheet_or_use">Or use</string>
    <string name="stripe_paymentsheet_or_use_a_card">Or use a card</string>
    <string name="stripe_paymentsheet_pay_button_label">Pay</string>
    <string name="stripe_paymentsheet_pay_using">Pay using</string>
    <string name="stripe_paymentsheet_pay_with_bank_title">Pay with your bank account in just a few steps.</string>
    <string name="stripe_paymentsheet_payment_method_au_becs_debit">AU BECS Direct Debit</string>
    <string name="stripe_paymentsheet_payment_method_bacs_debit">Bacs Direct Debit</string>
    <string name="stripe_paymentsheet_payment_method_card">Card</string>
    <string name="stripe_paymentsheet_payment_method_konbini">Konbini</string>
    <string name="stripe_paymentsheet_payment_method_sepa_debit">SEPA Debit</string>
    <string name="stripe_paymentsheet_primary_button_processing">Processing…</string>
    <string name="stripe_paymentsheet_remove_bank_account_question_title">Remove bank account?</string>
    <string name="stripe_paymentsheet_remove_bank_account_title">Remove bank account</string>
    <string name="stripe_paymentsheet_remove_card">Remove card</string>
    <string name="stripe_paymentsheet_remove_card_title">Remove card?</string>
    <string name="stripe_paymentsheet_remove_pm">Remove %s</string>
    <string name="stripe_paymentsheet_remove_pm_title">Remove payment method</string>
    <string name="stripe_paymentsheet_save">Save</string>
    <string name="stripe_paymentsheet_save_a_new_payment_method">Save a new payment method</string>
    <string name="stripe_paymentsheet_save_bank_title">Save your bank account in just a few steps.</string>
    <string name="stripe_paymentsheet_save_for_future_payments">Save for future payments</string>
    <string name="stripe_paymentsheet_save_this_card_with_merchant_name">Save this card for future %s payments</string>
    <string name="stripe_paymentsheet_saved">Saved</string>
    <string name="stripe_paymentsheet_select_payment_method">Select payment method</string>
    <string name="stripe_paymentsheet_select_your_payment_method">Select your payment method</string>
    <string name="stripe_paymentsheet_sepa_debit_details_cannot_be_changed">SEPA debit details cannot be changed.</string>
    <string name="stripe_paymentsheet_total_amount">Total: %s</string>
    <string name="stripe_paypal_mandate">By confirming your payment with PayPal, you allow %s to charge your PayPal account for future payments in accordance with their terms.</string>
    <string name="stripe_pm_set_as_default">Set as default payment method</string>
    <string name="stripe_price_free">Free</string>
    <string name="stripe_processing_error">There was an error processing your card - please try again in a few seconds</string>
    <string name="stripe_remove">Remove</string>
    <string name="stripe_remove_bank_account_ending_in">Remove bank account ending in %s</string>
    <string name="stripe_removed">Removed %s</string>
    <string name="stripe_revolut_mandate">By confirming your payment with RevolutPay, you allow %s to charge your RevolutPay account for future payments in accordance with their terms.</string>
    <string name="stripe_save_for_future_payments_with_merchant_name">Save for future %s payments</string>
    <string name="stripe_save_payment_details_to_merchant_name">Save payment details to %s for future purchases</string>
    <string name="stripe_scan_card">Scan card</string>
    <string name="stripe_secure_checkout">Secure Checkout</string>
    <string name="stripe_sepa_mandate">By providing your payment information and confirming this payment, you authorise (A) %s and Stripe, our payment service provider, to send instructions to your bank to debit your account and (B) your bank to debit your account in accordance with those instructions. As part of your rights, you are entitled to a refund from your bank under the terms and conditions of your agreement with your bank. A refund must be claimed within 8 weeks starting from the date on which your account was debited. Your rights are explained in a statement that you can obtain from your bank. You agree to receive notifications for future debits up to 2 days before they occur.</string>
    <string name="stripe_set_as_default_payment_method">Set as default payment method</string>
    <string name="stripe_setup_button_label">Set up</string>
    <string name="stripe_show_menu">Show menu</string>
    <string name="stripe_sign_up">Join Link</string>
    <string name="stripe_sign_up_header">Secure 1⁠-⁠click checkout</string>
    <string name="stripe_sign_up_message">Pay faster at %s and thousands of businesses.</string>
    <string name="stripe_sign_up_terms"><![CDATA[By joining Link, you agree to the <terms>Terms</terms> and <privacy>Privacy Policy</privacy>.]]></string>
    <string name="stripe_sign_up_terms_alternative"><![CDATA[By providing your email, you agree to create a Link account and save your payment info to Link, according to the <terms>Link Terms</terms> and <privacy>Privacy Policy</privacy>.]]></string>
    <string name="stripe_sign_up_terms_alternative_with_phone_number"><![CDATA[By providing your phone number, you agree to create a Link account and save your payment info to Link, according to the <terms>Link Terms</terms> and <privacy>Privacy Policy</privacy>.]]></string>
    <string name="stripe_signup_deactivated_account_message">Your account has been deactivated</string>
    <string name="stripe_something_went_wrong">Something went wrong</string>
    <string name="stripe_title_add_a_card">Add a card</string>
    <string name="stripe_title_add_an_address">Set Address</string>
    <string name="stripe_title_bank_account">Bank Account</string>
    <string name="stripe_title_payment_method">Payment Method</string>
    <string name="stripe_title_select_shipping_method">Select Shipping Method</string>
    <string name="stripe_title_update_card">Update card</string>
    <string name="stripe_update">Update</string>
    <string name="stripe_upi_id_label">UPI ID</string>
    <string name="stripe_upi_polling_cancel">Cancel and pay another way</string>
    <string name="stripe_upi_polling_header">Approve payment</string>
    <string name="stripe_upi_polling_message">Open your UPI app to approve your payment within %s</string>
    <string name="stripe_upi_polling_payment_failed_message">Please go back and select another payment method</string>
    <string name="stripe_upi_polling_payment_failed_title">Payment failed</string>
    <string name="stripe_verification_change_email">Change email</string>
    <string name="stripe_verification_code_sent">Code sent</string>
    <string name="stripe_verification_dialog_header">Confirm it\'s you</string>
    <string name="stripe_verification_header">Enter your verification code</string>
    <string name="stripe_verification_header_inline">Sign in to your Link account</string>
    <string name="stripe_verification_header_prefilled">Use your saved info to check out faster</string>
    <string name="stripe_verification_message">Enter the code sent to %s to use Link to pay by default.</string>
    <string name="stripe_verification_not_email">Not %s?</string>
    <string name="stripe_verification_resend">Resend code</string>
    <string name="stripe_verify_your_payment">Verify your payment</string>
    <string name="stripe_view_more">View more</string>
    <string name="stripe_wallet_bank_account_terms"><![CDATA[By continuing, you agree to authorise payments pursuant to these <terms>terms</terms>.]]></string>
    <string name="stripe_wallet_collapsed_payment">Payment</string>
    <string name="stripe_wallet_default">Default</string>
    <string name="stripe_wallet_expand_accessibility">Change selection</string>
    <string name="stripe_wallet_expanded_title">Payment methods</string>
    <string name="stripe_wallet_passthrough_description">Passthrough</string>
    <string name="stripe_wallet_pay_another_way">Pay another way</string>
    <string name="stripe_wallet_recollect_cvc_error">For security, please re-enter your card’s security code.</string>
    <string name="stripe_wallet_remove_account_confirmation">Are you sure you want to remove this linked account?</string>
    <string name="stripe_wallet_remove_card_confirmation">Are you sure you want to remove this card?</string>
    <string name="stripe_wallet_remove_linked_account">Remove linked account</string>
    <string name="stripe_wallet_set_as_default">Set as default</string>
    <string name="stripe_wallet_unavailable">Unavailable for this purchase</string>
    <string name="stripe_wallet_update_card">Update card</string>
    <string name="stripe_wallet_update_expired_card_error">This card has expired. Update your card info or choose a different payment method.</string>
    <string msgid="2561197295334830845" name="switch_role">"Switch"</string>
    <string msgid="********17127674378" name="tab">"Tab"</string>
    <string msgid="5946805113151406391" name="template_percent">"<ns1:g id="PERCENTAGE">%1$d</ns1:g> per cent."</string>
    <string msgid="3765533235322692011" name="tooltip_description">"tooltip"</string>
    <string msgid="3124740595719787496" name="tooltip_label">"show tooltip"</string>
    <string name="wallet_buy_button_place_holder">Buy with Google</string>
</resources>
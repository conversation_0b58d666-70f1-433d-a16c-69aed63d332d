<variant
    name="release"
    package="io.github.ponnamkarthik.toast.fluttertoast"
    minSdkVersion="19"
    targetSdkVersion="33"
    mergedManifest="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\fluttertoast\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    proguardFiles="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\fluttertoast\intermediates\default_proguard_files\global\proguard-android.txt-8.6.0"
    partialResultsDir="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\fluttertoast\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57c1ed20ca3265d304f6aff0236b8f43\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\main\kotlin;src\release\java;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\fluttertoast\intermediates\javac\release\compileReleaseJavaWithJavac\classes;C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\fluttertoast\tmp\kotlin-classes\release;C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\fluttertoast\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="io.github.ponnamkarthik.toast.fluttertoast"
      generatedSourceFolders="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\fluttertoast\generated\ap_generated_sources\release\out"
      generatedResourceFolders="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\fluttertoast\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57c1ed20ca3265d304f6aff0236b8f43\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>

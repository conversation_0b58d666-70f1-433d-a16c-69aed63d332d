//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:lottie/lottie.dart';

class AppLoaderWidget extends StatelessWidget {
  const AppLoaderWidget({super.key, this.centered = true});

  final bool centered;

  @override
  Widget build(BuildContext context) {
    Widget loader = Container(
      width: 80,
      height: 80,
      child: Lottie.asset(
        'public/animations/loader.json',
        width: 80,
        height: 80,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          // Fallback to original spinner if <PERSON><PERSON> fails
          bool isDark = (Theme.of(context).brightness == Brightness.dark);
          return SpinKitDoubleBounce(
            color: Color(!isDark ? 0xFF424242 : 0xFFC7C7C7),
          );
        },
      ),
    );

    // Return perfectly centered loader by default, or just the loader if centered is false
    return centered
        ? Center(
            child: Container(
              alignment: Alignment.center,
              child: loader,
            ),
          )
        : loader;
  }
}

//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/models/customer_country.dart';
import '/app/models/libyan_city.dart';

import '/resources/widgets/velvete_ui.dart';
import '/resources/widgets/google_maps_location_picker.dart';

import 'package:nylo_framework/nylo_framework.dart';

class CustomerAddressInput extends StatefulWidget {
  const CustomerAddressInput(
      {super.key,
      required this.txtControllerFirstName,
      required this.txtControllerLastName,
      required this.txtControllerAddressLine,
      required this.txtControllerCity,
      required this.txtControllerPostalCode,
      this.txtControllerEmailAddress,
      this.txtControllerPhoneN<PERSON>,
      required this.customerCountry,
      required this.onTapCountry});

  final TextEditingController? txtControllerFirstName,
      txtControllerLastName,
      txtControllerAddressLine,
      txtControllerCity,
      txtControllerPostalCode,
      txtControllerEmailAddress,
      txtControllerPhoneNumber;

  final CustomerCountry? customerCountry;
  final Function() onTapCountry;

  @override
  State<CustomerAddressInput> createState() => _CustomerAddressInputState();
}

class _CustomerAddressInputState extends State<CustomerAddressInput> {
  LibyanCity? _selectedCity;
  List<LibyanCity> _cities = [];

  @override
  void initState() {
    super.initState();

    // CRITICAL FIX: Ensure unique cities to prevent DropdownButton duplicate value error
    List<LibyanCity> allCities = LibyanCitiesData.getAllCities();
    _cities = allCities.toSet().toList(); // Remove duplicates using Set

    print('🔍 Loaded ${allCities.length} total cities, ${_cities.length} unique cities');

    // Try to find the selected city from the current text controller value
    if (widget.txtControllerCity?.text.isNotEmpty == true) {
      _selectedCity = LibyanCitiesData.findCityByName(widget.txtControllerCity!.text) ??
                     LibyanCitiesData.findCityByArabicName(widget.txtControllerCity!.text);
      print('🔍 Pre-selected city: ${_selectedCity?.getDisplayName()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min, // FIXED: min size since parent now provides bounds
      children: <Widget>[
        Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              flex: 1,
              child: TextEditingRow(
                heading: trans("First Name"),
                controller: widget.txtControllerFirstName,
                shouldAutoFocus: true,
              ),
            ),
            SizedBox(width: 8),
            Expanded(
              flex: 1,
              child: TextEditingRow(
                heading: trans("Last Name"),
                controller: widget.txtControllerLastName,
              ),
            ),
          ],
        ),
        // Address Line with Google Maps Location Picker
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 23,
              width: double.infinity,
              child: Text(
                trans("Address Line"),
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.left,
              ),
            ),
            SizedBox(
              height: 80, // Adequate height for text field and button
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    flex: 3,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextField(
                        controller: widget.txtControllerAddressLine,
                        decoration: InputDecoration(
                          hintText: trans("Enter address or use location picker"),
                          border: const OutlineInputBorder(),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Flexible(
                    flex: 1,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => GoogleMapsLocationPicker(
                                initialAddress: widget.txtControllerAddressLine?.text,
                                onLocationSelected: (address, lat, lng) {
                                  widget.txtControllerAddressLine?.text = address;
                                },
                              ),
                            ),
                          );
                        },
                        icon: const Icon(Icons.location_on, size: 16),
                        label: Text(
                          trans("Map"),
                          style: const TextStyle(fontSize: 12),
                        ),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        // Libyan Cities Dropdown
        Container(
          margin: EdgeInsets.symmetric(vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "المدينة", // Arabic for "City"
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<LibyanCity>(
                    value: _selectedCity,
                    hint: Text("اختر المدينة"), // Arabic for "Choose City"
                    isExpanded: true,
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    items: _cities.map((LibyanCity city) {
                      return DropdownMenuItem<LibyanCity>(
                        value: city,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(city.getDisplayName()),
                            Text(
                              city.getFormattedDeliveryCost(),
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (LibyanCity? newValue) {
                      setState(() {
                        _selectedCity = newValue;
                        // Update the text controller with the Arabic city name for WooCommerce API
                        widget.txtControllerCity?.text = newValue?.nameArabic ?? newValue?.name ?? '';
                        print('✅ Selected city: ${newValue?.nameArabic} (${newValue?.deliveryCost} LYD)');
                      });
                    },
                  ),
                ),
              ),
              if (_selectedCity != null)
                Container(
                  margin: EdgeInsets.only(top: 4),
                  child: Text(
                    "تكلفة التوصيل: ${_selectedCity!.getFormattedDeliveryCost()}", // Arabic for "Delivery cost"
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.green[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          children: <Widget>[
            Expanded(
              flex: 1,
              child: TextEditingRow(
                heading: trans("Postal code"),
                controller: widget.txtControllerPostalCode,
              ),
            ),
            if (widget.txtControllerEmailAddress != null) ...[
              SizedBox(width: 8),
              Expanded(
                flex: 1,
                child: TextEditingRow(
                    heading: trans("Email address"),
                    keyboardType: TextInputType.emailAddress,
                    controller: widget.txtControllerEmailAddress),
              ),
            ],
          ],
        ),
        if (widget.txtControllerPhoneNumber != null)
          Row(
            mainAxisSize: MainAxisSize.max,
            children: <Widget>[
              Expanded(
                child: TextEditingRow(
                  heading: trans("Phone Number"),
                  controller: widget.txtControllerPhoneNumber,
                  keyboardType: TextInputType.phone,
                ),
              ),
            ],
          ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              if (widget.customerCountry?.hasState() ?? false) ...[
                Expanded(
                  flex: 1,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 23,
                        width: double.infinity,
                        child: Text(
                          trans("State"),
                          style: Theme.of(context).textTheme.bodyLarge,
                          textAlign: TextAlign.left,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.location_city, color: Colors.grey[600], size: 20),
                              SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  (widget.customerCountry!.state != null
                                      ? (widget.customerCountry?.state?.name ?? "")
                                      : trans("No state required")),
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ),
                              Icon(Icons.lock, color: Colors.grey[500], size: 16),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 8),
              ],
              Expanded(
                flex: 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 23,
                      width: double.infinity,
                      child: Text(
                        trans("Country"),
                        style: Theme.of(context).textTheme.bodyLarge,
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.all(8),
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.flag, color: Colors.green[700], size: 20),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                "Libya", // Hardcoded Libya
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey[700],
                                ),
                              ),
                            ),
                            Icon(Icons.lock, color: Colors.grey[500], size: 16),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

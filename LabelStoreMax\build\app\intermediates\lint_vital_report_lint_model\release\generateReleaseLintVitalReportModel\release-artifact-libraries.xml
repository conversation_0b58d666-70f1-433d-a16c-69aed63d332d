<libraries>
  <library
      name="__local_aars__:C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app\intermediates\flutter\release\libs.jar:unspecified@jar"
      jars="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app\intermediates\flutter\release\libs.jar"
      resolved="__local_aars__:C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app\intermediates\flutter\release\libs.jar:unspecified"/>
  <library
      name=":@@:app_badge_plus::release"
      project=":app_badge_plus"/>
  <library
      name=":@@:flutter_timezone::release"
      project=":flutter_timezone"/>
  <library
      name=":@@:google_sign_in_android::release"
      project=":google_sign_in_android"/>
  <library
      name=":@@:shared_preferences_android::release"
      project=":shared_preferences_android"/>
  <library
      name=":@@:stripe_android::release"
      project=":stripe_android"/>
  <library
      name=":@@:firebase_messaging::release"
      project=":firebase_messaging"/>
  <library
      name=":@@:firebase_core::release"
      project=":firebase_core"/>
  <library
      name=":@@:flutter_facebook_auth::release"
      project=":flutter_facebook_auth"/>
  <library
      name=":@@:flutter_inappwebview_android::release"
      project=":flutter_inappwebview_android"/>
  <library
      name=":@@:flutter_local_notifications::release"
      project=":flutter_local_notifications"/>
  <library
      name=":@@:flutter_plugin_android_lifecycle::release"
      project=":flutter_plugin_android_lifecycle"/>
  <library
      name=":@@:flutter_secure_storage::release"
      project=":flutter_secure_storage"/>
  <library
      name=":@@:fluttertoast::release"
      project=":fluttertoast"/>
  <library
      name=":@@:geocoding_android::release"
      project=":geocoding_android"/>
  <library
      name=":@@:geolocator_android::release"
      project=":geolocator_android"/>
  <library
      name=":@@:google_maps_flutter_android::release"
      project=":google_maps_flutter_android"/>
  <library
      name=":@@:image_picker_android::release"
      project=":image_picker_android"/>
  <library
      name=":@@:package_info_plus::release"
      project=":package_info_plus"/>
  <library
      name=":@@:path_provider_android::release"
      project=":path_provider_android"/>
  <library
      name=":@@:razorpay_flutter::release"
      project=":razorpay_flutter"/>
  <library
      name=":@@:sign_in_with_apple::release"
      project=":sign_in_with_apple"/>
  <library
      name=":@@:sqflite_android::release"
      project=":sqflite_android"/>
  <library
      name=":@@:url_launcher_android::release"
      project=":url_launcher_android"/>
  <library
      name="io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\flutter_embedding_release\1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa\e49eb3292687e1b573c417f19ce5fb36ba995ecc\flutter_embedding_release-1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa.jar"
      resolved="io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa"/>
  <library
      name="com.razorpay:checkout:1.6.41@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff4f8aa8090204d5b186df1e1acdf455\transformed\jetified-checkout-1.6.41\jars\classes.jar"
      resolved="com.razorpay:checkout:1.6.41"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff4f8aa8090204d5b186df1e1acdf455\transformed\jetified-checkout-1.6.41"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.razorpay:standard-core:1.6.52@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\292150c9485399b885ca8c053b82f6ef\transformed\jetified-standard-core-1.6.52\jars\classes.jar"
      resolved="com.razorpay:standard-core:1.6.52"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\292150c9485399b885ca8c053b82f6ef\transformed\jetified-standard-core-1.6.52"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth:21.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth:21.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-wallet:19.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57d1206001d3e5c2badfd9d4afb4491b\transformed\jetified-play-services-wallet-19.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-wallet:19.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57d1206001d3e5c2badfd9d4afb4491b\transformed\jetified-play-services-wallet-19.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b13220e95e8f412f8fdf929ca7159aa\transformed\jetified-play-services-auth-api-phone-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:18.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b13220e95e8f412f8fdf929ca7159aa\transformed\jetified-play-services-auth-api-phone-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1330f9219eea973eec929af8fa1c5729\transformed\jetified-play-services-auth-base-18.0.10\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-base:18.0.10"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1330f9219eea973eec929af8fa1c5729\transformed\jetified-play-services-auth-base-18.0.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-fido:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3564b79b40c22f3f33ce1e04cdf96410\transformed\jetified-play-services-fido-21.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-fido:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3564b79b40c22f3f33ce1e04cdf96410\transformed\jetified-play-services-fido-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-identity:18.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\004fa7362352e036c134d8042f2f8404\transformed\jetified-play-services-identity-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-identity:18.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\004fa7362352e036c134d8042f2f8404\transformed\jetified-play-services-identity-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-maps:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-maps:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33efbd91567b410951e429bcd7c7e693\transformed\jetified-play-services-tasks-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33efbd91567b410951e429bcd7c7e693\transformed\jetified-play-services-tasks-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26cc97693447936b3b8f69f2a6fa2719\transformed\fragment-1.8.6\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26cc97693447936b3b8f69f2a6fa2719\transformed\fragment-1.8.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.9.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\321bbbd46a1a906c825850049835f2cc\transformed\jetified-activity-1.9.3\jars\classes.jar"
      resolved="androidx.activity:activity:1.9.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\321bbbd46a1a906c825850049835f2cc\transformed\jetified-activity-1.9.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6a448fa6a634c3addcc37a4d1432544\transformed\loader-1.1.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6a448fa6a634c3addcc37a4d1432544\transformed\loader-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\760fa2757aee2315891779219e9070ae\transformed\lifecycle-viewmodel-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\760fa2757aee2315891779219e9070ae\transformed\lifecycle-viewmodel-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adc1afef20e2984fa32029d820ec40e7\transformed\jetified-lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adc1afef20e2984fa32029d820ec40e7\transformed\jetified-lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a39702f207adeb782373e12772796a2a\transformed\lifecycle-livedata-core-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a39702f207adeb782373e12772796a2a\transformed\lifecycle-livedata-core-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4cc774d802693b2269c3dbae9460560\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4cc774d802693b2269c3dbae9460560\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f8922862e97c4ef75dd7104d7485cc3\transformed\jetified-core-ktx-1.16.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f8922862e97c4ef75dd7104d7485cc3\transformed\jetified-core-ktx-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22591c71b40a2c4172ffe9173f59d74\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22591c71b40a2c4172ffe9173f59d74\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e05f257c7d66e44604f38435100db65\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e05f257c7d66e44604f38435100db65\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\jars\classes.jar"
      resolved="androidx.core:core:1.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3c53645ee6d8e428687f190ceaae045\transformed\jetified-lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3c53645ee6d8e428687f190ceaae045\transformed\jetified-lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.8.7\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\lifecycle-common-jvm-2.8.7.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.8.7"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.8.7@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.8.7\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.8.7.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.8.7"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\jars\classes.jar"
      resolved="androidx.window:window:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window-java:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78a0a6a50cf5903f2e10e25d8e8cd3ea\transformed\jetified-window-java-1.2.0\jars\classes.jar"
      resolved="androidx.window:window-java:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78a0a6a50cf5903f2e10e25d8e8cd3ea\transformed\jetified-window-java-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\2.1.0\d77bee6c7bc849e7b9bf4b83e94e8fd1dfc0ea8a\kotlin-stdlib-jdk7-2.1.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e9138e740080f557ac80532bf22f0ff\transformed\jetified-annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e9138e740080f557ac80532bf22f0ff\transformed\jetified-annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84ebcea95efeacc174f19a164342adfc\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84ebcea95efeacc174f19a164342adfc\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67bfba0cf7ed9cf2e740339ee0eeb31f\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67bfba0cf7ed9cf2e740339ee0eeb31f\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.collection:collection-jvm:1.4.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.2\bc60b5568a66d765a9fe8e266fd0c6c727e0b50b\collection-jvm-1.4.2.jar"
      resolved="androidx.collection:collection-jvm:1.4.2"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40787840b62239417ec0cc6c32dd9937\transformed\jetified-core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40787840b62239417ec0cc6c32dd9937\transformed\jetified-core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.10.1\fe066928754beda3d59c8282e04289546465a360\kotlinx-coroutines-core-jvm-1.10.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.10.1\864006e2e42ed4d76150651c635d038a060c20a2\kotlinx-coroutines-android-1.10.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.1.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.1.10\d3028429e7151d7a7c1a0d63a4f60eac86a87b91\kotlin-stdlib-2.1.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.1.10"/>
  <library
      name="io.flutter:armeabi_v7a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\armeabi_v7a_release\1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa\4a1622e4221ab8ec37d49ea9525f6bac5bffb95e\armeabi_v7a_release-1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa.jar"
      resolved="io.flutter:armeabi_v7a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa"/>
  <library
      name="io.flutter:arm64_v8a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\arm64_v8a_release\1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa\93179db9fcde85c97d8080f6eba90a3a219e1d7\arm64_v8a_release-1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa.jar"
      resolved="io.flutter:arm64_v8a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa"/>
  <library
      name="io.flutter:x86_64_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\x86_64_release\1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa\92310057aa1fe51d117a6f550f682f1994f14d8d\x86_64_release-1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa.jar"
      resolved="io.flutter:x86_64_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e375d717563e75bcda864b436ff0ef7e\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e375d717563e75bcda864b436ff0ef7e\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55768584dec4ba0be643d3fb626f8713\transformed\jetified-tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55768584dec4ba0be643d3fb626f8713\transformed\jetified-tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jspecify:jspecify:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jspecify\jspecify\1.0.0\7425a601c1c7ec76645a78d22b8c6a627edee507\jspecify-1.0.0.jar"
      resolved="org.jspecify:jspecify:1.0.0"/>
  <library
      name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ac09cd70c1722cbd681cddc96971cbd\transformed\jetified-relinker-1.4.5\jars\classes.jar"
      resolved="com.getkeepsafe.relinker:relinker:1.4.5"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ac09cd70c1722cbd681cddc96971cbd\transformed\jetified-relinker-1.4.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.android:facebook-login:18.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca6706086df4d54b31c3004876d79fd4\transformed\jetified-facebook-login-18.0.3\jars\classes.jar"
      resolved="com.facebook.android:facebook-login:18.0.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca6706086df4d54b31c3004876d79fd4\transformed\jetified-facebook-login-18.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.webkit:webkit:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acea059c377a46540689cab85dde90cd\transformed\webkit-1.12.0\jars\classes.jar"
      resolved="androidx.webkit:webkit:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acea059c377a46540689cab85dde90cd\transformed\webkit-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.stripe:financial-connections:21.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\jars\classes.jar"
      resolved="com.stripe:financial-connections:21.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.android:facebook-common:18.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\jars\classes.jar"
      resolved="com.facebook.android:facebook-common:18.0.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.stripe:stripe-android:21.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cd974a17593dc6d6688e8906d100e3f\transformed\jetified-stripe-android-21.6.0\jars\classes.jar"
      resolved="com.stripe:stripe-android:21.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cd974a17593dc6d6688e8906d100e3f\transformed\jetified-stripe-android-21.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.stripe:paymentsheet:21.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\jars\classes.jar"
      resolved="com.stripe:paymentsheet:21.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.stripe:payments-ui-core:21.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\jars\classes.jar"
      resolved="com.stripe:payments-ui-core:21.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.stripe:payments-core:21.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\jars\classes.jar"
      resolved="com.stripe:payments-core:21.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.stripe:stripe-ui-core:21.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b05c15e4c638bbe5f218480c9cf74bd\transformed\jetified-stripe-ui-core-21.6.0\jars\classes.jar"
      resolved="com.stripe:stripe-ui-core:21.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b05c15e4c638bbe5f218480c9cf74bd\transformed\jetified-stripe-ui-core-21.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.stripe:payments-model:21.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96a01676df5203a3b615fbb3718261a\transformed\jetified-payments-model-21.6.0\jars\classes.jar"
      resolved="com.stripe:payments-model:21.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96a01676df5203a3b615fbb3718261a\transformed\jetified-payments-model-21.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.stripe:stripe-core:21.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\transformed\jetified-stripe-core-21.6.0\jars\classes.jar"
      resolved="com.stripe:stripe-core:21.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\transformed\jetified-stripe-core-21.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3c723f32cc31c3d5ce9263682ffa8b7\transformed\browser-1.8.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3c723f32cc31c3d5ce9263682ffa8b7\transformed\browser-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-v4:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a3a4a579b2ef5f1ea25f0994a4694df\transformed\legacy-support-v4-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-v4:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a3a4a579b2ef5f1ea25f0994a4694df\transformed\legacy-support-v4-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d63c9f981e85b091b6acb23f12ae70\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d63c9f981e85b091b6acb23f12ae70\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a787c0803660539a92495da5d31ea17e\transformed\swiperefreshlayout-1.1.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a787c0803660539a92495da5d31ea17e\transformed\swiperefreshlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb71e3b60d9eed6cadfc1a249b13d149\transformed\media-1.1.0\jars\classes.jar"
      resolved="androidx.media:media:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb71e3b60d9eed6cadfc1a249b13d149\transformed\media-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials-play-services-auth:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\jars\classes.jar"
      resolved="androidx.credentials:credentials-play-services-auth:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.libraries.identity.googleid:googleid:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08d12d7e609da5f0d48d54670ec0b8e6\transformed\jetified-googleid-1.1.1\jars\classes.jar"
      resolved="com.google.android.libraries.identity.googleid:googleid:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08d12d7e609da5f0d48d54670ec0b8e6\transformed\jetified-googleid-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3c1b17a9ffaafd0471e6527f4794f6b\transformed\jetified-credentials-1.5.0\jars\classes.jar"
      resolved="androidx.credentials:credentials:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3c1b17a9ffaafd0471e6527f4794f6b\transformed\jetified-credentials-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.biometric:biometric:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\jars\classes.jar"
      resolved="androidx.biometric:biometric:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.preference:preference:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b0e46e5034b62169defac2cb4fe8fb\transformed\preference-1.2.1\jars\classes.jar"
      resolved="androidx.preference:preference:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b0e46e5034b62169defac2cb4fe8fb\transformed\preference-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c650d55ab1e3e720524d3af207cfc6f\transformed\navigation-common-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-common:2.7.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c650d55ab1e3e720524d3af207cfc6f\transformed\navigation-common-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34a503dc6d29c2c14429a9f85ae2807\transformed\navigation-runtime-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime:2.7.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34a503dc6d29c2c14429a9f85ae2807\transformed\navigation-runtime-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common-ktx:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc8f54f5d96ca582d8e67ebc5a19ac09\transformed\navigation-common-ktx-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-common-ktx:2.7.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc8f54f5d96ca582d8e67ebc5a19ac09\transformed\navigation-common-ktx-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime-ktx:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d68662e109539e5ec3b632e8f6d8c967\transformed\navigation-runtime-ktx-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime-ktx:2.7.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d68662e109539e5ec3b632e8f6d8c967\transformed\navigation-runtime-ktx-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-compose:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14daecc3ebd000f4593d0172df64931b\transformed\jetified-navigation-compose-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-compose:2.7.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14daecc3ebd000f4593d0172df64931b\transformed\jetified-navigation-compose-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.9.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfe8956b361855791638c0b764997d4e\transformed\jetified-activity-compose-1.9.3\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.9.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfe8956b361855791638c0b764997d4e\transformed\jetified-activity-compose-1.9.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.stripe:stripe-3ds2-android:6.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\jars\classes.jar"
      resolved="com.stripe:stripe-3ds2-android:6.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.accompanist:accompanist-systemuicontroller:0.34.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\458b5494eafd4996cd399e5b49a9df4c\transformed\jetified-accompanist-systemuicontroller-0.34.0\jars\classes.jar"
      resolved="com.google.accompanist:accompanist-systemuicontroller:0.34.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\458b5494eafd4996cd399e5b49a9df4c\transformed\jetified-accompanist-systemuicontroller-0.34.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.pay.button:compose-pay-button:0.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f46722f2ae74414dc7e075cd66b8597\transformed\jetified-compose-pay-button-0.1.3\jars\classes.jar"
      resolved="com.google.pay.button:compose-pay-button:0.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f46722f2ae74414dc7e075cd66b8597\transformed\jetified-compose-pay-button-0.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.accompanist:accompanist-themeadapter-appcompat:0.34.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ae34fd4eb7d9b72497105893ff93e83\transformed\jetified-accompanist-themeadapter-appcompat-0.34.0\jars\classes.jar"
      resolved="com.google.accompanist:accompanist-themeadapter-appcompat:0.34.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ae34fd4eb7d9b72497105893ff93e83\transformed\jetified-accompanist-themeadapter-appcompat-0.34.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.accompanist:accompanist-themeadapter-material:0.34.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01336241c67244a97fb4d45728317cf6\transformed\jetified-accompanist-themeadapter-material-0.34.0\jars\classes.jar"
      resolved="com.google.accompanist:accompanist-themeadapter-material:0.34.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01336241c67244a97fb4d45728317cf6\transformed\jetified-accompanist-themeadapter-material-0.34.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.accompanist:accompanist-themeadapter-material3:0.34.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23dae27af0289147ae3cee436f1f9c59\transformed\jetified-accompanist-themeadapter-material3-0.34.0\jars\classes.jar"
      resolved="com.google.accompanist:accompanist-themeadapter-material3:0.34.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23dae27af0289147ae3cee436f1f9c59\transformed\jetified-accompanist-themeadapter-material3-0.34.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material3:material3:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb54734c218ce176bffcbd48481aca79\transformed\jetified-material3-1.0.1\jars\classes.jar"
      resolved="androidx.compose.material3:material3:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb54734c218ce176bffcbd48481aca79\transformed\jetified-material3-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e34cf6b06a41f14fb0ceaffd38f45e53\transformed\jetified-material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e34cf6b06a41f14fb0ceaffd38f45e53\transformed\jetified-material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2985daea627f0dec971a8ff5a48e644a\transformed\jetified-material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2985daea627f0dec971a8ff5a48e644a\transformed\jetified-material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\292fe153afec31a88f60505c2f9c1809\transformed\jetified-material-release\jars\classes.jar"
      resolved="androidx.compose.material:material-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\292fe153afec31a88f60505c2f9c1809\transformed\jetified-material-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.accompanist:accompanist-themeadapter-core:0.34.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2120daa35dbbaf918041c277632ab66b\transformed\jetified-accompanist-themeadapter-core-0.34.0\jars\classes.jar"
      resolved="com.google.accompanist:accompanist-themeadapter-core:0.34.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2120daa35dbbaf918041c277632ab66b\transformed\jetified-accompanist-themeadapter-core-0.34.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.accompanist:accompanist-flowlayout:0.34.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a646fcf2c5427a40a6bfed16eecfa6e3\transformed\jetified-accompanist-flowlayout-0.34.0\jars\classes.jar"
      resolved="com.google.accompanist:accompanist-flowlayout:0.34.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a646fcf2c5427a40a6bfed16eecfa6e3\transformed\jetified-accompanist-flowlayout-0.34.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25e6373e571a19f98d64d75f1f32810\transformed\jetified-animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25e6373e571a19f98d64d75f1f32810\transformed\jetified-animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a9760ed3571a1c2428d4c5ae6df07a4\transformed\jetified-animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a9760ed3571a1c2428d4c5ae6df07a4\transformed\jetified-animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30287e20ba85be6ba7bac4fc26404cb6\transformed\jetified-foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30287e20ba85be6ba7bac4fc26404cb6\transformed\jetified-foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732d08034aa94dff057adb759b93ca56\transformed\jetified-foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732d08034aa94dff057adb759b93ca56\transformed\jetified-foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43052af7f9775fbf90804a1b628e4dad\transformed\jetified-ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43052af7f9775fbf90804a1b628e4dad\transformed\jetified-ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6c8d7cc1af8f2ca2f5d6b3f9f1ec167\transformed\jetified-ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6c8d7cc1af8f2ca2f5d6b3f9f1ec167\transformed\jetified-ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16a42088a31edaba552ff082f0660291\transformed\jetified-ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16a42088a31edaba552ff082f0660291\transformed\jetified-ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d450bae7c21a07703659a23e148691d\transformed\jetified-ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d450bae7c21a07703659a23e148691d\transformed\jetified-ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32eacbaf6163f6900b1209ad53185332\transformed\jetified-ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32eacbaf6163f6900b1209ad53185332\transformed\jetified-ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b154d7bdb63c654ee93416929c1f009d\transformed\jetified-ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b154d7bdb63c654ee93416929c1f009d\transformed\jetified-ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.android:facebook-core:18.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\jars\classes.jar"
      resolved="com.facebook.android:facebook-core:18.0.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-messaging:24.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-messaging:24.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6dfcec072e49025e6fe7486ae8e78b5\transformed\jetified-play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6dfcec072e49025e6fe7486ae8e78b5\transformed\jetified-play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be2b43e4377e03d598e671e01a23c196\transformed\material-1.12.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be2b43e4377e03d598e671e01a23c196\transformed\material-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99f04d5ed1f58b1cb06a2785f5ce6b91\transformed\slidingpanelayout-1.2.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99f04d5ed1f58b1cb06a2785f5ce6b91\transformed\slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5d2f88374dd98138a4945549e1432a2\transformed\transition-1.5.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5d2f88374dd98138a4945549e1432a2\transformed\transition-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f48494b431258a5a46659ee4e96a961\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f48494b431258a5a46659ee4e96a961\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71234f4db869dea16b06e87b8a83d62f\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71234f4db869dea16b06e87b8a83d62f\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:glide:4.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0c1d510e1d5509900d56081a4c9069a\transformed\jetified-glide-4.12.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0c1d510e1d5509900d56081a4c9069a\transformed\jetified-glide-4.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-location:21.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dd21728cb620282904f394395e0fd9e\transformed\jetified-play-services-location-21.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-location:21.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dd21728cb620282904f394395e0fd9e\transformed\jetified-play-services-location-21.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-blockstore:16.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e42d76d94f1fcdeedaed7068e5399e7\transformed\jetified-play-services-auth-blockstore-16.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-blockstore:16.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e42d76d94f1fcdeedaed7068e5399e7\transformed\jetified-play-services-auth-blockstore-16.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\767b70ede55bbe0998786b69d70bb8f1\transformed\jetified-play-services-identity-credentials-16.0.0-alpha02\jars\classes.jar"
      resolved="com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\767b70ede55bbe0998786b69d70bb8f1\transformed\jetified-play-services-identity-credentials-16.0.0-alpha02"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\300d6f94e6d1b4f45d88e5a7a0d3fba2\transformed\jetified-firebase-iid-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\300d6f94e6d1b4f45d88e5a7a0d3fba2\transformed\jetified-firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad1e04f1d12e58d3782d56fbc518fb\transformed\jetified-play-services-cloud-messaging-17.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad1e04f1d12e58d3782d56fbc518fb\transformed\jetified-play-services-cloud-messaging-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86612afe8bee028d49a7fa5c79e9601e\transformed\constraintlayout-2.2.0\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86612afe8bee028d49a7fa5c79e9601e\transformed\constraintlayout-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.stripe:hcaptcha:21.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37a9eea61f7f246731189c96a915165d\transformed\jetified-hcaptcha-21.6.0\jars\classes.jar"
      resolved="com.stripe:hcaptcha:21.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37a9eea61f7f246731189c96a915165d\transformed\jetified-hcaptcha-21.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbb5513571c3ecc71eebf495a5d90e8d\transformed\jetified-appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbb5513571c3ecc71eebf495a5d90e8d\transformed\jetified-appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\190cb7abb318e85f1c79a4fa923f65ed\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\190cb7abb318e85f1c79a4fa923f65ed\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cafb69718dab122b155aedd4bb28a61\transformed\jetified-emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cafb69718dab122b155aedd4bb28a61\transformed\jetified-emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d04a35a4ee37ede57595a177ed10562\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d04a35a4ee37ede57595a177ed10562\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7706ff15a2500248677ad2acd47b5be2\transformed\lifecycle-livedata-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7706ff15a2500248677ad2acd47b5be2\transformed\lifecycle-livedata-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\644cbcfa005681d6d7ac5def34be967c\transformed\jetified-lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\644cbcfa005681d6d7ac5def34be967c\transformed\jetified-lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-ktx:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd518ec8be27b28a26e8c21522082392\transformed\jetified-lifecycle-livedata-ktx-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-ktx:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd518ec8be27b28a26e8c21522082392\transformed\jetified-lifecycle-livedata-ktx-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ee55f53c2bafc18ef88b5208b82f5dc\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ee55f53c2bafc18ef88b5208b82f5dc\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\215f629f88e644b2aa4d00c7e198828f\transformed\jetified-runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\215f629f88e644b2aa4d00c7e198828f\transformed\jetified-runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d1cf6f7265eabe26b6437df8cdcfdb\transformed\jetified-runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d1cf6f7265eabe26b6437df8cdcfdb\transformed\jetified-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-external-protobuf\1.1.3\ddd0a2d64e3c928359c993d1291e535d5d7fc9a3\datastore-preferences-external-protobuf-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-external-protobuf:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-proto\1.1.3\6d7430ed8d2b5f2b8675dad8d196ba5dd710921b\datastore-preferences-proto-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-proto:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-core-jvm\1.1.3\fb991f11389ccf2a5d5d4c99783ff958bc400\datastore-preferences-core-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-core-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-core-okio-jvm\1.1.3\ce08f132812044a9778547b299fd812e34dbd602\datastore-core-okio-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-core-okio-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-android:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3124575ae01a8c110bc8e5f0b157eb92\transformed\jetified-datastore-core-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-core-android:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3124575ae01a8c110bc8e5f0b157eb92\transformed\jetified-datastore-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e882cde7a3e0600d970431e6c1b2b1a3\transformed\jetified-datastore-preferences-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-preferences-android:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e882cde7a3e0600d970431e6c1b2b1a3\transformed\jetified-datastore-preferences-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-android:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a223f5d2b2514ecd1dd09366bea71d2\transformed\jetified-datastore-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-android:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a223f5d2b2514ecd1dd09366bea71d2\transformed\jetified-datastore-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.stripe:attestation:21.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee148be6b377a3a4a7c6c54ff4e131b\transformed\jetified-attestation-21.6.0\jars\classes.jar"
      resolved="com.stripe:attestation:21.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee148be6b377a3a4a7c6c54ff4e131b\transformed\jetified-attestation-21.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.10.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.10.1\da0d3fb6ecb374d22c20b63ee8ed6e39466c4232\kotlinx-coroutines-play-services-1.10.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.10.1"/>
  <library
      name="com.google.android.play:integrity:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd85043377d2540f00416437fb2ac7e9\transformed\jetified-integrity-1.4.0\jars\classes.jar"
      resolved="com.google.android.play:integrity:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd85043377d2540f00416437fb2ac7e9\transformed\jetified-integrity-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1732276c7eaabf8fe7c8d4461b851e3\transformed\jetified-firebase-installations-interop-17.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1732276c7eaabf8fe7c8d4461b851e3\transformed\jetified-firebase-installations-interop-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e237f1b1c6c966f47bd0f9574b81fa0\transformed\jetified-firebase-measurement-connector-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e237f1b1c6c966f47bd0f9574b81fa0\transformed\jetified-firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.3.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab628adf0beae462834b52c12b84464e\transformed\recyclerview-1.3.2\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.3.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab628adf0beae462834b52c12b84464e\transformed\recyclerview-1.3.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcdbeef9428bcf2eaf588e2bb181b879\transformed\jetified-viewpager2-1.1.0-beta02\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.1.0-beta02"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcdbeef9428bcf2eaf588e2bb181b879\transformed\jetified-viewpager2-1.1.0-beta02"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e74b3890f1d6d9c05eafca39c2cf5127\transformed\jetified-lifecycle-runtime-ktx-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e74b3890f1d6d9c05eafca39c2cf5127\transformed\jetified-lifecycle-runtime-ktx-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fcf367accd9adb2a40b05c1230eee73\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fcf367accd9adb2a40b05c1230eee73\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f5d39c01e455345a99b57c9c4aea631\transformed\jetified-lifecycle-viewmodel-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f5d39c01e455345a99b57c9c4aea631\transformed\jetified-lifecycle-viewmodel-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ff12406a05f415564f57bbfef1a99d3\transformed\jetified-ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ff12406a05f415564f57bbfef1a99d3\transformed\jetified-ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-viewbinding:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5153fc03d74991eab2b8e9ac53a5efa5\transformed\jetified-ui-viewbinding-1.6.8\jars\classes.jar"
      resolved="androidx.compose.ui:ui-viewbinding:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5153fc03d74991eab2b8e9ac53a5efa5\transformed\jetified-ui-viewbinding-1.6.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\885fb369f15d0f07f12fb71f12cd937d\transformed\jetified-fragment-ktx-1.8.6\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\885fb369f15d0f07f12fb71f12cd937d\transformed\jetified-fragment-ktx-1.8.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.9.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\814c617f1ac9ff012bf36e3770a4bced\transformed\jetified-activity-ktx-1.9.3\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.9.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\814c617f1ac9ff012bf36e3770a4bced\transformed\jetified-activity-ktx-1.9.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.android:facebook-bolts:18.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04b9cb770066efbcdc5cffbe90480302\transformed\jetified-facebook-bolts-18.0.3\jars\classes.jar"
      resolved="com.facebook.android:facebook-bolts:18.0.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04b9cb770066efbcdc5cffbe90480302\transformed\jetified-facebook-bolts-18.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\490ca861c3f775c52464c68b2c18db32\transformed\jetified-customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\490ca861c3f775c52464c68b2c18db32\transformed\jetified-customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\355c5ab9bc86f03453b3d02ef56e8fec\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\355c5ab9bc86f03453b3d02ef56e8fec\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9481e3ee1e591d121d9517f5f35bad98\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9481e3ee1e591d121d9517f5f35bad98\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92534fa39dacee9443b9933684a4e7d9\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92534fa39dacee9443b9933684a4e7d9\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad0a9145586392a0f19d737a934a6cb\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad0a9145586392a0f19d737a934a6cb\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dce355c3308ed0f85deebc0cec080522\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dce355c3308ed0f85deebc0cec080522\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d90c6124f0a7904581e279d68faa177d\transformed\jetified-autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d90c6124f0a7904581e279d68faa177d\transformed\jetified-autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.airbnb.android:showkase-annotation:1.0.0-beta18@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.airbnb.android\showkase-annotation\1.0.0-beta18\e2733db38005d2d442b2b3c944896a7bab57deb8\showkase-annotation-1.0.0-beta18.jar"
      resolved="com.airbnb.android:showkase-annotation:1.0.0-beta18"/>
  <library
      name="com.squareup.okio:okio-jvm:3.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.4.0\4e8bd78a52ab935ce383d0092646922154295e54\okio-jvm-3.4.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.4.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\2.1.0\c4a51be16c840b969ed706bc25d5836b099d188a\kotlin-stdlib-jdk8-2.1.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.1.0"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01fdda772c29d097e47e7e07b6ab574d\transformed\localbroadcastmanager-1.1.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01fdda772c29d097e47e7e07b6ab574d\transformed\localbroadcastmanager-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.security:security-crypto:1.1.0-alpha06@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3e2433c84cb36b07e14cfcc9de7fb0\transformed\jetified-security-crypto-1.1.0-alpha06\jars\classes.jar"
      resolved="androidx.security:security-crypto:1.1.0-alpha06"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3e2433c84cb36b07e14cfcc9de7fb0\transformed\jetified-security-crypto-1.1.0-alpha06"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.crypto.tink:tink-android:1.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.crypto.tink\tink-android\1.9.0\4c3bb230542a11ad51492cd9913979466b56fa16\tink-android-1.9.0.jar"
      resolved="com.google.crypto.tink:tink-android:1.9.0"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9b2d8195f5fa3122717d67e294e0984\transformed\exifinterface-1.3.7\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9b2d8195f5fa3122717d67e294e0984\transformed\exifinterface-1.3.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db5836b85779fdc96b81a6716c0188c3\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db5836b85779fdc96b81a6716c0188c3\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1475968be37fc1cc0d45185eaabae93f\transformed\jetified-gifdecoder-4.12.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1475968be37fc1cc0d45185eaabae93f\transformed\jetified-gifdecoder-4.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48f5055102bd9d9e4b56e3462bbf2d4\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48f5055102bd9d9e4b56e3462bbf2d4\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0238e3b1342e1795e945ca7826bc3d08\transformed\jetified-firebase-components-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0238e3b1342e1795e945ca7826bc3d08\transformed\jetified-firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-datatransport:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-datatransport:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\827d0526913e6e77c29cfb8917d798c3\transformed\jetified-firebase-encoders-json-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\827d0526913e6e77c29cfb8917d798c3\transformed\jetified-firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.android.datatransport:transport-api:3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7af2d6d8446aedcc11b15bf921f193f3\transformed\jetified-transport-api-3.1.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7af2d6d8446aedcc11b15bf921f193f3\transformed\jetified-transport-api-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="androidx.collection:collection-ktx:1.4.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.2\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.2.jar"
      resolved="androidx.collection:collection-ktx:1.4.2"/>
  <library
      name="androidx.databinding:viewbinding:8.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2382091193301cf94b5acbc1816adf9\transformed\jetified-viewbinding-8.8.1\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:8.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2382091193301cf94b5acbc1816adf9\transformed\jetified-viewbinding-8.8.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22c780435a20ee8d79bdb03390db482b\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22c780435a20ee8d79bdb03390db482b\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\815128c6d80d05f19bc4f30c4e96e4da\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\815128c6d80d05f19bc4f30c4e96e4da\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c3dbe816f7c0de1f0839c6d8a557e29\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c3dbe816f7c0de1f0839c6d8a557e29\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window.extensions.core:core:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8967af0d0d410bf0eecf0cb191f0850\transformed\jetified-core-1.0.0\jars\classes.jar"
      resolved="androidx.window.extensions.core:core:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8967af0d0d410bf0eecf0cb191f0850\transformed\jetified-core-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout-core:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-core\1.1.0\e678372aacb0e91f0ed44854a8a413fc93283050\constraintlayout-core-1.1.0.jar"
      resolved="androidx.constraintlayout:constraintlayout-core:1.1.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad68493bc82d5a49585ae8c975c7e495\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad68493bc82d5a49585ae8c975c7e495\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.8.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-core-jvm\1.8.0\fe5bb37ab4a3771f14c63e0c333c98bdfeb1d7cc\kotlinx-serialization-core-jvm-1.8.0.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.8.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.8.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-json-jvm\1.8.0\8253bf6e8c713a3dace54493a838e0f8fd3e08fa\kotlinx-serialization-json-jvm-1.8.0.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.8.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\2.0.21\8947a70750a8552acfe84ad5703e16bf072d9368\kotlin-parcelize-runtime-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21"/>
  <library
      name="dev.drewhamilton.poko:poko-annotations-jvm:0.18.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\dev.drewhamilton.poko\poko-annotations-jvm\0.18.2\6f4c8636d8ad2262d3e3ae4894cc374e5830f822\poko-annotations-jvm-0.18.2.jar"
      resolved="dev.drewhamilton.poko:poko-annotations-jvm:0.18.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\2.0.21\ec0f8b769af81cd91a76b530399684a9567bdc35\kotlin-android-extensions-runtime-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21"/>
  <library
      name="com.google.code.gson:gson:2.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.12.0\10596b68aaca6230f7c40bfd9298b21ff4b84103\gson-2.12.0.jar"
      resolved="com.google.code.gson:gson:2.12.0"/>
  <library
      name="com.google.maps.android:android-maps-utils:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\606e56decca20cee599de108723d7ece\transformed\jetified-android-maps-utils-3.6.0\jars\classes.jar"
      resolved="com.google.maps.android:android-maps-utils:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\606e56decca20cee599de108723d7ece\transformed\jetified-android-maps-utils-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.36.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.36.0\227d4d4957ccc3dc5761bd897e3a0ee587e750a7\error_prone_annotations-2.36.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.36.0"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="com.google.dagger:dagger:2.55@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.dagger\dagger\2.55\2865c6ae22d2911183058e96ed757a355a25b53\dagger-2.55.jar"
      resolved="com.google.dagger:dagger:2.55"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.12.0\6d44c9103551e3ff51f00ac26077c143a8fb74f9\disklrucache-4.12.0.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.12.0"/>
  <library
      name="com.github.bumptech.glide:annotations:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.12.0\298d793db124809d74ad9a90de5eddc408898c84\annotations-4.12.0.jar"
      resolved="com.github.bumptech.glide:annotations:4.12.0"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="com.android.installreferrer:installreferrer:1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8d47debb03072941b4414566260c2d\transformed\jetified-installreferrer-1.0\jars\classes.jar"
      resolved="com.android.installreferrer:installreferrer:1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8d47debb03072941b4414566260c2d\transformed\jetified-installreferrer-1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.zxing:core:3.3.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.zxing\core\3.3.3\b640badcc97f18867c4dfd249ef8d20ec0204c07\core-3.3.3.jar"
      resolved="com.google.zxing:core:3.3.3"/>
  <library
      name="com.google.android.instantapps:instantapps:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0\jars\classes.jar"
      resolved="com.google.android.instantapps:instantapps:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.jakewharton:disklrucache:2.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.jakewharton\disklrucache\2.0.2\93b399470e72c0bc8cb52379943b63c9aa586cd8\disklrucache-2.0.2.jar"
      resolved="com.jakewharton:disklrucache:2.0.2"/>
  <library
      name="jakarta.inject:jakarta.inject-api:2.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\jakarta.inject\jakarta.inject-api\2.0.1\4c28afe1991a941d7702fe1362c365f0a8641d1e\jakarta.inject-api-2.0.1.jar"
      resolved="jakarta.inject:jakarta.inject-api:2.0.1"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="org.bouncycastle:bcprov-jdk15to18:1.79@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.bouncycastle\bcprov-jdk15to18\1.79\ffb3ff552ca56ac4af9a3927ebe1388f900ab283\bcprov-jdk15to18-1.79.jar"
      resolved="org.bouncycastle:bcprov-jdk15to18:1.79"/>
  <library
      name="com.nimbusds:nimbus-jose-jwt:9.46@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.nimbusds\nimbus-jose-jwt\9.46\889fd74382343ea4e3960db48736afb32f4c510d\nimbus-jose-jwt-9.46.jar"
      resolved="com.nimbusds:nimbus-jose-jwt:9.46"/>
  <library
      name="com.google.android.play:core-common:2.0.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\jars\classes.jar"
      resolved="com.google.android.play:core-common:2.0.4"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>

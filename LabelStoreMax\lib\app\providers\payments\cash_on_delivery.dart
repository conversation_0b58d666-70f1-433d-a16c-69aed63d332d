//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import '/resources/pages/checkout_status_page.dart';
import '/bootstrap/data/order_wc.dart';
import '/app/models/cart.dart';
import '/resources/widgets/cart_quantity_widget.dart';
import '/resources/pages/checkout_confirmation_page.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '../../services/woocommerce_service.dart';


cashOnDeliveryPay(context) async {
  try {
    print('🌐 ===== CASH ON DELIVERY CHECKOUT STARTED =====');

    Map<String, dynamic> orderData = await buildOrderWC(markPaid: false);

    // LOG ORDER DETAILS BEFORE SUBMISSION
    print('🌐 ===== ORDER CREATION REQUEST =====');
    print('📍 URL: https://velvete.ly/wp-json/wc/v3/orders');
    print('🔧 METHOD: POST');
    print('📦 ORDER DATA (Raw Map):');
    print('   Payment Method: ${orderData['payment_method']}');
    print('   Payment Method Title: ${orderData['payment_method_title']}');
    print('   Status: ${orderData['status']}');
    print('   Set Paid: ${orderData['set_paid']}');
    print('   Customer Note: ${orderData['customer_note']}');
    print('   Line Items Count: ${(orderData['line_items'] as List?)?.length ?? 0}');
    if (orderData['line_items'] is List && (orderData['line_items'] as List).isNotEmpty) {
      List lineItems = orderData['line_items'] as List;
      for (int i = 0; i < lineItems.length; i++) {
        var item = lineItems[i];
        print('     Item $i: ${item['name']} (ID: ${item['product_id']}, Qty: ${item['quantity']}, Total: ${item['total']})');
      }
    }
    print('==================================');

    // MASTER'S DEBUG: Log shipping details before order creation
    print('🔍 ===== SHIPPING DETAILS BEFORE ORDER CREATION =====');
    if (orderData['shipping_lines'] is List && (orderData['shipping_lines'] as List).isNotEmpty) {
      print('✅ Shipping Lines Present:');
      List shippingLines = orderData['shipping_lines'] as List;
      for (int i = 0; i < shippingLines.length; i++) {
        var shippingLine = shippingLines[i];
        print('   Line $i:');
        print('     Method ID: ${shippingLine['method_id']}');
        print('     Method Title: ${shippingLine['method_title']}');
        print('     Total: ${shippingLine['total']}');
      }
    } else {
      print('❌ NO SHIPPING LINES - This will cause "Shipping is required" error!');
    }

    if (orderData['shipping'] is Map) {
      Map shipping = orderData['shipping'] as Map;
      print('✅ Shipping Address Present:');
      print('   Name: ${shipping['first_name']} ${shipping['last_name']}');
      print('   Address: ${shipping['address_1']}');
      print('   City: ${shipping['city']}');
      print('   Country: ${shipping['country']}');
    } else {
      print('❌ NO SHIPPING ADDRESS');
    }
    print('================================================');

    // PHASE 11: ULTIMATE BYPASS - Use raw Map directly (no WooOrder conversion needed)
    print('🚀 PHASE 11: Using perfectly cleansed raw Map directly');
    print('🔍 Raw order map ready with ${orderData.length} fields');

    // Call the new direct API function with raw Map
    dynamic orderResponse = await WooCommerceService().createOrderDirectly(orderData);

    // PHASE 11: Work directly with raw Map response (no WooOrder conversion)
    print('🚀 PHASE 11: Working directly with raw Map response');

    // LOG ORDER CREATION RESPONSE
    print('🌐 ===== ORDER CREATION RESPONSE =====');
    if (orderResponse != null && orderResponse.containsKey('id')) {
      print('✅ SUCCESS - Order created');
      print('📊 Order ID: ${orderResponse['id']}');
      print('📧 Order Number: ${orderResponse['number']}');
      print('💰 Order Total: ${orderResponse['total']}');
      print('📋 Order Status: ${orderResponse['status']}');
    } else {
      print('❌ FAILED - Order creation returned null or missing ID');
      print('📦 Full Order Response: $orderResponse');
    }
    print('===================================');

    // **NEW: Clear the cart and update the UI cart icon**
    await Cart.getInstance.clear();
    updateState(CartQuantity.state);

    // **NEW: Show a success notification**
    showToastNotification(
        context,
        title: trans("Success"),
        description: trans("Your order has been placed successfully."),
        style: ToastNotificationStyleType.success
    );

    if (orderResponse == null || !orderResponse.containsKey('id')) {
      print('❌ CHECKOUT FAILED: Order response is null or missing ID');
      showToastNotification(
        context,
        title: trans("Error"),
        description: trans("Something went wrong, please contact our store"),
      );
      updateState(CheckoutConfirmationPage.path.nyPageName(),
          data: {"reloadState": false});
      return;
    }

    print('✅ CHECKOUT SUCCESS: Navigating to status page');
    routeTo(CheckoutStatusPage.path, data: orderResponse);
  } catch (e, stackTrace) {
    print('❌ CHECKOUT EXCEPTION: $e');
    print('📋 Stack Trace: $stackTrace');
    showToastNotification(
      context,
      title: trans("Error"),
      description: trans("Something went wrong, please contact our store"),
      style: ToastNotificationStyleType.danger
    );
    updateState(CheckoutConfirmationPage.path.nyPageName(),
        data: {"reloadState": false});
  }
}

{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3195,3287,3386,3480,3574,3667,3760,12364", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3282,3381,3475,3569,3662,3755,3851,12460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,183,244,305,374,428,488,536,601,667,735,819,903,976,1045,1115,1186,1266,1345,1408,1484,1557,1627,1710,1780,1856,1926,2009,2074,2149,2222,2291,2360,2438,2498,2565,2657,2742,2805,2866,3193,3510,3575,3659,3739,3794,3870,3942,4000,4071,4144,4199,4269,4314,4384,4466,4523,4588,4655,4722,4766,4830,4907,4971,5014,5057,5112,5170,5228,5319,5411,5488,5548,5611,5670,5745,5808,5868,5930,6001,6059,6133,6202,6279,6328,6401,6446,6496,6552,6608,6669,6728,6789,6860,6919,6964", "endColumns": "59,67,60,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,69,82,69,75,69,82,64,74,72,68,68,77,59,66,91,84,62,60,326,316,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,68,76,48,72,44,49,55,55,60,58,60,70,58,44,61", "endOffsets": "110,178,239,300,369,423,483,531,596,662,730,814,898,971,1040,1110,1181,1261,1340,1403,1479,1552,1622,1705,1775,1851,1921,2004,2069,2144,2217,2286,2355,2433,2493,2560,2652,2737,2800,2861,3188,3505,3570,3654,3734,3789,3865,3937,3995,4066,4139,4194,4264,4309,4379,4461,4518,4583,4650,4717,4761,4825,4902,4966,5009,5052,5107,5165,5223,5314,5406,5483,5543,5606,5665,5740,5803,5863,5925,5996,6054,6128,6197,6274,6323,6396,6441,6491,6547,6603,6664,6723,6784,6855,6914,6959,7021"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,321,322,324,325,329,330,332,337,344,345,416,417,418,420,425,438,439,440,441,442,443,444,460", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12939,12999,13067,13128,13189,13258,13312,13558,13606,13671,13737,13936,14091,14175,14312,14637,14898,15841,16054,16133,16196,16332,16405,16475,16615,16877,16953,17023,17106,17171,17246,17319,17388,17457,17535,17668,17871,17963,18530,18593,18654,19684,20001,20066,20150,20230,20285,20361,20433,20491,20562,20635,20690,21146,21191,21261,21488,21545,21610,21677,22002,22309,22373,22450,22514,22557,22821,22876,22934,22992,23083,23175,23418,23478,24112,24171,24313,24376,24642,24704,24835,25259,25685,25754,31627,31676,31749,31874,32268,33525,33581,33642,33701,33762,33833,33892,34985", "endColumns": "59,67,60,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,69,82,69,75,69,82,64,74,72,68,68,77,59,66,91,84,62,60,326,316,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,68,76,48,72,44,49,55,55,60,58,60,70,58,44,61", "endOffsets": "12994,13062,13123,13184,13253,13307,13367,13601,13666,13732,13800,14015,14170,14243,14376,14702,14964,15916,16128,16191,16267,16400,16470,16553,16680,16948,17018,17101,17166,17241,17314,17383,17452,17530,17590,17730,17958,18043,18588,18649,18976,19996,20061,20145,20225,20280,20356,20428,20486,20557,20630,20685,20755,21186,21256,21338,21540,21605,21672,21739,22041,22368,22445,22509,22552,22595,22871,22929,22987,23078,23170,23247,23473,23536,24166,24241,24371,24431,24699,24770,24888,25328,25749,25826,31671,31744,31789,31919,32319,33576,33637,33696,33757,33828,33887,33932,35042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,905,967,1045,1104,1162,1240,1301,1358,1414,1473,1531,1585,1671,1727,1785,1839,1904,1997,2071,2143,2223,2297,2375,2495,2558,2621,2720,2797,2871,2921,2972,3038,3102,3170,3241,3313,3374,3445,3512,3572,3660,3740,3803,3886,3971,4045,4110,4186,4234,4308,4372,4448,4526,4588,4652,4715,4781,4861,4941,5017,5098,5152,5207,5276,5351,5424", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "242,305,366,433,502,579,669,776,849,900,962,1040,1099,1157,1235,1296,1353,1409,1468,1526,1580,1666,1722,1780,1834,1899,1992,2066,2138,2218,2292,2370,2490,2553,2616,2715,2792,2866,2916,2967,3033,3097,3165,3236,3308,3369,3440,3507,3567,3655,3735,3798,3881,3966,4040,4105,4181,4229,4303,4367,4443,4521,4583,4647,4710,4776,4856,4936,5012,5093,5147,5202,5271,5346,5419,5489"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2858,2921,2982,3049,3118,3856,3946,4053,6583,6634,6767,7192,7402,7460,7538,7599,7656,7712,7771,7829,7883,7969,8025,8083,8137,8202,8295,8369,8441,8521,8595,8673,8793,8856,8919,9018,9095,9169,9219,9270,9336,9400,9468,9539,9611,9672,9743,9810,9870,9958,10038,10101,10184,10269,10343,10408,10484,10532,10606,10670,10746,10824,10886,10950,11013,11079,11159,11239,11315,11396,11450,11783,12079,12154,12294", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "292,2916,2977,3044,3113,3190,3941,4048,4121,6629,6691,6840,7246,7455,7533,7594,7651,7707,7766,7824,7878,7964,8020,8078,8132,8197,8290,8364,8436,8516,8590,8668,8788,8851,8914,9013,9090,9164,9214,9265,9331,9395,9463,9534,9606,9667,9738,9805,9865,9953,10033,10096,10179,10264,10338,10403,10479,10527,10601,10665,10741,10819,10881,10945,11008,11074,11154,11234,11310,11391,11445,11500,11847,12149,12222,12359"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,279,356,468,512,570,645,709,827,882,954,1010,1063,1127,1192,1272,1416,1476,1536,1587,1638,1704,1788,1865,1920,1991,2058,2125,2185,2260,2413,2479,2551,2605,2663,2721,2779,2840,2906,2996,3073,3150,3240,3324,3394,3473,3558,3659,3768,3860,3954,4003,4239,4314,4371", "endColumns": "88,134,76,111,43,57,74,63,117,54,71,55,52,63,64,79,143,59,59,50,50,65,83,76,54,70,66,66,59,74,152,65,71,53,57,57,57,60,65,89,76,76,89,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "139,274,351,463,507,565,640,704,822,877,949,1005,1058,1122,1187,1267,1411,1471,1531,1582,1633,1699,1783,1860,1915,1986,2053,2120,2180,2255,2408,2474,2546,2600,2658,2716,2774,2835,2901,2991,3068,3145,3235,3319,3389,3468,3553,3654,3763,3855,3949,3998,4234,4309,4366,4421"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,313,314,315,316,317,323,326,331,333,334,335,336,339,341,342,346,350,351,353,355,367,392,393,394,395,396,414,421,422,423,424,426,427,428,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18048,18137,18272,18418,18981,19025,19083,19158,19222,19340,19395,20760,20951,21082,21343,21408,21744,22189,22249,22708,23367,23541,23607,23691,23768,23823,24246,24436,24775,24893,24968,25121,25187,25384,25510,25568,25831,26224,26285,26436,26608,27859,29746,29836,29920,29990,30069,31463,31924,32033,32125,32219,32324,32560,32635,33937", "endColumns": "88,134,76,111,43,57,74,63,117,54,71,55,52,63,64,79,143,59,59,50,50,65,83,76,54,70,66,66,59,74,152,65,71,53,57,57,57,60,65,89,76,76,89,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "18132,18267,18344,18525,19020,19078,19153,19217,19335,19390,19462,20811,20999,21141,21403,21483,21883,22244,22304,22754,23413,23602,23686,23763,23818,23889,24308,24498,24830,24963,25116,25182,25254,25433,25563,25621,25884,26280,26346,26521,26680,27931,29831,29915,29985,30064,30149,31559,32028,32120,32214,32263,32555,32630,32687,33987"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1301,1397,1492,1586,1682,1774,1866,1958,2036,2132,2227,2322,2419,2515,2613,2764,12000", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1296,1392,1487,1581,1677,1769,1861,1953,2031,2127,2222,2317,2414,2510,2608,2759,2853,12074"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,130,214,279,345,405,467", "endColumns": "74,83,64,65,59,61,61", "endOffsets": "125,209,274,340,400,462,524"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12465,12540,12624,12689,12755,12815,12877", "endColumns": "74,83,64,65,59,61,61", "endOffsets": "12535,12619,12684,12750,12810,12872,12934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6174,6845,6937,7038", "endColumns": "82,91,100,92", "endOffsets": "6252,6932,7033,7126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4348,4449,4577,4692,4794,4901,5017,5119,5320,5430,5531,5660,5775,5882,5990,6045,6102", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "4444,4572,4687,4789,4896,5012,5114,5207,5425,5526,5655,5770,5877,5985,6040,6097,6169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,186,250,323,388,449,512,573,633,701,765,826,884,950,1012,1077,1135,1202,1261,1331,1404,1469,1540,1612,1675,1720,1766,1828,1890,1943,2005,2077,2144,2214", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,69,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "110,181,245,318,383,444,507,568,628,696,760,821,879,945,1007,1072,1130,1197,1256,1326,1399,1464,1535,1607,1670,1715,1761,1823,1885,1938,2000,2072,2139,2209,2278"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,318,319,327,328", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13805,14020,14248,14381,14454,14519,14969,15032,15155,15215,15283,15347,15408,15466,15532,15651,15716,15921,16685,16744,17595,17735,17800,20816,20888,21888,22600,22646,22759,23252,23305,23894,23966,24503,24573", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,69,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "13860,14086,14307,14449,14514,14575,15027,15088,15210,15278,15342,15403,15461,15527,15589,15711,15769,15983,16739,16809,17663,17795,17866,20883,20946,21928,22641,22703,22816,23300,23362,23961,24028,24568,24637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5212", "endColumns": "107", "endOffsets": "5315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,183,243,316,374,436,493,560,626,686,743", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "121,178,238,311,369,431,488,555,621,681,738,801"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13865,14580,14707,14767,14840,15093,15594,15774,15988,16272,16558,16814", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "13931,14632,14762,14835,14893,15150,15646,15836,16049,16327,16610,16872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,76", "endOffsets": "131,208"}, "to": {"startLines": "480,481", "startColumns": "4,4", "startOffsets": "36462,36543", "endColumns": "80,76", "endOffsets": "36538,36615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,878,941,1014,1089,1156,1231,1296", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,873,936,1009,1084,1151,1226,1291,1407"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,477,478,479", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4126,4202,6257,6345,6505,7251,7325,11505,11583,11657,11720,11852,11925,12227,36206,36281,36346", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "4197,4271,6340,6431,6578,7320,7397,11578,11652,11715,11778,11920,11995,12289,36276,36341,36457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,180,241,310,381,454,527,605,674,745,817,896,947,1019,1078,1196,1318,1413,1498,1580,1664,1750,1854,1942,2028,2147,2232,2331,2499,2666,2754,2844,2946,3025,3082,3141,3216,3294,3371,3437,3515,3592,3677,3798,3861,3923,3984,4074,4144,4217,4276,4345,4413,4476,4564,4645,4736,4817,4882,4953,5017,5088,5144,5227,5303,5387,5486,5544,5621,5705,5806,5873,5936,6016,6066,6117,6175,6244,6362,6527,6710,6789,6849,6914,6973,7054,7139,7212,7280,7345,7414,7477,7552,7630,7714,7779,7842,7892,8005,8070,8123,8191,8253,8331,8397,8482,8568,8646,8717,8784,8847,8907", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,74,77,76,65,77,76,84,120,62,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,98,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,80,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "109,175,236,305,376,449,522,600,669,740,812,891,942,1014,1073,1191,1313,1408,1493,1575,1659,1745,1849,1937,2023,2142,2227,2326,2494,2661,2749,2839,2941,3020,3077,3136,3211,3289,3366,3432,3510,3587,3672,3793,3856,3918,3979,4069,4139,4212,4271,4340,4408,4471,4559,4640,4731,4812,4877,4948,5012,5083,5139,5222,5298,5382,5481,5539,5616,5700,5801,5868,5931,6011,6061,6112,6170,6239,6357,6522,6705,6784,6844,6909,6968,7049,7134,7207,7275,7340,7409,7472,7547,7625,7709,7774,7837,7887,8000,8065,8118,8186,8248,8326,8392,8477,8563,8641,8712,8779,8842,8902,8996"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,320,338,340,343,347,348,349,352,354,356,357,358,359,360,361,362,363,364,365,366,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,415,419,429,430,431,432,433,434,435,436,437,446,447,448,449,450,451,452,453,454,455,456,457,458,459,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13372,13431,13497,18349,19467,19538,19611,21004,21933,22046,22117,24033,25333,25438,25626,25889,26007,26129,26351,26526,26685,26769,26855,26959,27047,27133,27252,27337,27436,27604,27771,27936,28026,28128,28207,28264,28323,28398,28476,28553,28619,28697,28774,28859,28980,29043,29105,29166,29256,29326,29399,29458,29527,29595,29658,30154,30235,30326,30407,30472,30543,30607,30678,30734,30817,30893,30977,31076,31134,31211,31295,31396,31564,31794,32692,32742,32793,32851,32920,33038,33203,33386,33465,33992,34057,34116,34197,34282,34355,34423,34488,34557,34620,34695,34773,34857,34922,35047,35097,35210,35275,35328,35396,35458,35536,35602,35687,35773,35851,35922,35989,36052,36112", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,74,77,76,65,77,76,84,120,62,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,98,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,80,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "13426,13492,13553,18413,19533,19606,19679,21077,21997,22112,22184,24107,25379,25505,25680,26002,26124,26219,26431,26603,26764,26850,26954,27042,27128,27247,27332,27431,27599,27766,27854,28021,28123,28202,28259,28318,28393,28471,28548,28614,28692,28769,28854,28975,29038,29100,29161,29251,29321,29394,29453,29522,29590,29653,29741,30230,30321,30402,30467,30538,30602,30673,30729,30812,30888,30972,31071,31129,31206,31290,31391,31458,31622,31869,32737,32788,32846,32915,33033,33198,33381,33460,33520,34052,34111,34192,34277,34350,34418,34483,34552,34615,34690,34768,34852,34917,34980,35092,35205,35270,35323,35391,35453,35531,35597,35682,35768,35846,35917,35984,36047,36107,36201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,196", "endColumns": "71,68,70", "endOffsets": "122,191,262"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4276,6436,6696", "endColumns": "71,68,70", "endOffsets": "4343,6500,6762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,67", "endOffsets": "262,330"}, "to": {"startLines": "81,482", "startColumns": "4,4", "startOffsets": "7131,36620", "endColumns": "60,71", "endOffsets": "7187,36687"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3195,3287,3386,3480,3574,3667,3760,12364", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3282,3381,3475,3569,3662,3755,3851,12460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,183,244,305,374,428,488,536,601,667,735,819,903,976,1045,1115,1186,1266,1345,1408,1484,1557,1627,1710,1780,1856,1926,2009,2074,2149,2222,2291,2360,2438,2498,2565,2657,2742,2805,2866,3193,3510,3575,3659,3739,3794,3870,3942,4000,4071,4144,4199,4269,4314,4384,4466,4523,4588,4655,4722,4766,4830,4907,4971,5014,5057,5112,5170,5228,5319,5411,5488,5548,5611,5670,5745,5808,5868,5930,6001,6059,6133,6202,6279,6328,6401,6446,6496,6552,6608,6669,6728,6789,6860,6919,6964", "endColumns": "59,67,60,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,69,82,69,75,69,82,64,74,72,68,68,77,59,66,91,84,62,60,326,316,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,68,76,48,72,44,49,55,55,60,58,60,70,58,44,61", "endOffsets": "110,178,239,300,369,423,483,531,596,662,730,814,898,971,1040,1110,1181,1261,1340,1403,1479,1552,1622,1705,1775,1851,1921,2004,2069,2144,2217,2286,2355,2433,2493,2560,2652,2737,2800,2861,3188,3505,3570,3654,3734,3789,3865,3937,3995,4066,4139,4194,4264,4309,4379,4461,4518,4583,4650,4717,4761,4825,4902,4966,5009,5052,5107,5165,5223,5314,5406,5483,5543,5606,5665,5740,5803,5863,5925,5996,6054,6128,6197,6274,6323,6396,6441,6491,6547,6603,6664,6723,6784,6855,6914,6959,7021"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,321,322,324,325,329,330,332,337,344,345,416,417,418,420,425,438,439,440,441,442,443,444,460", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12939,12999,13067,13128,13189,13258,13312,13558,13606,13671,13737,13936,14091,14175,14312,14637,14898,15841,16054,16133,16196,16332,16405,16475,16615,16877,16953,17023,17106,17171,17246,17319,17388,17457,17535,17668,17871,17963,18530,18593,18654,19684,20001,20066,20150,20230,20285,20361,20433,20491,20562,20635,20690,21146,21191,21261,21488,21545,21610,21677,22002,22309,22373,22450,22514,22557,22821,22876,22934,22992,23083,23175,23418,23478,24112,24171,24313,24376,24642,24704,24835,25259,25685,25754,31627,31676,31749,31874,32268,33525,33581,33642,33701,33762,33833,33892,34985", "endColumns": "59,67,60,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,69,82,69,75,69,82,64,74,72,68,68,77,59,66,91,84,62,60,326,316,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,68,76,48,72,44,49,55,55,60,58,60,70,58,44,61", "endOffsets": "12994,13062,13123,13184,13253,13307,13367,13601,13666,13732,13800,14015,14170,14243,14376,14702,14964,15916,16128,16191,16267,16400,16470,16553,16680,16948,17018,17101,17166,17241,17314,17383,17452,17530,17590,17730,17958,18043,18588,18649,18976,19996,20061,20145,20225,20280,20356,20428,20486,20557,20630,20685,20755,21186,21256,21338,21540,21605,21672,21739,22041,22368,22445,22509,22552,22595,22871,22929,22987,23078,23170,23247,23473,23536,24166,24241,24371,24431,24699,24770,24888,25328,25749,25826,31671,31744,31789,31919,32319,33576,33637,33696,33757,33828,33887,33932,35042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,905,967,1045,1104,1162,1240,1301,1358,1414,1473,1531,1585,1671,1727,1785,1839,1904,1997,2071,2143,2223,2297,2375,2495,2558,2621,2720,2797,2871,2921,2972,3038,3102,3170,3241,3313,3374,3445,3512,3572,3660,3740,3803,3886,3971,4045,4110,4186,4234,4308,4372,4448,4526,4588,4652,4715,4781,4861,4941,5017,5098,5152,5207,5276,5351,5424", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "242,305,366,433,502,579,669,776,849,900,962,1040,1099,1157,1235,1296,1353,1409,1468,1526,1580,1666,1722,1780,1834,1899,1992,2066,2138,2218,2292,2370,2490,2553,2616,2715,2792,2866,2916,2967,3033,3097,3165,3236,3308,3369,3440,3507,3567,3655,3735,3798,3881,3966,4040,4105,4181,4229,4303,4367,4443,4521,4583,4647,4710,4776,4856,4936,5012,5093,5147,5202,5271,5346,5419,5489"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2858,2921,2982,3049,3118,3856,3946,4053,6583,6634,6767,7192,7402,7460,7538,7599,7656,7712,7771,7829,7883,7969,8025,8083,8137,8202,8295,8369,8441,8521,8595,8673,8793,8856,8919,9018,9095,9169,9219,9270,9336,9400,9468,9539,9611,9672,9743,9810,9870,9958,10038,10101,10184,10269,10343,10408,10484,10532,10606,10670,10746,10824,10886,10950,11013,11079,11159,11239,11315,11396,11450,11783,12079,12154,12294", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "292,2916,2977,3044,3113,3190,3941,4048,4121,6629,6691,6840,7246,7455,7533,7594,7651,7707,7766,7824,7878,7964,8020,8078,8132,8197,8290,8364,8436,8516,8590,8668,8788,8851,8914,9013,9090,9164,9214,9265,9331,9395,9463,9534,9606,9667,9738,9805,9865,9953,10033,10096,10179,10264,10338,10403,10479,10527,10601,10665,10741,10819,10881,10945,11008,11074,11154,11234,11310,11391,11445,11500,11847,12149,12222,12359"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,279,356,468,512,570,645,709,827,882,954,1010,1063,1127,1192,1272,1416,1476,1536,1587,1638,1704,1788,1865,1920,1991,2058,2125,2185,2260,2413,2479,2551,2605,2663,2721,2779,2840,2906,2996,3073,3150,3240,3324,3394,3473,3558,3659,3768,3860,3954,4003,4239,4314,4371", "endColumns": "88,134,76,111,43,57,74,63,117,54,71,55,52,63,64,79,143,59,59,50,50,65,83,76,54,70,66,66,59,74,152,65,71,53,57,57,57,60,65,89,76,76,89,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "139,274,351,463,507,565,640,704,822,877,949,1005,1058,1122,1187,1267,1411,1471,1531,1582,1633,1699,1783,1860,1915,1986,2053,2120,2180,2255,2408,2474,2546,2600,2658,2716,2774,2835,2901,2991,3068,3145,3235,3319,3389,3468,3553,3654,3763,3855,3949,3998,4234,4309,4366,4421"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,313,314,315,316,317,323,326,331,333,334,335,336,339,341,342,346,350,351,353,355,367,392,393,394,395,396,414,421,422,423,424,426,427,428,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18048,18137,18272,18418,18981,19025,19083,19158,19222,19340,19395,20760,20951,21082,21343,21408,21744,22189,22249,22708,23367,23541,23607,23691,23768,23823,24246,24436,24775,24893,24968,25121,25187,25384,25510,25568,25831,26224,26285,26436,26608,27859,29746,29836,29920,29990,30069,31463,31924,32033,32125,32219,32324,32560,32635,33937", "endColumns": "88,134,76,111,43,57,74,63,117,54,71,55,52,63,64,79,143,59,59,50,50,65,83,76,54,70,66,66,59,74,152,65,71,53,57,57,57,60,65,89,76,76,89,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "18132,18267,18344,18525,19020,19078,19153,19217,19335,19390,19462,20811,20999,21141,21403,21483,21883,22244,22304,22754,23413,23602,23686,23763,23818,23889,24308,24498,24830,24963,25116,25182,25254,25433,25563,25621,25884,26280,26346,26521,26680,27931,29831,29915,29985,30064,30149,31559,32028,32120,32214,32263,32555,32630,32687,33987"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1301,1397,1492,1586,1682,1774,1866,1958,2036,2132,2227,2322,2419,2515,2613,2764,12000", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1296,1392,1487,1581,1677,1769,1861,1953,2031,2127,2222,2317,2414,2510,2608,2759,2853,12074"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,130,214,279,345,405,467", "endColumns": "74,83,64,65,59,61,61", "endOffsets": "125,209,274,340,400,462,524"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12465,12540,12624,12689,12755,12815,12877", "endColumns": "74,83,64,65,59,61,61", "endOffsets": "12535,12619,12684,12750,12810,12872,12934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6174,6845,6937,7038", "endColumns": "82,91,100,92", "endOffsets": "6252,6932,7033,7126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4348,4449,4577,4692,4794,4901,5017,5119,5320,5430,5531,5660,5775,5882,5990,6045,6102", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "4444,4572,4687,4789,4896,5012,5114,5207,5425,5526,5655,5770,5877,5985,6040,6097,6169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,186,250,323,388,449,512,573,633,701,765,826,884,950,1012,1077,1135,1202,1261,1331,1404,1469,1540,1612,1675,1720,1766,1828,1890,1943,2005,2077,2144,2214", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,69,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "110,181,245,318,383,444,507,568,628,696,760,821,879,945,1007,1072,1130,1197,1256,1326,1399,1464,1535,1607,1670,1715,1761,1823,1885,1938,2000,2072,2139,2209,2278"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,318,319,327,328", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13805,14020,14248,14381,14454,14519,14969,15032,15155,15215,15283,15347,15408,15466,15532,15651,15716,15921,16685,16744,17595,17735,17800,20816,20888,21888,22600,22646,22759,23252,23305,23894,23966,24503,24573", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,69,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "13860,14086,14307,14449,14514,14575,15027,15088,15210,15278,15342,15403,15461,15527,15589,15711,15769,15983,16739,16809,17663,17795,17866,20883,20946,21928,22641,22703,22816,23300,23362,23961,24028,24568,24637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5212", "endColumns": "107", "endOffsets": "5315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,183,243,316,374,436,493,560,626,686,743", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "121,178,238,311,369,431,488,555,621,681,738,801"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13865,14580,14707,14767,14840,15093,15594,15774,15988,16272,16558,16814", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "13931,14632,14762,14835,14893,15150,15646,15836,16049,16327,16610,16872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,76", "endOffsets": "131,208"}, "to": {"startLines": "480,481", "startColumns": "4,4", "startOffsets": "36462,36543", "endColumns": "80,76", "endOffsets": "36538,36615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,878,941,1014,1089,1156,1231,1296", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,873,936,1009,1084,1151,1226,1291,1407"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,477,478,479", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4126,4202,6257,6345,6505,7251,7325,11505,11583,11657,11720,11852,11925,12227,36206,36281,36346", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "4197,4271,6340,6431,6578,7320,7397,11578,11652,11715,11778,11920,11995,12289,36276,36341,36457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,180,241,310,381,454,527,605,674,745,817,896,947,1019,1078,1196,1318,1413,1498,1580,1664,1750,1854,1942,2028,2147,2232,2331,2499,2666,2754,2844,2946,3025,3082,3141,3216,3294,3371,3437,3515,3592,3677,3798,3861,3923,3984,4074,4144,4217,4276,4345,4413,4476,4564,4645,4736,4817,4882,4953,5017,5088,5144,5227,5303,5387,5486,5544,5621,5705,5806,5873,5936,6016,6066,6117,6175,6244,6362,6527,6710,6789,6849,6914,6973,7054,7139,7212,7280,7345,7414,7477,7552,7630,7714,7779,7842,7892,8005,8070,8123,8191,8253,8331,8397,8482,8568,8646,8717,8784,8847,8907", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,74,77,76,65,77,76,84,120,62,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,98,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,80,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "109,175,236,305,376,449,522,600,669,740,812,891,942,1014,1073,1191,1313,1408,1493,1575,1659,1745,1849,1937,2023,2142,2227,2326,2494,2661,2749,2839,2941,3020,3077,3136,3211,3289,3366,3432,3510,3587,3672,3793,3856,3918,3979,4069,4139,4212,4271,4340,4408,4471,4559,4640,4731,4812,4877,4948,5012,5083,5139,5222,5298,5382,5481,5539,5616,5700,5801,5868,5931,6011,6061,6112,6170,6239,6357,6522,6705,6784,6844,6909,6968,7049,7134,7207,7275,7340,7409,7472,7547,7625,7709,7774,7837,7887,8000,8065,8118,8186,8248,8326,8392,8477,8563,8641,8712,8779,8842,8902,8996"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,320,338,340,343,347,348,349,352,354,356,357,358,359,360,361,362,363,364,365,366,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,415,419,429,430,431,432,433,434,435,436,437,446,447,448,449,450,451,452,453,454,455,456,457,458,459,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13372,13431,13497,18349,19467,19538,19611,21004,21933,22046,22117,24033,25333,25438,25626,25889,26007,26129,26351,26526,26685,26769,26855,26959,27047,27133,27252,27337,27436,27604,27771,27936,28026,28128,28207,28264,28323,28398,28476,28553,28619,28697,28774,28859,28980,29043,29105,29166,29256,29326,29399,29458,29527,29595,29658,30154,30235,30326,30407,30472,30543,30607,30678,30734,30817,30893,30977,31076,31134,31211,31295,31396,31564,31794,32692,32742,32793,32851,32920,33038,33203,33386,33465,33992,34057,34116,34197,34282,34355,34423,34488,34557,34620,34695,34773,34857,34922,35047,35097,35210,35275,35328,35396,35458,35536,35602,35687,35773,35851,35922,35989,36052,36112", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,74,77,76,65,77,76,84,120,62,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,98,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,80,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "13426,13492,13553,18413,19533,19606,19679,21077,21997,22112,22184,24107,25379,25505,25680,26002,26124,26219,26431,26603,26764,26850,26954,27042,27128,27247,27332,27431,27599,27766,27854,28021,28123,28202,28259,28318,28393,28471,28548,28614,28692,28769,28854,28975,29038,29100,29161,29251,29321,29394,29453,29522,29590,29653,29741,30230,30321,30402,30467,30538,30602,30673,30729,30812,30888,30972,31071,31129,31206,31290,31391,31458,31622,31869,32737,32788,32846,32915,33033,33198,33381,33460,33520,34052,34111,34192,34277,34350,34418,34483,34552,34615,34690,34768,34852,34917,34980,35092,35205,35270,35323,35391,35453,35531,35597,35682,35768,35846,35917,35984,36047,36107,36201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,196", "endColumns": "71,68,70", "endOffsets": "122,191,262"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4276,6436,6696", "endColumns": "71,68,70", "endOffsets": "4343,6500,6762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,67", "endOffsets": "262,330"}, "to": {"startLines": "81,482", "startColumns": "4,4", "startOffsets": "7131,36620", "endColumns": "60,71", "endOffsets": "7187,36687"}}]}]}
{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,214", "endColumns": "79,78,78", "endOffsets": "130,209,288"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4593,7147,7442", "endColumns": "79,78,78", "endOffsets": "4668,7221,7516"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6844,7614,7715,7824", "endColumns": "102,100,108,98", "endOffsets": "6942,7710,7819,7918"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,75", "endOffsets": "258,334"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "7923,44661", "endColumns": "60,79", "endOffsets": "7979,44736"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,195,265,357,425,486,553,622,684,754,829,889,951,1026,1090,1163,1227,1307,1375,1464,1539,1617,1701,1804,1888,1937,1989,2069,2131,2200,2272,2371,2458,2551", "endColumns": "63,75,69,91,67,60,66,68,61,69,74,59,61,74,63,72,63,79,67,88,74,77,83,102,83,48,51,79,61,68,71,98,86,92,89", "endOffsets": "114,190,260,352,420,481,548,617,679,749,824,884,946,1021,1085,1158,1222,1302,1370,1459,1534,1612,1696,1799,1883,1932,1984,2064,2126,2195,2267,2366,2453,2546,2636"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15431,15670,15933,16086,16178,16246,16750,16817,16955,17017,17087,17162,17222,17284,17359,17482,17555,17794,18716,18784,19798,19950,20028,24208,24311,25593,26434,26486,26621,27303,27372,28182,28281,29003,29096", "endColumns": "63,75,69,91,67,60,66,68,61,69,74,59,61,74,63,72,63,79,67,88,74,77,83,102,83,48,51,79,61,68,71,98,86,92,89", "endOffsets": "15490,15741,15998,16173,16241,16302,16812,16881,17012,17082,17157,17217,17279,17354,17418,17550,17614,17869,18779,18868,19868,20023,20107,24306,24390,25637,26481,26561,26678,27367,27439,28276,28363,29091,29181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3392,3488,3590,3688,3793,3898,4010,13810", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3483,3585,3683,3788,3893,4005,4121,13906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5666", "endColumns": "149", "endOffsets": "5811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "27647", "endColumns": "88", "endOffsets": "27731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1022,1087,1180,1255,1320,1408,1473,1539,1597,1668,1734,1788,1898,1958,2022,2076,2149,2265,2349,2425,2516,2597,2678,2811,2896,2981,3114,3204,3278,3330,3381,3447,3524,3606,3677,3751,3825,3904,3981,4053,4160,4249,4325,4416,4511,4585,4658,4752,4806,4880,4952,5038,5124,5186,5250,5313,5384,5485,5588,5683,5783,5839,5894,5973,6059,6138", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "263,339,413,496,585,667,763,871,955,1017,1082,1175,1250,1315,1403,1468,1534,1592,1663,1729,1783,1893,1953,2017,2071,2144,2260,2344,2420,2511,2592,2673,2806,2891,2976,3109,3199,3273,3325,3376,3442,3519,3601,3672,3746,3820,3899,3976,4048,4155,4244,4320,4411,4506,4580,4653,4747,4801,4875,4947,5033,5119,5181,5245,5308,5379,5480,5583,5678,5778,5834,5889,5968,6054,6133,6208"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2988,3064,3138,3221,3310,4126,4222,4330,7315,7377,7521,7984,8229,8294,8382,8447,8513,8571,8642,8708,8762,8872,8932,8996,9050,9123,9239,9323,9399,9490,9571,9652,9785,9870,9955,10088,10178,10252,10304,10355,10421,10498,10580,10651,10725,10799,10878,10955,11027,11134,11223,11299,11390,11485,11559,11632,11726,11780,11854,11926,12012,12098,12160,12224,12287,12358,12459,12562,12657,12757,12813,13175,13498,13584,13735", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "313,3059,3133,3216,3305,3387,4217,4325,4409,7372,7437,7609,8054,8289,8377,8442,8508,8566,8637,8703,8757,8867,8927,8991,9045,9118,9234,9318,9394,9485,9566,9647,9780,9865,9950,10083,10173,10247,10299,10350,10416,10493,10575,10646,10720,10794,10873,10950,11022,11129,11218,11294,11385,11480,11554,11627,11721,11775,11849,11921,12007,12093,12155,12219,12282,12353,12454,12557,12652,12752,12808,12863,13249,13579,13658,13805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,982,1050,1131,1213,1285,1362,1434", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,977,1045,1126,1208,1280,1357,1429,1551"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4414,4508,6947,7046,7226,8059,8136,12868,12959,13041,13107,13254,13335,13663,44210,44287,44359", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "4503,4588,7041,7142,7310,8131,8224,12954,13036,13102,13170,13330,13412,13730,44282,44354,44476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "44481,44571", "endColumns": "89,89", "endOffsets": "44566,44656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,273,367,464,543,621,737,831,920,1013,1148,1221,1330,1401,1566,1759,1906,1994,2085,2180,2275,2467,2567,2664,2896,2989,3112,3302,3511,3605,3715,3888,3976,4036,4101,4179,4277,4384,4462,4554,4652,4751,4956,5025,5094,5164,5279,5361,5445,5510,5590,5661,5736,5860,5949,6057,6149,6223,6304,6372,6453,6515,6613,6726,6831,6962,7028,7116,7209,7336,7410,7488,7588,7647,7711,7805,7920,8074,8322,8591,8683,8758,8846,8914,9017,9130,9216,9295,9369,9459,9535,9626,9742,9869,9934,10012,10069,10211,10277,10334,10412,10480,10558,10639,10770,10890,10997,11084,11164,11249,11318", "endColumns": "68,78,69,93,96,78,77,115,93,88,92,134,72,108,70,164,192,146,87,90,94,94,191,99,96,231,92,122,189,208,93,109,172,87,59,64,77,97,106,77,91,97,98,204,68,68,69,114,81,83,64,79,70,74,123,88,107,91,73,80,67,80,61,97,112,104,130,65,87,92,126,73,77,99,58,63,93,114,153,247,268,91,74,87,67,102,112,85,78,73,89,75,90,115,126,64,77,56,141,65,56,77,67,77,80,130,119,106,86,79,84,68,146", "endOffsets": "119,198,268,362,459,538,616,732,826,915,1008,1143,1216,1325,1396,1561,1754,1901,1989,2080,2175,2270,2462,2562,2659,2891,2984,3107,3297,3506,3600,3710,3883,3971,4031,4096,4174,4272,4379,4457,4549,4647,4746,4951,5020,5089,5159,5274,5356,5440,5505,5585,5656,5731,5855,5944,6052,6144,6218,6299,6367,6448,6510,6608,6721,6826,6957,7023,7111,7204,7331,7405,7483,7583,7642,7706,7800,7915,8069,8317,8586,8678,8753,8841,8909,9012,9125,9211,9290,9364,9454,9530,9621,9737,9864,9929,10007,10064,10206,10272,10329,10407,10475,10553,10634,10765,10885,10992,11079,11159,11244,11313,11460"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14936,15005,15084,20689,22390,22487,22566,24451,25642,25783,25872,28368,30131,30272,30503,30810,30975,31168,31464,31650,31843,31938,32033,32225,32325,32422,32654,32747,32870,33060,33269,33458,33568,33741,33829,33889,33954,34032,34130,34237,34315,34407,34505,34604,34809,34878,34947,35017,35132,35214,35298,35363,35443,35514,35589,36145,36234,36342,36434,36508,36589,36657,36738,36800,36898,37011,37116,37247,37313,37401,37494,37621,37879,38195,39730,39789,39853,39947,40062,40216,40464,40733,40825,41433,41521,41589,41692,41805,41891,41970,42044,42134,42210,42301,42417,42544,42609,42757,42814,42956,43022,43079,43157,43225,43303,43384,43515,43635,43742,43829,43909,43994,44063", "endColumns": "68,78,69,93,96,78,77,115,93,88,92,134,72,108,70,164,192,146,87,90,94,94,191,99,96,231,92,122,189,208,93,109,172,87,59,64,77,97,106,77,91,97,98,204,68,68,69,114,81,83,64,79,70,74,123,88,107,91,73,80,67,80,61,97,112,104,130,65,87,92,126,73,77,99,58,63,93,114,153,247,268,91,74,87,67,102,112,85,78,73,89,75,90,115,126,64,77,56,141,65,56,77,67,77,80,130,119,106,86,79,84,68,146", "endOffsets": "15000,15079,15149,20778,22482,22561,22639,24562,25731,25867,25960,28498,30199,30376,30569,30970,31163,31310,31547,31736,31933,32028,32220,32320,32417,32649,32742,32865,33055,33264,33358,33563,33736,33824,33884,33949,34027,34125,34232,34310,34402,34500,34599,34804,34873,34942,35012,35127,35209,35293,35358,35438,35509,35584,35708,36229,36337,36429,36503,36584,36652,36733,36795,36893,37006,37111,37242,37308,37396,37489,37616,37690,37952,38290,39784,39848,39942,40057,40211,40459,40728,40820,40895,41516,41584,41687,41800,41886,41965,42039,42129,42205,42296,42412,42539,42604,42682,42809,42951,43017,43074,43152,43220,43298,43379,43510,43630,43737,43824,43904,43989,44058,44205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,139,239,305,375,440,515", "endColumns": "83,99,65,69,64,74,68", "endOffsets": "134,234,300,370,435,510,579"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "13911,13995,14095,14161,14231,14296,14371", "endColumns": "83,99,65,69,64,74,68", "endOffsets": "13990,14090,14156,14226,14291,14366,14435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,13417", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,13493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4673,4784,4937,5068,5174,5317,5443,5559,5816,5957,6063,6212,6338,6486,6625,6691,6761", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "4779,4932,5063,5169,5312,5438,5554,5661,5952,6058,6207,6333,6481,6620,6686,6756,6839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,206,268,341,423,484,551,603,681,754,828,927,1026,1114,1197,1286,1371,1470,1566,1636,1729,1819,1907,2018,2106,2199,2276,2376,2449,2539,2630,2714,2793,2893,2961,3038,3148,3245,3312,3377,3974,4543,4616,4727,4822,4877,4977,5073,5143,5242,5336,5393,5472,5522,5600,5705,5778,5857,5924,5990,6037,6111,6199,6280,6327,6375,6447,6505,6572,6728,6879,6995,7068,7143,7223,7315,7395,7477,7553,7642,7719,7838,7922,8012,8067,8201,8250,8306,8375,8440,8509,8574,8640,8729,8799,8849", "endColumns": "70,79,61,72,81,60,66,51,77,72,73,98,98,87,82,88,84,98,95,69,92,89,87,110,87,92,76,99,72,89,90,83,78,99,67,76,109,96,66,64,596,568,72,110,94,54,99,95,69,98,93,56,78,49,77,104,72,78,66,65,46,73,87,80,46,47,71,57,66,155,150,115,72,74,79,91,79,81,75,88,76,118,83,89,54,133,48,55,68,64,68,64,65,88,69,49,69", "endOffsets": "121,201,263,336,418,479,546,598,676,749,823,922,1021,1109,1192,1281,1366,1465,1561,1631,1724,1814,1902,2013,2101,2194,2271,2371,2444,2534,2625,2709,2788,2888,2956,3033,3143,3240,3307,3372,3969,4538,4611,4722,4817,4872,4972,5068,5138,5237,5331,5388,5467,5517,5595,5700,5773,5852,5919,5985,6032,6106,6194,6275,6322,6370,6442,6500,6567,6723,6874,6990,7063,7138,7218,7310,7390,7472,7548,7637,7714,7833,7917,8007,8062,8196,8245,8301,8370,8435,8504,8569,8635,8724,8794,8844,8914"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14440,14511,14591,14653,14726,14808,14869,15154,15206,15284,15357,15571,15746,15845,16003,16373,16665,17695,17947,18043,18113,18274,18364,18452,18628,18943,19036,19113,19213,19286,19376,19467,19551,19630,19730,19873,20112,20222,20969,21036,21101,22644,23213,23286,23397,23492,23547,23647,23743,23813,23912,24006,24063,24631,24681,24759,25030,25103,25182,25249,25736,26096,26170,26258,26339,26386,26683,26755,26813,26880,27036,27187,27499,27572,28503,28583,28750,28830,29186,29262,29425,30012,30574,30658,37957,38012,38146,38295,38855,40900,40965,41034,41099,41165,41254,41324,42687", "endColumns": "70,79,61,72,81,60,66,51,77,72,73,98,98,87,82,88,84,98,95,69,92,89,87,110,87,92,76,99,72,89,90,83,78,99,67,76,109,96,66,64,596,568,72,110,94,54,99,95,69,98,93,56,78,49,77,104,72,78,66,65,46,73,87,80,46,47,71,57,66,155,150,115,72,74,79,91,79,81,75,88,76,118,83,89,54,133,48,55,68,64,68,64,65,88,69,49,69", "endOffsets": "14506,14586,14648,14721,14803,14864,14931,15201,15279,15352,15426,15665,15840,15928,16081,16457,16745,17789,18038,18108,18201,18359,18447,18558,18711,19031,19108,19208,19281,19371,19462,19546,19625,19725,19793,19945,20217,20314,21031,21096,21693,23208,23281,23392,23487,23542,23642,23738,23808,23907,24001,24058,24137,24676,24754,24859,25098,25177,25244,25310,25778,26165,26253,26334,26381,26429,26750,26808,26875,27031,27182,27298,27567,27642,28578,28670,28825,28907,29257,29346,29497,30126,30653,30743,38007,38141,38190,38346,38919,40960,41029,41094,41160,41249,41319,41369,42752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,197,258,338,400,469,528,604,677,745,810", "endColumns": "75,65,60,79,61,68,58,75,72,67,64,69", "endOffsets": "126,192,253,333,395,464,523,599,672,740,805,875"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15495,16307,16462,16523,16603,16886,17423,17619,17874,18206,18563,18873", "endColumns": "75,65,60,79,61,68,58,75,72,67,64,69", "endOffsets": "15566,16368,16518,16598,16660,16950,17477,17690,17942,18269,18623,18938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,336,425,611,660,726,823,893,1134,1204,1303,1369,1425,1489,1564,1655,1933,2001,2064,2119,2174,2248,2366,2474,2533,2620,2695,2786,2860,2960,3214,3293,3370,3438,3498,3560,3622,3692,3771,3869,3971,4066,4166,4259,4334,4413,4498,4682,4877,4993,5126,5186,5829,5930,5992", "endColumns": "120,159,88,185,48,65,96,69,240,69,98,65,55,63,74,90,277,67,62,54,54,73,117,107,58,86,74,90,73,99,253,78,76,67,59,61,61,69,78,97,101,94,99,92,74,78,84,183,194,115,132,59,642,100,61,58", "endOffsets": "171,331,420,606,655,721,818,888,1129,1199,1298,1364,1420,1484,1559,1650,1928,1996,2059,2114,2169,2243,2361,2469,2528,2615,2690,2781,2855,2955,3209,3288,3365,3433,3493,3555,3617,3687,3766,3864,3966,4061,4161,4254,4329,4408,4493,4677,4872,4988,5121,5181,5824,5925,5987,6046"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20319,20440,20600,20783,21698,21747,21813,21910,21980,22221,22291,24142,24395,24567,24864,24939,25315,25965,26033,26566,27444,27736,27810,27928,28036,28095,28675,28912,29351,29502,29602,29856,29935,30204,30381,30441,30748,31315,31385,31552,31741,33363,35713,35813,35906,35981,36060,37695,38351,38546,38662,38795,38924,39567,39668,41374", "endColumns": "120,159,88,185,48,65,96,69,240,69,98,65,55,63,74,90,277,67,62,54,54,73,117,107,58,86,74,90,73,99,253,78,76,67,59,61,61,69,78,97,101,94,99,92,74,78,84,183,194,115,132,59,642,100,61,58", "endOffsets": "20435,20595,20684,20964,21742,21808,21905,21975,22216,22286,22385,24203,24446,24626,24934,25025,25588,26028,26091,26616,27494,27805,27923,28031,28090,28177,28745,28998,29420,29597,29851,29930,30007,30267,30436,30498,30805,31380,31459,31645,31838,33453,35808,35901,35976,36055,36140,37874,38541,38657,38790,38850,39562,39663,39725,41428"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,214", "endColumns": "79,78,78", "endOffsets": "130,209,288"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4593,7147,7442", "endColumns": "79,78,78", "endOffsets": "4668,7221,7516"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6844,7614,7715,7824", "endColumns": "102,100,108,98", "endOffsets": "6942,7710,7819,7918"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,75", "endOffsets": "258,334"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "7923,44661", "endColumns": "60,79", "endOffsets": "7979,44736"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,195,265,357,425,486,553,622,684,754,829,889,951,1026,1090,1163,1227,1307,1375,1464,1539,1617,1701,1804,1888,1937,1989,2069,2131,2200,2272,2371,2458,2551", "endColumns": "63,75,69,91,67,60,66,68,61,69,74,59,61,74,63,72,63,79,67,88,74,77,83,102,83,48,51,79,61,68,71,98,86,92,89", "endOffsets": "114,190,260,352,420,481,548,617,679,749,824,884,946,1021,1085,1158,1222,1302,1370,1459,1534,1612,1696,1799,1883,1932,1984,2064,2126,2195,2267,2366,2453,2546,2636"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15431,15670,15933,16086,16178,16246,16750,16817,16955,17017,17087,17162,17222,17284,17359,17482,17555,17794,18716,18784,19798,19950,20028,24208,24311,25593,26434,26486,26621,27303,27372,28182,28281,29003,29096", "endColumns": "63,75,69,91,67,60,66,68,61,69,74,59,61,74,63,72,63,79,67,88,74,77,83,102,83,48,51,79,61,68,71,98,86,92,89", "endOffsets": "15490,15741,15998,16173,16241,16302,16812,16881,17012,17082,17157,17217,17279,17354,17418,17550,17614,17869,18779,18868,19868,20023,20107,24306,24390,25637,26481,26561,26678,27367,27439,28276,28363,29091,29181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3392,3488,3590,3688,3793,3898,4010,13810", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3483,3585,3683,3788,3893,4005,4121,13906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5666", "endColumns": "149", "endOffsets": "5811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "27647", "endColumns": "88", "endOffsets": "27731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1022,1087,1180,1255,1320,1408,1473,1539,1597,1668,1734,1788,1898,1958,2022,2076,2149,2265,2349,2425,2516,2597,2678,2811,2896,2981,3114,3204,3278,3330,3381,3447,3524,3606,3677,3751,3825,3904,3981,4053,4160,4249,4325,4416,4511,4585,4658,4752,4806,4880,4952,5038,5124,5186,5250,5313,5384,5485,5588,5683,5783,5839,5894,5973,6059,6138", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "263,339,413,496,585,667,763,871,955,1017,1082,1175,1250,1315,1403,1468,1534,1592,1663,1729,1783,1893,1953,2017,2071,2144,2260,2344,2420,2511,2592,2673,2806,2891,2976,3109,3199,3273,3325,3376,3442,3519,3601,3672,3746,3820,3899,3976,4048,4155,4244,4320,4411,4506,4580,4653,4747,4801,4875,4947,5033,5119,5181,5245,5308,5379,5480,5583,5678,5778,5834,5889,5968,6054,6133,6208"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2988,3064,3138,3221,3310,4126,4222,4330,7315,7377,7521,7984,8229,8294,8382,8447,8513,8571,8642,8708,8762,8872,8932,8996,9050,9123,9239,9323,9399,9490,9571,9652,9785,9870,9955,10088,10178,10252,10304,10355,10421,10498,10580,10651,10725,10799,10878,10955,11027,11134,11223,11299,11390,11485,11559,11632,11726,11780,11854,11926,12012,12098,12160,12224,12287,12358,12459,12562,12657,12757,12813,13175,13498,13584,13735", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "313,3059,3133,3216,3305,3387,4217,4325,4409,7372,7437,7609,8054,8289,8377,8442,8508,8566,8637,8703,8757,8867,8927,8991,9045,9118,9234,9318,9394,9485,9566,9647,9780,9865,9950,10083,10173,10247,10299,10350,10416,10493,10575,10646,10720,10794,10873,10950,11022,11129,11218,11294,11385,11480,11554,11627,11721,11775,11849,11921,12007,12093,12155,12219,12282,12353,12454,12557,12652,12752,12808,12863,13249,13579,13658,13805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,982,1050,1131,1213,1285,1362,1434", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,977,1045,1126,1208,1280,1357,1429,1551"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4414,4508,6947,7046,7226,8059,8136,12868,12959,13041,13107,13254,13335,13663,44210,44287,44359", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "4503,4588,7041,7142,7310,8131,8224,12954,13036,13102,13170,13330,13412,13730,44282,44354,44476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "44481,44571", "endColumns": "89,89", "endOffsets": "44566,44656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,273,367,464,543,621,737,831,920,1013,1148,1221,1330,1401,1566,1759,1906,1994,2085,2180,2275,2467,2567,2664,2896,2989,3112,3302,3511,3605,3715,3888,3976,4036,4101,4179,4277,4384,4462,4554,4652,4751,4956,5025,5094,5164,5279,5361,5445,5510,5590,5661,5736,5860,5949,6057,6149,6223,6304,6372,6453,6515,6613,6726,6831,6962,7028,7116,7209,7336,7410,7488,7588,7647,7711,7805,7920,8074,8322,8591,8683,8758,8846,8914,9017,9130,9216,9295,9369,9459,9535,9626,9742,9869,9934,10012,10069,10211,10277,10334,10412,10480,10558,10639,10770,10890,10997,11084,11164,11249,11318", "endColumns": "68,78,69,93,96,78,77,115,93,88,92,134,72,108,70,164,192,146,87,90,94,94,191,99,96,231,92,122,189,208,93,109,172,87,59,64,77,97,106,77,91,97,98,204,68,68,69,114,81,83,64,79,70,74,123,88,107,91,73,80,67,80,61,97,112,104,130,65,87,92,126,73,77,99,58,63,93,114,153,247,268,91,74,87,67,102,112,85,78,73,89,75,90,115,126,64,77,56,141,65,56,77,67,77,80,130,119,106,86,79,84,68,146", "endOffsets": "119,198,268,362,459,538,616,732,826,915,1008,1143,1216,1325,1396,1561,1754,1901,1989,2080,2175,2270,2462,2562,2659,2891,2984,3107,3297,3506,3600,3710,3883,3971,4031,4096,4174,4272,4379,4457,4549,4647,4746,4951,5020,5089,5159,5274,5356,5440,5505,5585,5656,5731,5855,5944,6052,6144,6218,6299,6367,6448,6510,6608,6721,6826,6957,7023,7111,7204,7331,7405,7483,7583,7642,7706,7800,7915,8069,8317,8586,8678,8753,8841,8909,9012,9125,9211,9290,9364,9454,9530,9621,9737,9864,9929,10007,10064,10206,10272,10329,10407,10475,10553,10634,10765,10885,10992,11079,11159,11244,11313,11460"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14936,15005,15084,20689,22390,22487,22566,24451,25642,25783,25872,28368,30131,30272,30503,30810,30975,31168,31464,31650,31843,31938,32033,32225,32325,32422,32654,32747,32870,33060,33269,33458,33568,33741,33829,33889,33954,34032,34130,34237,34315,34407,34505,34604,34809,34878,34947,35017,35132,35214,35298,35363,35443,35514,35589,36145,36234,36342,36434,36508,36589,36657,36738,36800,36898,37011,37116,37247,37313,37401,37494,37621,37879,38195,39730,39789,39853,39947,40062,40216,40464,40733,40825,41433,41521,41589,41692,41805,41891,41970,42044,42134,42210,42301,42417,42544,42609,42757,42814,42956,43022,43079,43157,43225,43303,43384,43515,43635,43742,43829,43909,43994,44063", "endColumns": "68,78,69,93,96,78,77,115,93,88,92,134,72,108,70,164,192,146,87,90,94,94,191,99,96,231,92,122,189,208,93,109,172,87,59,64,77,97,106,77,91,97,98,204,68,68,69,114,81,83,64,79,70,74,123,88,107,91,73,80,67,80,61,97,112,104,130,65,87,92,126,73,77,99,58,63,93,114,153,247,268,91,74,87,67,102,112,85,78,73,89,75,90,115,126,64,77,56,141,65,56,77,67,77,80,130,119,106,86,79,84,68,146", "endOffsets": "15000,15079,15149,20778,22482,22561,22639,24562,25731,25867,25960,28498,30199,30376,30569,30970,31163,31310,31547,31736,31933,32028,32220,32320,32417,32649,32742,32865,33055,33264,33358,33563,33736,33824,33884,33949,34027,34125,34232,34310,34402,34500,34599,34804,34873,34942,35012,35127,35209,35293,35358,35438,35509,35584,35708,36229,36337,36429,36503,36584,36652,36733,36795,36893,37006,37111,37242,37308,37396,37489,37616,37690,37952,38290,39784,39848,39942,40057,40211,40459,40728,40820,40895,41516,41584,41687,41800,41886,41965,42039,42129,42205,42296,42412,42539,42604,42682,42809,42951,43017,43074,43152,43220,43298,43379,43510,43630,43737,43824,43904,43989,44058,44205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,139,239,305,375,440,515", "endColumns": "83,99,65,69,64,74,68", "endOffsets": "134,234,300,370,435,510,579"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "13911,13995,14095,14161,14231,14296,14371", "endColumns": "83,99,65,69,64,74,68", "endOffsets": "13990,14090,14156,14226,14291,14366,14435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,13417", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,13493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4673,4784,4937,5068,5174,5317,5443,5559,5816,5957,6063,6212,6338,6486,6625,6691,6761", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "4779,4932,5063,5169,5312,5438,5554,5661,5952,6058,6207,6333,6481,6620,6686,6756,6839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,206,268,341,423,484,551,603,681,754,828,927,1026,1114,1197,1286,1371,1470,1566,1636,1729,1819,1907,2018,2106,2199,2276,2376,2449,2539,2630,2714,2793,2893,2961,3038,3148,3245,3312,3377,3974,4543,4616,4727,4822,4877,4977,5073,5143,5242,5336,5393,5472,5522,5600,5705,5778,5857,5924,5990,6037,6111,6199,6280,6327,6375,6447,6505,6572,6728,6879,6995,7068,7143,7223,7315,7395,7477,7553,7642,7719,7838,7922,8012,8067,8201,8250,8306,8375,8440,8509,8574,8640,8729,8799,8849", "endColumns": "70,79,61,72,81,60,66,51,77,72,73,98,98,87,82,88,84,98,95,69,92,89,87,110,87,92,76,99,72,89,90,83,78,99,67,76,109,96,66,64,596,568,72,110,94,54,99,95,69,98,93,56,78,49,77,104,72,78,66,65,46,73,87,80,46,47,71,57,66,155,150,115,72,74,79,91,79,81,75,88,76,118,83,89,54,133,48,55,68,64,68,64,65,88,69,49,69", "endOffsets": "121,201,263,336,418,479,546,598,676,749,823,922,1021,1109,1192,1281,1366,1465,1561,1631,1724,1814,1902,2013,2101,2194,2271,2371,2444,2534,2625,2709,2788,2888,2956,3033,3143,3240,3307,3372,3969,4538,4611,4722,4817,4872,4972,5068,5138,5237,5331,5388,5467,5517,5595,5700,5773,5852,5919,5985,6032,6106,6194,6275,6322,6370,6442,6500,6567,6723,6874,6990,7063,7138,7218,7310,7390,7472,7548,7637,7714,7833,7917,8007,8062,8196,8245,8301,8370,8435,8504,8569,8635,8724,8794,8844,8914"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14440,14511,14591,14653,14726,14808,14869,15154,15206,15284,15357,15571,15746,15845,16003,16373,16665,17695,17947,18043,18113,18274,18364,18452,18628,18943,19036,19113,19213,19286,19376,19467,19551,19630,19730,19873,20112,20222,20969,21036,21101,22644,23213,23286,23397,23492,23547,23647,23743,23813,23912,24006,24063,24631,24681,24759,25030,25103,25182,25249,25736,26096,26170,26258,26339,26386,26683,26755,26813,26880,27036,27187,27499,27572,28503,28583,28750,28830,29186,29262,29425,30012,30574,30658,37957,38012,38146,38295,38855,40900,40965,41034,41099,41165,41254,41324,42687", "endColumns": "70,79,61,72,81,60,66,51,77,72,73,98,98,87,82,88,84,98,95,69,92,89,87,110,87,92,76,99,72,89,90,83,78,99,67,76,109,96,66,64,596,568,72,110,94,54,99,95,69,98,93,56,78,49,77,104,72,78,66,65,46,73,87,80,46,47,71,57,66,155,150,115,72,74,79,91,79,81,75,88,76,118,83,89,54,133,48,55,68,64,68,64,65,88,69,49,69", "endOffsets": "14506,14586,14648,14721,14803,14864,14931,15201,15279,15352,15426,15665,15840,15928,16081,16457,16745,17789,18038,18108,18201,18359,18447,18558,18711,19031,19108,19208,19281,19371,19462,19546,19625,19725,19793,19945,20217,20314,21031,21096,21693,23208,23281,23392,23487,23542,23642,23738,23808,23907,24001,24058,24137,24676,24754,24859,25098,25177,25244,25310,25778,26165,26253,26334,26381,26429,26750,26808,26875,27031,27182,27298,27567,27642,28578,28670,28825,28907,29257,29346,29497,30126,30653,30743,38007,38141,38190,38346,38919,40960,41029,41094,41160,41249,41319,41369,42752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,197,258,338,400,469,528,604,677,745,810", "endColumns": "75,65,60,79,61,68,58,75,72,67,64,69", "endOffsets": "126,192,253,333,395,464,523,599,672,740,805,875"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15495,16307,16462,16523,16603,16886,17423,17619,17874,18206,18563,18873", "endColumns": "75,65,60,79,61,68,58,75,72,67,64,69", "endOffsets": "15566,16368,16518,16598,16660,16950,17477,17690,17942,18269,18623,18938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,336,425,611,660,726,823,893,1134,1204,1303,1369,1425,1489,1564,1655,1933,2001,2064,2119,2174,2248,2366,2474,2533,2620,2695,2786,2860,2960,3214,3293,3370,3438,3498,3560,3622,3692,3771,3869,3971,4066,4166,4259,4334,4413,4498,4682,4877,4993,5126,5186,5829,5930,5992", "endColumns": "120,159,88,185,48,65,96,69,240,69,98,65,55,63,74,90,277,67,62,54,54,73,117,107,58,86,74,90,73,99,253,78,76,67,59,61,61,69,78,97,101,94,99,92,74,78,84,183,194,115,132,59,642,100,61,58", "endOffsets": "171,331,420,606,655,721,818,888,1129,1199,1298,1364,1420,1484,1559,1650,1928,1996,2059,2114,2169,2243,2361,2469,2528,2615,2690,2781,2855,2955,3209,3288,3365,3433,3493,3555,3617,3687,3766,3864,3966,4061,4161,4254,4329,4408,4493,4677,4872,4988,5121,5181,5824,5925,5987,6046"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20319,20440,20600,20783,21698,21747,21813,21910,21980,22221,22291,24142,24395,24567,24864,24939,25315,25965,26033,26566,27444,27736,27810,27928,28036,28095,28675,28912,29351,29502,29602,29856,29935,30204,30381,30441,30748,31315,31385,31552,31741,33363,35713,35813,35906,35981,36060,37695,38351,38546,38662,38795,38924,39567,39668,41374", "endColumns": "120,159,88,185,48,65,96,69,240,69,98,65,55,63,74,90,277,67,62,54,54,73,117,107,58,86,74,90,73,99,253,78,76,67,59,61,61,69,78,97,101,94,99,92,74,78,84,183,194,115,132,59,642,100,61,58", "endOffsets": "20435,20595,20684,20964,21742,21808,21905,21975,22216,22286,22385,24203,24446,24626,24934,25025,25588,26028,26091,26616,27494,27805,27923,28031,28090,28177,28745,28998,29420,29597,29851,29930,30007,30267,30436,30498,30805,31380,31459,31645,31838,33453,35808,35901,35976,36055,36140,37874,38541,38657,38790,38850,39562,39663,39725,41428"}}]}]}
{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,266,336,415,473,532,586,661,745,828,934,1040,1126,1210,1291,1379,1479,1577,1650,1745,1836,1928,2042,2126,2215,2295,2397,2472,2566,2661,2752,2838,2937,3010,3089,3201,3306,3376,3439,4110,4760,4834,4940,5040,5095,5185,5269,5337,5431,5525,5583,5666,5715,5793,5903,5972,6044,6111,6177,6226,6306,6402,6478,6525,6571,6643,6701,6761,6936,7095,7219,7288,7370,7450,7537,7618,7706,7784,7879,7961,8067,8154,8250,8306,8442,8491,8550,8617,8683,8756,8825,8896,8984,9056,9107", "endColumns": "69,78,61,69,78,57,58,53,74,83,82,105,105,85,83,80,87,99,97,72,94,90,91,113,83,88,79,101,74,93,94,90,85,98,72,78,111,104,69,62,670,649,73,105,99,54,89,83,67,93,93,57,82,48,77,109,68,71,66,65,48,79,95,75,46,45,71,57,59,174,158,123,68,81,79,86,80,87,77,94,81,105,86,95,55,135,48,58,66,65,72,68,70,87,71,50,79", "endOffsets": "120,199,261,331,410,468,527,581,656,740,823,929,1035,1121,1205,1286,1374,1474,1572,1645,1740,1831,1923,2037,2121,2210,2290,2392,2467,2561,2656,2747,2833,2932,3005,3084,3196,3301,3371,3434,4105,4755,4829,4935,5035,5090,5180,5264,5332,5426,5520,5578,5661,5710,5788,5898,5967,6039,6106,6172,6221,6301,6397,6473,6520,6566,6638,6696,6756,6931,7090,7214,7283,7365,7445,7532,7613,7701,7779,7874,7956,8062,8149,8245,8301,8437,8486,8545,8612,8678,8751,8820,8891,8979,9051,9102,9182"}, "to": {"startLines": "158,159,160,161,162,163,164,168,169,170,171,174,176,177,179,184,188,203,206,207,208,210,211,212,214,218,219,220,221,222,223,224,225,226,227,229,232,233,239,240,241,252,253,254,255,256,257,258,259,260,261,262,263,270,271,272,275,276,277,278,282,287,288,289,290,291,296,297,298,299,300,301,305,306,316,317,319,320,324,325,327,332,339,340,411,412,413,415,420,433,434,435,436,437,438,439,455", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14217,14287,14366,14428,14498,14577,14635,14928,14982,15057,15141,15372,15580,15686,15839,16196,16492,17536,17786,17884,17957,18121,18212,18304,18480,18777,18866,18946,19048,19123,19217,19312,19403,19489,19588,19740,19993,20105,20892,20962,21025,22654,23304,23378,23484,23584,23639,23729,23813,23881,23975,24069,24127,24718,24767,24845,25125,25194,25266,25333,25836,26221,26301,26397,26473,26520,26810,26882,26940,27000,27175,27334,27647,27716,28633,28713,28879,28960,29315,29393,29554,30170,30713,30800,38380,38436,38572,38728,39284,41338,41404,41477,41546,41617,41705,41777,43187", "endColumns": "69,78,61,69,78,57,58,53,74,83,82,105,105,85,83,80,87,99,97,72,94,90,91,113,83,88,79,101,74,93,94,90,85,98,72,78,111,104,69,62,670,649,73,105,99,54,89,83,67,93,93,57,82,48,77,109,68,71,66,65,48,79,95,75,46,45,71,57,59,174,158,123,68,81,79,86,80,87,77,94,81,105,86,95,55,135,48,58,66,65,72,68,70,87,71,50,79", "endOffsets": "14282,14361,14423,14493,14572,14630,14689,14977,15052,15136,15219,15473,15681,15767,15918,16272,16575,17631,17879,17952,18047,18207,18299,18413,18559,18861,18941,19043,19118,19212,19307,19398,19484,19583,19656,19814,20100,20205,20957,21020,21691,23299,23373,23479,23579,23634,23724,23808,23876,23970,24064,24122,24205,24762,24840,24950,25189,25261,25328,25394,25880,26296,26392,26468,26515,26561,26877,26935,26995,27170,27329,27453,27711,27793,28708,28795,28955,29043,29388,29483,29631,30271,30795,30891,38431,38567,38616,38782,39346,41399,41472,41541,41612,41700,41772,41823,43262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,475,570,677,763,867,986,1071,1153,1244,1337,1432,1526,1626,1719,1814,1909,2000,2091,2177,2281,2393,2494,2599,2713,2815,2984,13723", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "470,565,672,758,862,981,1066,1148,1239,1332,1427,1521,1621,1714,1809,1904,1995,2086,2172,2276,2388,2489,2594,2708,2810,2979,3076,13803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,297,391,490,580,659,752,847,932,1004,1075,1156,1242,1315,1394,1464", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "205,292,386,485,575,654,747,842,927,999,1070,1151,1237,1310,1389,1459,1577"}, "to": {"startLines": "49,50,71,72,74,84,85,145,146,147,148,150,151,155,472,473,474", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4548,4653,7107,7201,7381,8224,8303,13153,13248,13333,13405,13556,13637,13968,44769,44848,44918", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "4648,4735,7196,7295,7466,8298,8391,13243,13328,13400,13471,13632,13718,14036,44843,44913,45031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,214,289,385,477,560,642,778,874,978,1072,1198,1261,1377,1449,1622,1810,1959,2047,2153,2246,2345,2533,2645,2745,3003,3103,3238,3463,3689,3791,3919,4101,4196,4258,4322,4406,4498,4604,4685,4786,4880,4993,5214,5281,5351,5426,5545,5628,5712,5784,5871,5942,6018,6138,6227,6335,6431,6506,6590,6660,6746,6806,6910,7022,7121,7244,7309,7404,7504,7635,7707,7796,7903,7965,8030,8112,8209,8365,8631,8917,9012,9089,9173,9243,9371,9492,9582,9660,9732,9817,9899,9993,10113,10251,10319,10393,10448,10613,10682,10739,10818,10891,10969,11050,11174,11301,11417,11503,11579,11661,11732", "endColumns": "74,83,74,95,91,82,81,135,95,103,93,125,62,115,71,172,187,148,87,105,92,98,187,111,99,257,99,134,224,225,101,127,181,94,61,63,83,91,105,80,100,93,112,220,66,69,74,118,82,83,71,86,70,75,119,88,107,95,74,83,69,85,59,103,111,98,122,64,94,99,130,71,88,106,61,64,81,96,155,265,285,94,76,83,69,127,120,89,77,71,84,81,93,119,137,67,73,54,164,68,56,78,72,77,80,123,126,115,85,75,81,70,162", "endOffsets": "125,209,284,380,472,555,637,773,869,973,1067,1193,1256,1372,1444,1617,1805,1954,2042,2148,2241,2340,2528,2640,2740,2998,3098,3233,3458,3684,3786,3914,4096,4191,4253,4317,4401,4493,4599,4680,4781,4875,4988,5209,5276,5346,5421,5540,5623,5707,5779,5866,5937,6013,6133,6222,6330,6426,6501,6585,6655,6741,6801,6905,7017,7116,7239,7304,7399,7499,7630,7702,7791,7898,7960,8025,8107,8204,8360,8626,8912,9007,9084,9168,9238,9366,9487,9577,9655,9727,9812,9894,9988,10108,10246,10314,10388,10443,10608,10677,10734,10813,10886,10964,11045,11169,11296,11412,11498,11574,11656,11727,11890"}, "to": {"startLines": "165,166,167,237,249,250,251,268,281,283,284,315,333,335,338,342,343,344,347,349,351,352,353,354,355,356,357,358,359,360,361,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,410,414,424,425,426,427,428,429,430,431,432,441,442,443,444,445,446,447,448,449,450,451,452,453,454,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14694,14769,14853,20599,22397,22489,22572,24518,25740,25885,25989,28507,30276,30402,30641,30957,31130,31318,31620,31814,32017,32110,32209,32397,32509,32609,32867,32967,33102,33327,33553,33742,33870,34052,34147,34209,34273,34357,34449,34555,34636,34737,34831,34944,35165,35232,35302,35377,35496,35579,35663,35735,35822,35893,35969,36524,36613,36721,36817,36892,36976,37046,37132,37192,37296,37408,37507,37630,37695,37790,37890,38021,38291,38621,40152,40214,40279,40361,40458,40614,40880,41166,41261,41883,41967,42037,42165,42286,42376,42454,42526,42611,42693,42787,42907,43045,43113,43267,43322,43487,43556,43613,43692,43765,43843,43924,44048,44175,44291,44377,44453,44535,44606", "endColumns": "74,83,74,95,91,82,81,135,95,103,93,125,62,115,71,172,187,148,87,105,92,98,187,111,99,257,99,134,224,225,101,127,181,94,61,63,83,91,105,80,100,93,112,220,66,69,74,118,82,83,71,86,70,75,119,88,107,95,74,83,69,85,59,103,111,98,122,64,94,99,130,71,88,106,61,64,81,96,155,265,285,94,76,83,69,127,120,89,77,71,84,81,93,119,137,67,73,54,164,68,56,78,72,77,80,123,126,115,85,75,81,70,162", "endOffsets": "14764,14848,14923,20690,22484,22567,22649,24649,25831,25984,26078,28628,30334,30513,30708,31125,31313,31462,31703,31915,32105,32204,32392,32504,32604,32862,32962,33097,33322,33548,33650,33865,34047,34142,34204,34268,34352,34444,34550,34631,34732,34826,34939,35160,35227,35297,35372,35491,35574,35658,35730,35817,35888,35964,36084,36608,36716,36812,36887,36971,37041,37127,37187,37291,37403,37502,37625,37690,37785,37885,38016,38088,38375,38723,40209,40274,40356,40453,40609,40875,41161,41256,41333,41962,42032,42160,42281,42371,42449,42521,42606,42688,42782,42902,43040,43108,43182,43317,43482,43551,43608,43687,43760,43838,43919,44043,44170,44286,44372,44448,44530,44601,44764"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,211", "endColumns": "74,80,76", "endOffsets": "125,206,283"}, "to": {"startLines": "51,73,77", "startColumns": "4,4,4", "startOffsets": "4740,7300,7606", "endColumns": "74,80,76", "endOffsets": "4810,7376,7678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,101", "endOffsets": "155,255,369,471"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "7002,7779,7879,7993", "endColumns": "104,99,113,101", "endOffsets": "7102,7874,7988,8090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,340,444,641,688,755,853,927,1149,1234,1342,1411,1465,1529,1606,1699,1989,2062,2127,2181,2234,2311,2429,2540,2597,2679,2758,2843,2909,3015,3283,3366,3443,3506,3567,3629,3690,3761,3843,3949,4046,4133,4236,4328,4404,4483,4568,4766,4964,5072,5201,5263,5903,6002,6064", "endColumns": "121,162,103,196,46,66,97,73,221,84,107,68,53,63,76,92,289,72,64,53,52,76,117,110,56,81,78,84,65,105,267,82,76,62,60,61,60,70,81,105,96,86,102,91,75,78,84,197,197,107,128,61,639,98,61,54", "endOffsets": "172,335,439,636,683,750,848,922,1144,1229,1337,1406,1460,1524,1601,1694,1984,2057,2122,2176,2229,2306,2424,2535,2592,2674,2753,2838,2904,3010,3278,3361,3438,3501,3562,3624,3685,3756,3838,3944,4041,4128,4231,4323,4399,4478,4563,4761,4959,5067,5196,5258,5898,5997,6059,6114"}, "to": {"startLines": "234,235,236,238,242,243,244,245,246,247,248,264,267,269,273,274,279,285,286,294,304,308,309,310,311,312,318,321,326,328,329,330,331,334,336,337,341,345,346,348,350,362,387,388,389,390,391,409,416,417,418,419,421,422,423,440", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20210,20332,20495,20695,21696,21743,21810,21908,21982,22204,22289,24210,24464,24654,24955,25032,25399,26083,26156,26692,27594,27877,27954,28072,28183,28240,28800,29048,29488,29636,29742,30010,30093,30339,30518,30579,30896,31467,31538,31708,31920,33655,36089,36192,36284,36360,36439,38093,38787,38985,39093,39222,39351,39991,40090,41828", "endColumns": "121,162,103,196,46,66,97,73,221,84,107,68,53,63,76,92,289,72,64,53,52,76,117,110,56,81,78,84,65,105,267,82,76,62,60,61,60,70,81,105,96,86,102,91,75,78,84,197,197,107,128,61,639,98,61,54", "endOffsets": "20327,20490,20594,20887,21738,21805,21903,21977,22199,22284,22392,24274,24513,24713,25027,25120,25684,26151,26216,26741,27642,27949,28067,28178,28235,28317,28874,29128,29549,29737,30005,30088,30165,30397,30574,30636,30952,31533,31615,31809,32012,33737,36187,36279,36355,36434,36519,38286,38980,39088,39217,39279,39986,40085,40147,41878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-hr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5827", "endColumns": "131", "endOffsets": "5954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,198,262,347,413,481,539,617,693,762,824", "endColumns": "83,58,63,84,65,67,57,77,75,68,61,66", "endOffsets": "134,193,257,342,408,476,534,612,688,757,819,886"}, "to": {"startLines": "173,183,185,186,187,191,199,202,205,209,213,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15288,16137,16277,16341,16426,16729,17269,17458,17710,18052,18418,18710", "endColumns": "83,58,63,84,65,67,57,77,75,68,61,66", "endOffsets": "15367,16191,16336,16421,16487,16792,17322,17531,17781,18116,18475,18772"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "82,477", "startColumns": "4,4", "startOffsets": "8095,45204", "endColumns": "60,77", "endOffsets": "8151,45277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "39,40,41,42,43,44,45,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3512,3610,3717,3814,3913,4017,4121,14116", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3605,3712,3809,3908,4012,4116,4233,14212"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,221,288,374,441,502,587,651,717,785,864,924,985,1059,1123,1191,1254,1328,1394,1474,1553,1634,1727,1827,1912,1963,2011,2089,2153,2218,2289,2389,2474,2565", "endColumns": "63,101,66,85,66,60,84,63,65,67,78,59,60,73,63,67,62,73,65,79,78,80,92,99,84,50,47,77,63,64,70,99,84,90,90", "endOffsets": "114,216,283,369,436,497,582,646,712,780,859,919,980,1054,1118,1186,1249,1323,1389,1469,1548,1629,1722,1822,1907,1958,2006,2084,2148,2213,2284,2384,2469,2560,2651"}, "to": {"startLines": "172,175,178,180,181,182,189,190,192,193,194,195,196,197,198,200,201,204,215,216,228,230,231,265,266,280,292,293,295,302,303,313,314,322,323", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15224,15478,15772,15923,16009,16076,16580,16665,16797,16863,16931,17010,17070,17131,17205,17327,17395,17636,18564,18630,19661,19819,19900,24279,24379,25689,26566,26614,26746,27458,27523,28322,28422,29133,29224", "endColumns": "63,101,66,85,66,60,84,63,65,67,78,59,60,73,63,67,62,73,65,79,78,80,92,99,84,50,47,77,63,64,70,99,84,90,90", "endOffsets": "15283,15575,15834,16004,16071,16132,16660,16724,16858,16926,17005,17065,17126,17200,17264,17390,17453,17705,18625,18705,19735,19895,19988,24374,24459,25735,26609,26687,26805,27518,27589,28417,28502,29219,29310"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "475,476", "startColumns": "4,4", "startOffsets": "45036,45119", "endColumns": "82,84", "endOffsets": "45114,45199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "78", "endOffsets": "129"}, "to": {"startLines": "307", "startColumns": "4", "startOffsets": "27798", "endColumns": "78", "endOffsets": "27872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,448,574,680,833,959,1070,1173,1319,1422,1575,1699,1842,1981,2045,2103", "endColumns": "101,152,125,105,152,125,110,102,145,102,152,123,142,138,63,57,76", "endOffsets": "294,447,573,679,832,958,1069,1172,1318,1421,1574,1698,1841,1980,2044,2102,2179"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4815,4921,5078,5208,5318,5475,5605,5720,5959,6109,6216,6373,6501,6648,6791,6859,6921", "endColumns": "105,156,129,109,156,129,114,106,149,106,156,127,146,142,67,61,80", "endOffsets": "4916,5073,5203,5313,5470,5600,5715,5822,6104,6211,6368,6496,6643,6786,6854,6916,6997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1128,1196,1292,1360,1423,1531,1591,1657,1713,1784,1844,1898,2024,2081,2143,2197,2272,2406,2491,2569,2664,2749,2830,2967,3051,3137,3270,3361,3439,3495,3550,3616,3690,3768,3839,3921,3993,4070,4150,4224,4331,4424,4497,4589,4685,4759,4835,4931,4983,5065,5132,5219,5306,5368,5432,5495,5565,5671,5787,5884,5998,6058,6117,6197,6280,6357", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "315,393,471,556,653,746,842,972,1056,1123,1191,1287,1355,1418,1526,1586,1652,1708,1779,1839,1893,2019,2076,2138,2192,2267,2401,2486,2564,2659,2744,2825,2962,3046,3132,3265,3356,3434,3490,3545,3611,3685,3763,3834,3916,3988,4065,4145,4219,4326,4419,4492,4584,4680,4754,4830,4926,4978,5060,5127,5214,5301,5363,5427,5490,5560,5666,5782,5879,5993,6053,6112,6192,6275,6352,6427"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,75,76,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,149,153,154,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3081,3159,3237,3322,3419,4238,4334,4464,7471,7538,7683,8156,8396,8459,8567,8627,8693,8749,8820,8880,8934,9060,9117,9179,9233,9308,9442,9527,9605,9700,9785,9866,10003,10087,10173,10306,10397,10475,10531,10586,10652,10726,10804,10875,10957,11029,11106,11186,11260,11367,11460,11533,11625,11721,11795,11871,11967,12019,12101,12168,12255,12342,12404,12468,12531,12601,12707,12823,12920,13034,13094,13476,13808,13891,14041", "endLines": "6,34,35,36,37,38,46,47,48,75,76,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,149,153,154,156", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "365,3154,3232,3317,3414,3507,4329,4459,4543,7533,7601,7774,8219,8454,8562,8622,8688,8744,8815,8875,8929,9055,9112,9174,9228,9303,9437,9522,9600,9695,9780,9861,9998,10082,10168,10301,10392,10470,10526,10581,10647,10721,10799,10870,10952,11024,11101,11181,11255,11362,11455,11528,11620,11716,11790,11866,11962,12014,12096,12163,12250,12337,12399,12463,12526,12596,12702,12818,12915,13029,13089,13148,13551,13886,13963,14111"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,266,336,415,473,532,586,661,745,828,934,1040,1126,1210,1291,1379,1479,1577,1650,1745,1836,1928,2042,2126,2215,2295,2397,2472,2566,2661,2752,2838,2937,3010,3089,3201,3306,3376,3439,4110,4760,4834,4940,5040,5095,5185,5269,5337,5431,5525,5583,5666,5715,5793,5903,5972,6044,6111,6177,6226,6306,6402,6478,6525,6571,6643,6701,6761,6936,7095,7219,7288,7370,7450,7537,7618,7706,7784,7879,7961,8067,8154,8250,8306,8442,8491,8550,8617,8683,8756,8825,8896,8984,9056,9107", "endColumns": "69,78,61,69,78,57,58,53,74,83,82,105,105,85,83,80,87,99,97,72,94,90,91,113,83,88,79,101,74,93,94,90,85,98,72,78,111,104,69,62,670,649,73,105,99,54,89,83,67,93,93,57,82,48,77,109,68,71,66,65,48,79,95,75,46,45,71,57,59,174,158,123,68,81,79,86,80,87,77,94,81,105,86,95,55,135,48,58,66,65,72,68,70,87,71,50,79", "endOffsets": "120,199,261,331,410,468,527,581,656,740,823,929,1035,1121,1205,1286,1374,1474,1572,1645,1740,1831,1923,2037,2121,2210,2290,2392,2467,2561,2656,2747,2833,2932,3005,3084,3196,3301,3371,3434,4105,4755,4829,4935,5035,5090,5180,5264,5332,5426,5520,5578,5661,5710,5788,5898,5967,6039,6106,6172,6221,6301,6397,6473,6520,6566,6638,6696,6756,6931,7090,7214,7283,7365,7445,7532,7613,7701,7779,7874,7956,8062,8149,8245,8301,8437,8486,8545,8612,8678,8751,8820,8891,8979,9051,9102,9182"}, "to": {"startLines": "158,159,160,161,162,163,164,168,169,170,171,174,176,177,179,184,188,203,206,207,208,210,211,212,214,218,219,220,221,222,223,224,225,226,227,229,232,233,239,240,241,252,253,254,255,256,257,258,259,260,261,262,263,270,271,272,275,276,277,278,282,287,288,289,290,291,296,297,298,299,300,301,305,306,316,317,319,320,324,325,327,332,339,340,411,412,413,415,420,433,434,435,436,437,438,439,455", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14217,14287,14366,14428,14498,14577,14635,14928,14982,15057,15141,15372,15580,15686,15839,16196,16492,17536,17786,17884,17957,18121,18212,18304,18480,18777,18866,18946,19048,19123,19217,19312,19403,19489,19588,19740,19993,20105,20892,20962,21025,22654,23304,23378,23484,23584,23639,23729,23813,23881,23975,24069,24127,24718,24767,24845,25125,25194,25266,25333,25836,26221,26301,26397,26473,26520,26810,26882,26940,27000,27175,27334,27647,27716,28633,28713,28879,28960,29315,29393,29554,30170,30713,30800,38380,38436,38572,38728,39284,41338,41404,41477,41546,41617,41705,41777,43187", "endColumns": "69,78,61,69,78,57,58,53,74,83,82,105,105,85,83,80,87,99,97,72,94,90,91,113,83,88,79,101,74,93,94,90,85,98,72,78,111,104,69,62,670,649,73,105,99,54,89,83,67,93,93,57,82,48,77,109,68,71,66,65,48,79,95,75,46,45,71,57,59,174,158,123,68,81,79,86,80,87,77,94,81,105,86,95,55,135,48,58,66,65,72,68,70,87,71,50,79", "endOffsets": "14282,14361,14423,14493,14572,14630,14689,14977,15052,15136,15219,15473,15681,15767,15918,16272,16575,17631,17879,17952,18047,18207,18299,18413,18559,18861,18941,19043,19118,19212,19307,19398,19484,19583,19656,19814,20100,20205,20957,21020,21691,23299,23373,23479,23579,23634,23724,23808,23876,23970,24064,24122,24205,24762,24840,24950,25189,25261,25328,25394,25880,26296,26392,26468,26515,26561,26877,26935,26995,27170,27329,27453,27711,27793,28708,28795,28955,29043,29388,29483,29631,30271,30795,30891,38431,38567,38616,38782,39346,41399,41472,41541,41612,41700,41772,41823,43262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,475,570,677,763,867,986,1071,1153,1244,1337,1432,1526,1626,1719,1814,1909,2000,2091,2177,2281,2393,2494,2599,2713,2815,2984,13723", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "470,565,672,758,862,981,1066,1148,1239,1332,1427,1521,1621,1714,1809,1904,1995,2086,2172,2276,2388,2489,2594,2708,2810,2979,3076,13803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,297,391,490,580,659,752,847,932,1004,1075,1156,1242,1315,1394,1464", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "205,292,386,485,575,654,747,842,927,999,1070,1151,1237,1310,1389,1459,1577"}, "to": {"startLines": "49,50,71,72,74,84,85,145,146,147,148,150,151,155,472,473,474", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4548,4653,7107,7201,7381,8224,8303,13153,13248,13333,13405,13556,13637,13968,44769,44848,44918", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "4648,4735,7196,7295,7466,8298,8391,13243,13328,13400,13471,13632,13718,14036,44843,44913,45031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,214,289,385,477,560,642,778,874,978,1072,1198,1261,1377,1449,1622,1810,1959,2047,2153,2246,2345,2533,2645,2745,3003,3103,3238,3463,3689,3791,3919,4101,4196,4258,4322,4406,4498,4604,4685,4786,4880,4993,5214,5281,5351,5426,5545,5628,5712,5784,5871,5942,6018,6138,6227,6335,6431,6506,6590,6660,6746,6806,6910,7022,7121,7244,7309,7404,7504,7635,7707,7796,7903,7965,8030,8112,8209,8365,8631,8917,9012,9089,9173,9243,9371,9492,9582,9660,9732,9817,9899,9993,10113,10251,10319,10393,10448,10613,10682,10739,10818,10891,10969,11050,11174,11301,11417,11503,11579,11661,11732", "endColumns": "74,83,74,95,91,82,81,135,95,103,93,125,62,115,71,172,187,148,87,105,92,98,187,111,99,257,99,134,224,225,101,127,181,94,61,63,83,91,105,80,100,93,112,220,66,69,74,118,82,83,71,86,70,75,119,88,107,95,74,83,69,85,59,103,111,98,122,64,94,99,130,71,88,106,61,64,81,96,155,265,285,94,76,83,69,127,120,89,77,71,84,81,93,119,137,67,73,54,164,68,56,78,72,77,80,123,126,115,85,75,81,70,162", "endOffsets": "125,209,284,380,472,555,637,773,869,973,1067,1193,1256,1372,1444,1617,1805,1954,2042,2148,2241,2340,2528,2640,2740,2998,3098,3233,3458,3684,3786,3914,4096,4191,4253,4317,4401,4493,4599,4680,4781,4875,4988,5209,5276,5346,5421,5540,5623,5707,5779,5866,5937,6013,6133,6222,6330,6426,6501,6585,6655,6741,6801,6905,7017,7116,7239,7304,7399,7499,7630,7702,7791,7898,7960,8025,8107,8204,8360,8626,8912,9007,9084,9168,9238,9366,9487,9577,9655,9727,9812,9894,9988,10108,10246,10314,10388,10443,10608,10677,10734,10813,10886,10964,11045,11169,11296,11412,11498,11574,11656,11727,11890"}, "to": {"startLines": "165,166,167,237,249,250,251,268,281,283,284,315,333,335,338,342,343,344,347,349,351,352,353,354,355,356,357,358,359,360,361,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,410,414,424,425,426,427,428,429,430,431,432,441,442,443,444,445,446,447,448,449,450,451,452,453,454,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14694,14769,14853,20599,22397,22489,22572,24518,25740,25885,25989,28507,30276,30402,30641,30957,31130,31318,31620,31814,32017,32110,32209,32397,32509,32609,32867,32967,33102,33327,33553,33742,33870,34052,34147,34209,34273,34357,34449,34555,34636,34737,34831,34944,35165,35232,35302,35377,35496,35579,35663,35735,35822,35893,35969,36524,36613,36721,36817,36892,36976,37046,37132,37192,37296,37408,37507,37630,37695,37790,37890,38021,38291,38621,40152,40214,40279,40361,40458,40614,40880,41166,41261,41883,41967,42037,42165,42286,42376,42454,42526,42611,42693,42787,42907,43045,43113,43267,43322,43487,43556,43613,43692,43765,43843,43924,44048,44175,44291,44377,44453,44535,44606", "endColumns": "74,83,74,95,91,82,81,135,95,103,93,125,62,115,71,172,187,148,87,105,92,98,187,111,99,257,99,134,224,225,101,127,181,94,61,63,83,91,105,80,100,93,112,220,66,69,74,118,82,83,71,86,70,75,119,88,107,95,74,83,69,85,59,103,111,98,122,64,94,99,130,71,88,106,61,64,81,96,155,265,285,94,76,83,69,127,120,89,77,71,84,81,93,119,137,67,73,54,164,68,56,78,72,77,80,123,126,115,85,75,81,70,162", "endOffsets": "14764,14848,14923,20690,22484,22567,22649,24649,25831,25984,26078,28628,30334,30513,30708,31125,31313,31462,31703,31915,32105,32204,32392,32504,32604,32862,32962,33097,33322,33548,33650,33865,34047,34142,34204,34268,34352,34444,34550,34631,34732,34826,34939,35160,35227,35297,35372,35491,35574,35658,35730,35817,35888,35964,36084,36608,36716,36812,36887,36971,37041,37127,37187,37291,37403,37502,37625,37690,37785,37885,38016,38088,38375,38723,40209,40274,40356,40453,40609,40875,41161,41256,41333,41962,42032,42160,42281,42371,42449,42521,42606,42688,42782,42902,43040,43108,43182,43317,43482,43551,43608,43687,43760,43838,43919,44043,44170,44286,44372,44448,44530,44601,44764"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,211", "endColumns": "74,80,76", "endOffsets": "125,206,283"}, "to": {"startLines": "51,73,77", "startColumns": "4,4,4", "startOffsets": "4740,7300,7606", "endColumns": "74,80,76", "endOffsets": "4810,7376,7678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,101", "endOffsets": "155,255,369,471"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "7002,7779,7879,7993", "endColumns": "104,99,113,101", "endOffsets": "7102,7874,7988,8090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,340,444,641,688,755,853,927,1149,1234,1342,1411,1465,1529,1606,1699,1989,2062,2127,2181,2234,2311,2429,2540,2597,2679,2758,2843,2909,3015,3283,3366,3443,3506,3567,3629,3690,3761,3843,3949,4046,4133,4236,4328,4404,4483,4568,4766,4964,5072,5201,5263,5903,6002,6064", "endColumns": "121,162,103,196,46,66,97,73,221,84,107,68,53,63,76,92,289,72,64,53,52,76,117,110,56,81,78,84,65,105,267,82,76,62,60,61,60,70,81,105,96,86,102,91,75,78,84,197,197,107,128,61,639,98,61,54", "endOffsets": "172,335,439,636,683,750,848,922,1144,1229,1337,1406,1460,1524,1601,1694,1984,2057,2122,2176,2229,2306,2424,2535,2592,2674,2753,2838,2904,3010,3278,3361,3438,3501,3562,3624,3685,3756,3838,3944,4041,4128,4231,4323,4399,4478,4563,4761,4959,5067,5196,5258,5898,5997,6059,6114"}, "to": {"startLines": "234,235,236,238,242,243,244,245,246,247,248,264,267,269,273,274,279,285,286,294,304,308,309,310,311,312,318,321,326,328,329,330,331,334,336,337,341,345,346,348,350,362,387,388,389,390,391,409,416,417,418,419,421,422,423,440", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20210,20332,20495,20695,21696,21743,21810,21908,21982,22204,22289,24210,24464,24654,24955,25032,25399,26083,26156,26692,27594,27877,27954,28072,28183,28240,28800,29048,29488,29636,29742,30010,30093,30339,30518,30579,30896,31467,31538,31708,31920,33655,36089,36192,36284,36360,36439,38093,38787,38985,39093,39222,39351,39991,40090,41828", "endColumns": "121,162,103,196,46,66,97,73,221,84,107,68,53,63,76,92,289,72,64,53,52,76,117,110,56,81,78,84,65,105,267,82,76,62,60,61,60,70,81,105,96,86,102,91,75,78,84,197,197,107,128,61,639,98,61,54", "endOffsets": "20327,20490,20594,20887,21738,21805,21903,21977,22199,22284,22392,24274,24513,24713,25027,25120,25684,26151,26216,26741,27642,27949,28067,28178,28235,28317,28874,29128,29549,29737,30005,30088,30165,30397,30574,30636,30952,31533,31615,31809,32012,33737,36187,36279,36355,36434,36519,38286,38980,39088,39217,39279,39986,40085,40147,41878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-hr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5827", "endColumns": "131", "endOffsets": "5954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,198,262,347,413,481,539,617,693,762,824", "endColumns": "83,58,63,84,65,67,57,77,75,68,61,66", "endOffsets": "134,193,257,342,408,476,534,612,688,757,819,886"}, "to": {"startLines": "173,183,185,186,187,191,199,202,205,209,213,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15288,16137,16277,16341,16426,16729,17269,17458,17710,18052,18418,18710", "endColumns": "83,58,63,84,65,67,57,77,75,68,61,66", "endOffsets": "15367,16191,16336,16421,16487,16792,17322,17531,17781,18116,18475,18772"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "82,477", "startColumns": "4,4", "startOffsets": "8095,45204", "endColumns": "60,77", "endOffsets": "8151,45277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "39,40,41,42,43,44,45,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3512,3610,3717,3814,3913,4017,4121,14116", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3605,3712,3809,3908,4012,4116,4233,14212"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,221,288,374,441,502,587,651,717,785,864,924,985,1059,1123,1191,1254,1328,1394,1474,1553,1634,1727,1827,1912,1963,2011,2089,2153,2218,2289,2389,2474,2565", "endColumns": "63,101,66,85,66,60,84,63,65,67,78,59,60,73,63,67,62,73,65,79,78,80,92,99,84,50,47,77,63,64,70,99,84,90,90", "endOffsets": "114,216,283,369,436,497,582,646,712,780,859,919,980,1054,1118,1186,1249,1323,1389,1469,1548,1629,1722,1822,1907,1958,2006,2084,2148,2213,2284,2384,2469,2560,2651"}, "to": {"startLines": "172,175,178,180,181,182,189,190,192,193,194,195,196,197,198,200,201,204,215,216,228,230,231,265,266,280,292,293,295,302,303,313,314,322,323", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15224,15478,15772,15923,16009,16076,16580,16665,16797,16863,16931,17010,17070,17131,17205,17327,17395,17636,18564,18630,19661,19819,19900,24279,24379,25689,26566,26614,26746,27458,27523,28322,28422,29133,29224", "endColumns": "63,101,66,85,66,60,84,63,65,67,78,59,60,73,63,67,62,73,65,79,78,80,92,99,84,50,47,77,63,64,70,99,84,90,90", "endOffsets": "15283,15575,15834,16004,16071,16132,16660,16724,16858,16926,17005,17065,17126,17200,17264,17390,17453,17705,18625,18705,19735,19895,19988,24374,24459,25735,26609,26687,26805,27518,27589,28417,28502,29219,29310"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "475,476", "startColumns": "4,4", "startOffsets": "45036,45119", "endColumns": "82,84", "endOffsets": "45114,45199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "78", "endOffsets": "129"}, "to": {"startLines": "307", "startColumns": "4", "startOffsets": "27798", "endColumns": "78", "endOffsets": "27872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,448,574,680,833,959,1070,1173,1319,1422,1575,1699,1842,1981,2045,2103", "endColumns": "101,152,125,105,152,125,110,102,145,102,152,123,142,138,63,57,76", "endOffsets": "294,447,573,679,832,958,1069,1172,1318,1421,1574,1698,1841,1980,2044,2102,2179"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4815,4921,5078,5208,5318,5475,5605,5720,5959,6109,6216,6373,6501,6648,6791,6859,6921", "endColumns": "105,156,129,109,156,129,114,106,149,106,156,127,146,142,67,61,80", "endOffsets": "4916,5073,5203,5313,5470,5600,5715,5822,6104,6211,6368,6496,6643,6786,6854,6916,6997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1128,1196,1292,1360,1423,1531,1591,1657,1713,1784,1844,1898,2024,2081,2143,2197,2272,2406,2491,2569,2664,2749,2830,2967,3051,3137,3270,3361,3439,3495,3550,3616,3690,3768,3839,3921,3993,4070,4150,4224,4331,4424,4497,4589,4685,4759,4835,4931,4983,5065,5132,5219,5306,5368,5432,5495,5565,5671,5787,5884,5998,6058,6117,6197,6280,6357", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "315,393,471,556,653,746,842,972,1056,1123,1191,1287,1355,1418,1526,1586,1652,1708,1779,1839,1893,2019,2076,2138,2192,2267,2401,2486,2564,2659,2744,2825,2962,3046,3132,3265,3356,3434,3490,3545,3611,3685,3763,3834,3916,3988,4065,4145,4219,4326,4419,4492,4584,4680,4754,4830,4926,4978,5060,5127,5214,5301,5363,5427,5490,5560,5666,5782,5879,5993,6053,6112,6192,6275,6352,6427"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,75,76,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,149,153,154,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3081,3159,3237,3322,3419,4238,4334,4464,7471,7538,7683,8156,8396,8459,8567,8627,8693,8749,8820,8880,8934,9060,9117,9179,9233,9308,9442,9527,9605,9700,9785,9866,10003,10087,10173,10306,10397,10475,10531,10586,10652,10726,10804,10875,10957,11029,11106,11186,11260,11367,11460,11533,11625,11721,11795,11871,11967,12019,12101,12168,12255,12342,12404,12468,12531,12601,12707,12823,12920,13034,13094,13476,13808,13891,14041", "endLines": "6,34,35,36,37,38,46,47,48,75,76,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,149,153,154,156", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "365,3154,3232,3317,3414,3507,4329,4459,4543,7533,7601,7774,8219,8454,8562,8622,8688,8744,8815,8875,8929,9055,9112,9174,9228,9303,9437,9522,9600,9695,9780,9861,9998,10082,10168,10301,10392,10470,10526,10581,10647,10721,10799,10870,10952,11024,11101,11181,11255,11362,11455,11528,11620,11716,11790,11866,11962,12014,12096,12163,12250,12337,12399,12463,12526,12596,12702,12818,12915,13029,13089,13148,13551,13886,13963,14111"}}]}]}
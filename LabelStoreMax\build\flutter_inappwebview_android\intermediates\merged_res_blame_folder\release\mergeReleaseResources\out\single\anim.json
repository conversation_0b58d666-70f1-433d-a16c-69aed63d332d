[{"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/btn_checkbox_to_unchecked_check_path_merged_animation.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/btn_checkbox_to_unchecked_check_path_merged_animation.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/btn_radio_to_off_mtrl_dot_group_animation.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/btn_radio_to_off_mtrl_dot_group_animation.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/abc_slide_out_bottom.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/abc_slide_out_bottom.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/abc_fade_out.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/abc_fade_out.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/abc_slide_in_bottom.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/abc_slide_in_bottom.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/abc_slide_in_top.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/abc_slide_in_top.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/btn_checkbox_to_checked_box_outer_merged_animation.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/btn_checkbox_to_checked_box_outer_merged_animation.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/btn_checkbox_to_checked_icon_null_animation.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/btn_checkbox_to_checked_icon_null_animation.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/abc_slide_out_top.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/abc_slide_out_top.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/btn_radio_to_on_mtrl_ring_outer_path_animation.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/btn_radio_to_on_mtrl_ring_outer_path_animation.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/btn_checkbox_to_checked_box_inner_merged_animation.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/btn_checkbox_to_checked_box_inner_merged_animation.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/btn_checkbox_to_unchecked_icon_null_animation.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/btn_checkbox_to_unchecked_icon_null_animation.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/abc_tooltip_enter.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/abc_tooltip_enter.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/abc_popup_enter.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/abc_popup_enter.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/abc_popup_exit.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/abc_popup_exit.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/btn_radio_to_off_mtrl_ring_outer_path_animation.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/btn_radio_to_off_mtrl_ring_outer_path_animation.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/fragment_fast_out_extra_slow_in.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-fragment-1.7.1-18:/anim/fragment_fast_out_extra_slow_in.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/abc_tooltip_exit.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/abc_tooltip_exit.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/abc_grow_fade_in_from_bottom.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/abc_grow_fade_in_from_bottom.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/btn_checkbox_to_unchecked_box_inner_merged_animation.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/btn_checkbox_to_unchecked_box_inner_merged_animation.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/abc_shrink_fade_out_from_bottom.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/abc_shrink_fade_out_from_bottom.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/btn_radio_to_on_mtrl_ring_outer_animation.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/btn_radio_to_on_mtrl_ring_outer_animation.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/abc_fade_in.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/abc_fade_in.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/btn_radio_to_on_mtrl_dot_group_animation.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/btn_radio_to_on_mtrl_dot_group_animation.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/anim/btn_radio_to_off_mtrl_ring_outer_animation.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-appcompat-1.6.1-4:/anim/btn_radio_to_off_mtrl_ring_outer_animation.xml"}]
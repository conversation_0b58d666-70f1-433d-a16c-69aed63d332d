{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,444,568,675,838,961,1080,1182,1356,1458,1623,1745,1904,2082,2146,2205", "endColumns": "103,146,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,443,567,674,837,960,1079,1181,1355,1457,1622,1744,1903,2081,2145,2204,2279"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4769,4877,5028,5156,5267,5434,5561,5684,5933,6111,6217,6386,6512,6675,6857,6925,6988", "endColumns": "107,150,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "4872,5023,5151,5262,5429,5556,5679,5785,6106,6212,6381,6507,6670,6852,6920,6983,7062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,333,411,469,533,588,663,743,824,921,1018,1103,1182,1264,1352,1450,1541,1608,1696,1785,1871,1978,2060,2148,2222,2317,2390,2481,2569,2653,2735,2835,2904,2979,3089,3191,3263,3328,4090,4828,4905,5022,5123,5178,5281,5379,5444,5533,5622,5679,5760,5812,5892,6006,6077,6150,6217,6283,6332,6415,6512,6596,6643,6692,6762,6820,6886,7078,7245,7375,7440,7522,7607,7707,7791,7884,7959,8045,8119,8222,8309,8404,8457,8597,8651,8708,8783,8855,8930,8997,9067,9160,9235,9287", "endColumns": "68,77,61,68,77,57,63,54,74,79,80,96,96,84,78,81,87,97,90,66,87,88,85,106,81,87,73,94,72,90,87,83,81,99,68,74,109,101,71,64,761,737,76,116,100,54,102,97,64,88,88,56,80,51,79,113,70,72,66,65,48,82,96,83,46,48,69,57,65,191,166,129,64,81,84,99,83,92,74,85,73,102,86,94,52,139,53,56,74,71,74,66,69,92,74,51,77", "endOffsets": "119,197,259,328,406,464,528,583,658,738,819,916,1013,1098,1177,1259,1347,1445,1536,1603,1691,1780,1866,1973,2055,2143,2217,2312,2385,2476,2564,2648,2730,2830,2899,2974,3084,3186,3258,3323,4085,4823,4900,5017,5118,5173,5276,5374,5439,5528,5617,5674,5755,5807,5887,6001,6072,6145,6212,6278,6327,6410,6507,6591,6638,6687,6757,6815,6881,7073,7240,7370,7435,7517,7602,7702,7786,7879,7954,8040,8114,8217,8304,8399,8452,8592,8646,8703,8778,8850,8925,8992,9062,9155,9230,9282,9360"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14755,14824,14902,14964,15033,15111,15169,15483,15538,15613,15693,15913,16086,16183,16336,16692,16984,18018,18262,18353,18420,18576,18665,18751,18919,19219,19307,19381,19476,19549,19640,19728,19812,19894,19994,20140,20381,20491,21286,21358,21423,23137,23875,23952,24069,24170,24225,24328,24426,24491,24580,24669,24726,25317,25369,25449,25737,25808,25881,25948,26492,26869,26952,27049,27133,27180,27479,27549,27607,27673,27865,28032,28350,28415,29385,29470,29649,29733,30101,30176,30329,30980,31530,31617,39313,39366,39506,39668,40270,42545,42617,42692,42759,42829,42922,42997,44406", "endColumns": "68,77,61,68,77,57,63,54,74,79,80,96,96,84,78,81,87,97,90,66,87,88,85,106,81,87,73,94,72,90,87,83,81,99,68,74,109,101,71,64,761,737,76,116,100,54,102,97,64,88,88,56,80,51,79,113,70,72,66,65,48,82,96,83,46,48,69,57,65,191,166,129,64,81,84,99,83,92,74,85,73,102,86,94,52,139,53,56,74,71,74,66,69,92,74,51,77", "endOffsets": "14819,14897,14959,15028,15106,15164,15228,15533,15608,15688,15769,16005,16178,16263,16410,16769,17067,18111,18348,18415,18503,18660,18746,18853,18996,19302,19376,19471,19544,19635,19723,19807,19889,19989,20058,20210,20486,20588,21353,21418,22180,23870,23947,24064,24165,24220,24323,24421,24486,24575,24664,24721,24802,25364,25444,25558,25803,25876,25943,26009,26536,26947,27044,27128,27175,27224,27544,27602,27668,27860,28027,28157,28410,28492,29465,29565,29728,29821,30171,30257,30398,31078,31612,31707,39361,39501,39555,39720,40340,42612,42687,42754,42824,42917,42992,43044,44479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,437,542,649,734,838,958,1036,1112,1204,1298,1393,1487,1587,1681,1777,1872,1964,2056,2138,2249,2352,2451,2566,2680,2783,2938,13717", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "432,537,644,729,833,953,1031,1107,1199,1293,1388,1482,1582,1676,1772,1867,1959,2051,2133,2244,2347,2446,2561,2675,2778,2933,3036,13795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,974,1039,1120,1203,1280,1364,1434", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,969,1034,1115,1198,1275,1359,1429,1549"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4512,4604,7170,7267,7446,8274,8350,13166,13253,13342,13407,13553,13634,13959,46083,46167,46237", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "4599,4682,7262,7361,7526,8345,8441,13248,13337,13402,13467,13629,13712,14031,46162,46232,46352"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,137,217", "endColumns": "81,79,78", "endOffsets": "132,212,291"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4687,7366,7655", "endColumns": "81,79,78", "endOffsets": "4764,7441,7729"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,71", "endOffsets": "258,330"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "8134,46529", "endColumns": "60,75", "endOffsets": "8190,46600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,223,305,395,482,563,643,780,870,971,1061,1190,1251,1379,1445,1645,1864,2020,2112,2213,2306,2400,2581,2678,2777,3030,3142,3280,3488,3714,3806,3935,4157,4252,4314,4381,4464,4560,4661,4741,4835,4922,5024,5232,5302,5378,5454,5590,5666,5756,5822,5911,5983,6053,6169,6256,6365,6464,6548,6639,6712,6802,6863,6970,7078,7187,7321,7386,7481,7581,7717,7789,7879,7987,8047,8109,8188,8295,8468,8777,9107,9205,9282,9378,9452,9563,9681,9769,9851,9924,10012,10095,10187,10308,10439,10506,10584,10644,10837,10906,10966,11047,11119,11197,11284,11430,11562,11685,11779,11860,11949,12025", "endColumns": "74,92,81,89,86,80,79,136,89,100,89,128,60,127,65,199,218,155,91,100,92,93,180,96,98,252,111,137,207,225,91,128,221,94,61,66,82,95,100,79,93,86,101,207,69,75,75,135,75,89,65,88,71,69,115,86,108,98,83,90,72,89,60,106,107,108,133,64,94,99,135,71,89,107,59,61,78,106,172,308,329,97,76,95,73,110,117,87,81,72,87,82,91,120,130,66,77,59,192,68,59,80,71,77,86,145,131,122,93,80,88,75,157", "endOffsets": "125,218,300,390,477,558,638,775,865,966,1056,1185,1246,1374,1440,1640,1859,2015,2107,2208,2301,2395,2576,2673,2772,3025,3137,3275,3483,3709,3801,3930,4152,4247,4309,4376,4459,4555,4656,4736,4830,4917,5019,5227,5297,5373,5449,5585,5661,5751,5817,5906,5978,6048,6164,6251,6360,6459,6543,6634,6707,6797,6858,6965,7073,7182,7316,7381,7476,7576,7712,7784,7874,7982,8042,8104,8183,8290,8463,8772,9102,9200,9277,9373,9447,9558,9676,9764,9846,9919,10007,10090,10182,10303,10434,10501,10579,10639,10832,10901,10961,11042,11114,11192,11279,11425,11557,11680,11774,11855,11944,12020,12178"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15233,15308,15401,20961,22889,22976,23057,25116,26402,26541,26642,29256,31083,31212,31464,31772,31972,32191,32510,32700,32904,32997,33091,33272,33369,33468,33721,33833,33971,34179,34405,34583,34712,34934,35029,35091,35158,35241,35337,35438,35518,35612,35699,35801,36009,36079,36155,36231,36367,36443,36533,36599,36688,36760,36830,37384,37471,37580,37679,37763,37854,37927,38017,38078,38185,38293,38402,38536,38601,38696,38796,38932,39223,39560,41250,41310,41372,41451,41558,41731,42040,42370,42468,43104,43200,43274,43385,43503,43591,43673,43746,43834,43917,44009,44130,44261,44328,44484,44544,44737,44806,44866,44947,45019,45097,45184,45330,45462,45585,45679,45760,45849,45925", "endColumns": "74,92,81,89,86,80,79,136,89,100,89,128,60,127,65,199,218,155,91,100,92,93,180,96,98,252,111,137,207,225,91,128,221,94,61,66,82,95,100,79,93,86,101,207,69,75,75,135,75,89,65,88,71,69,115,86,108,98,83,90,72,89,60,106,107,108,133,64,94,99,135,71,89,107,59,61,78,106,172,308,329,97,76,95,73,110,117,87,81,72,87,82,91,120,130,66,77,59,192,68,59,80,71,77,86,145,131,122,93,80,88,75,157", "endOffsets": "15303,15396,15478,21046,22971,23052,23132,25248,26487,26637,26727,29380,31139,31335,31525,31967,32186,32342,32597,32796,32992,33086,33267,33364,33463,33716,33828,33966,34174,34400,34492,34707,34929,35024,35086,35153,35236,35332,35433,35513,35607,35694,35796,36004,36074,36150,36226,36362,36438,36528,36594,36683,36755,36825,36941,37466,37575,37674,37758,37849,37922,38012,38073,38180,38288,38397,38531,38596,38691,38791,38927,38999,39308,39663,41305,41367,41446,41553,41726,42035,42365,42463,42540,43195,43269,43380,43498,43586,43668,43741,43829,43912,44004,44125,44256,44323,44401,44539,44732,44801,44861,44942,45014,45092,45179,45325,45457,45580,45674,45755,45844,45920,46078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "28497", "endColumns": "87", "endOffsets": "28580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1066,1130,1219,1298,1361,1454,1516,1582,1640,1713,1777,1833,1955,2012,2074,2130,2206,2340,2425,2504,2602,2688,2774,2912,2993,3072,3196,3286,3363,3420,3471,3537,3615,3698,3769,3845,3920,3999,4072,4143,4252,4346,4424,4513,4603,4677,4758,4845,4898,4977,5044,5125,5209,5271,5335,5398,5469,5577,5689,5791,5902,5963,6018,6099,6182,6258", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "264,350,432,509,607,701,798,920,1001,1061,1125,1214,1293,1356,1449,1511,1577,1635,1708,1772,1828,1950,2007,2069,2125,2201,2335,2420,2499,2597,2683,2769,2907,2988,3067,3191,3281,3358,3415,3466,3532,3610,3693,3764,3840,3915,3994,4067,4138,4247,4341,4419,4508,4598,4672,4753,4840,4893,4972,5039,5120,5204,5266,5330,5393,5464,5572,5684,5786,5897,5958,6013,6094,6177,6253,6325"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3041,3127,3209,3286,3384,4212,4309,4431,7531,7591,7734,8195,8446,8509,8602,8664,8730,8788,8861,8925,8981,9103,9160,9222,9278,9354,9488,9573,9652,9750,9836,9922,10060,10141,10220,10344,10434,10511,10568,10619,10685,10763,10846,10917,10993,11068,11147,11220,11291,11400,11494,11572,11661,11751,11825,11906,11993,12046,12125,12192,12273,12357,12419,12483,12546,12617,12725,12837,12939,13050,13111,13472,13800,13883,14036", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "314,3122,3204,3281,3379,3473,4304,4426,4507,7586,7650,7818,8269,8504,8597,8659,8725,8783,8856,8920,8976,9098,9155,9217,9273,9349,9483,9568,9647,9745,9831,9917,10055,10136,10215,10339,10429,10506,10563,10614,10680,10758,10841,10912,10988,11063,11142,11215,11286,11395,11489,11567,11656,11746,11820,11901,11988,12041,12120,12187,12268,12352,12414,12478,12541,12612,12720,12832,12934,13045,13106,13161,13548,13878,13954,14103"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3478,3580,3682,3782,3882,3989,4093,14108", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3575,3677,3777,3877,3984,4088,4207,14204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,194,262,346,417,478,550,617,680,748,818,883,946,1020,1084,1151,1214,1290,1355,1441,1518,1598,1684,1791,1874,1925,1978,2058,2122,2187,2257,2358,2443,2540", "endColumns": "62,75,67,83,70,60,71,66,62,67,69,64,62,73,63,66,62,75,64,85,76,79,85,106,82,50,52,79,63,64,69,100,84,96,95", "endOffsets": "113,189,257,341,412,473,545,612,675,743,813,878,941,1015,1079,1146,1209,1285,1350,1436,1513,1593,1679,1786,1869,1920,1973,2053,2117,2182,2252,2353,2438,2535,2631"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15774,16010,16268,16415,16499,16570,17072,17144,17285,17348,17416,17486,17551,17614,17688,17811,17878,18116,19001,19066,20063,20215,20295,24871,24978,26351,27229,27282,27415,28162,28227,29070,29171,29908,30005", "endColumns": "62,75,67,83,70,60,71,66,62,67,69,64,62,73,63,66,62,75,64,85,76,79,85,106,82,50,52,79,63,64,69,100,84,96,95", "endOffsets": "15832,16081,16331,16494,16565,16626,17139,17206,17343,17411,17481,17546,17609,17683,17747,17873,17936,18187,19061,19147,20135,20290,20376,24973,25056,26397,27277,27357,27474,28222,28292,29166,29251,30000,30096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,85", "endOffsets": "136,222"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "46357,46443", "endColumns": "85,85", "endOffsets": "46438,46524"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,144,246,313,380,447,524", "endColumns": "88,101,66,66,66,76,76", "endOffsets": "139,241,308,375,442,519,596"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14209,14298,14400,14467,14534,14601,14678", "endColumns": "88,101,66,66,66,76,76", "endOffsets": "14293,14395,14462,14529,14596,14673,14750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5790", "endColumns": "142", "endOffsets": "5928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7067,7823,7924,8035", "endColumns": "102,100,110,98", "endOffsets": "7165,7919,8030,8129"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,325,423,658,704,774,873,945,1213,1273,1362,1426,1481,1545,1624,1719,2056,2127,2193,2246,2299,2391,2524,2643,2700,2784,2863,2945,3012,3108,3434,3511,3589,3657,3717,3781,3841,3914,4004,4102,4205,4291,4389,4489,4563,4642,4729,4948,5180,5298,5428,5493,6232,6334,6398", "endColumns": "109,159,97,234,45,69,98,71,267,59,88,63,54,63,78,94,336,70,65,52,52,91,132,118,56,83,78,81,66,95,325,76,77,67,59,63,59,72,89,97,102,85,97,99,73,78,86,218,231,117,129,64,738,101,63,54", "endOffsets": "160,320,418,653,699,769,868,940,1208,1268,1357,1421,1476,1540,1619,1714,2051,2122,2188,2241,2294,2386,2519,2638,2695,2779,2858,2940,3007,3103,3429,3506,3584,3652,3712,3776,3836,3909,3999,4097,4200,4286,4384,4484,4558,4637,4724,4943,5175,5293,5423,5488,6227,6329,6393,6448"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20593,20703,20863,21051,22185,22231,22301,22400,22472,22740,22800,24807,25061,25253,25563,25642,26014,26732,26803,27362,28297,28585,28677,28810,28929,28986,29570,29826,30262,30403,30499,30825,30902,31144,31340,31400,31712,32347,32420,32602,32801,34497,36946,37044,37144,37218,37297,39004,39725,39957,40075,40205,40345,41084,41186,43049", "endColumns": "109,159,97,234,45,69,98,71,267,59,88,63,54,63,78,94,336,70,65,52,52,91,132,118,56,83,78,81,66,95,325,76,77,67,59,63,59,72,89,97,102,85,97,99,73,78,86,218,231,117,129,64,738,101,63,54", "endOffsets": "20698,20858,20956,21281,22226,22296,22395,22467,22735,22795,22884,24866,25111,25312,25637,25732,26346,26798,26864,27410,28345,28672,28805,28924,28981,29065,29644,29903,30324,30494,30820,30897,30975,31207,31395,31459,31767,32415,32505,32695,32899,34578,37039,37139,37213,37292,37379,39218,39952,40070,40200,40265,41079,41181,41245,43099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,192,254,335,402,476,535,612,682,750,811", "endColumns": "75,60,61,80,66,73,58,76,69,67,60,66", "endOffsets": "126,187,249,330,397,471,530,607,677,745,806,873"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15837,16631,16774,16836,16917,17211,17752,17941,18192,18508,18858,19152", "endColumns": "75,60,61,80,66,73,58,76,69,67,60,66", "endOffsets": "15908,16687,16831,16912,16979,17280,17806,18013,18257,18571,18914,19214"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,444,568,675,838,961,1080,1182,1356,1458,1623,1745,1904,2082,2146,2205", "endColumns": "103,146,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,443,567,674,837,960,1079,1181,1355,1457,1622,1744,1903,2081,2145,2204,2279"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4769,4877,5028,5156,5267,5434,5561,5684,5933,6111,6217,6386,6512,6675,6857,6925,6988", "endColumns": "107,150,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "4872,5023,5151,5262,5429,5556,5679,5785,6106,6212,6381,6507,6670,6852,6920,6983,7062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,333,411,469,533,588,663,743,824,921,1018,1103,1182,1264,1352,1450,1541,1608,1696,1785,1871,1978,2060,2148,2222,2317,2390,2481,2569,2653,2735,2835,2904,2979,3089,3191,3263,3328,4090,4828,4905,5022,5123,5178,5281,5379,5444,5533,5622,5679,5760,5812,5892,6006,6077,6150,6217,6283,6332,6415,6512,6596,6643,6692,6762,6820,6886,7078,7245,7375,7440,7522,7607,7707,7791,7884,7959,8045,8119,8222,8309,8404,8457,8597,8651,8708,8783,8855,8930,8997,9067,9160,9235,9287", "endColumns": "68,77,61,68,77,57,63,54,74,79,80,96,96,84,78,81,87,97,90,66,87,88,85,106,81,87,73,94,72,90,87,83,81,99,68,74,109,101,71,64,761,737,76,116,100,54,102,97,64,88,88,56,80,51,79,113,70,72,66,65,48,82,96,83,46,48,69,57,65,191,166,129,64,81,84,99,83,92,74,85,73,102,86,94,52,139,53,56,74,71,74,66,69,92,74,51,77", "endOffsets": "119,197,259,328,406,464,528,583,658,738,819,916,1013,1098,1177,1259,1347,1445,1536,1603,1691,1780,1866,1973,2055,2143,2217,2312,2385,2476,2564,2648,2730,2830,2899,2974,3084,3186,3258,3323,4085,4823,4900,5017,5118,5173,5276,5374,5439,5528,5617,5674,5755,5807,5887,6001,6072,6145,6212,6278,6327,6410,6507,6591,6638,6687,6757,6815,6881,7073,7240,7370,7435,7517,7602,7702,7786,7879,7954,8040,8114,8217,8304,8399,8452,8592,8646,8703,8778,8850,8925,8992,9062,9155,9230,9282,9360"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14755,14824,14902,14964,15033,15111,15169,15483,15538,15613,15693,15913,16086,16183,16336,16692,16984,18018,18262,18353,18420,18576,18665,18751,18919,19219,19307,19381,19476,19549,19640,19728,19812,19894,19994,20140,20381,20491,21286,21358,21423,23137,23875,23952,24069,24170,24225,24328,24426,24491,24580,24669,24726,25317,25369,25449,25737,25808,25881,25948,26492,26869,26952,27049,27133,27180,27479,27549,27607,27673,27865,28032,28350,28415,29385,29470,29649,29733,30101,30176,30329,30980,31530,31617,39313,39366,39506,39668,40270,42545,42617,42692,42759,42829,42922,42997,44406", "endColumns": "68,77,61,68,77,57,63,54,74,79,80,96,96,84,78,81,87,97,90,66,87,88,85,106,81,87,73,94,72,90,87,83,81,99,68,74,109,101,71,64,761,737,76,116,100,54,102,97,64,88,88,56,80,51,79,113,70,72,66,65,48,82,96,83,46,48,69,57,65,191,166,129,64,81,84,99,83,92,74,85,73,102,86,94,52,139,53,56,74,71,74,66,69,92,74,51,77", "endOffsets": "14819,14897,14959,15028,15106,15164,15228,15533,15608,15688,15769,16005,16178,16263,16410,16769,17067,18111,18348,18415,18503,18660,18746,18853,18996,19302,19376,19471,19544,19635,19723,19807,19889,19989,20058,20210,20486,20588,21353,21418,22180,23870,23947,24064,24165,24220,24323,24421,24486,24575,24664,24721,24802,25364,25444,25558,25803,25876,25943,26009,26536,26947,27044,27128,27175,27224,27544,27602,27668,27860,28027,28157,28410,28492,29465,29565,29728,29821,30171,30257,30398,31078,31612,31707,39361,39501,39555,39720,40340,42612,42687,42754,42824,42917,42992,43044,44479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,437,542,649,734,838,958,1036,1112,1204,1298,1393,1487,1587,1681,1777,1872,1964,2056,2138,2249,2352,2451,2566,2680,2783,2938,13717", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "432,537,644,729,833,953,1031,1107,1199,1293,1388,1482,1582,1676,1772,1867,1959,2051,2133,2244,2347,2446,2561,2675,2778,2933,3036,13795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,974,1039,1120,1203,1280,1364,1434", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,969,1034,1115,1198,1275,1359,1429,1549"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4512,4604,7170,7267,7446,8274,8350,13166,13253,13342,13407,13553,13634,13959,46083,46167,46237", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "4599,4682,7262,7361,7526,8345,8441,13248,13337,13402,13467,13629,13712,14031,46162,46232,46352"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,137,217", "endColumns": "81,79,78", "endOffsets": "132,212,291"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4687,7366,7655", "endColumns": "81,79,78", "endOffsets": "4764,7441,7729"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,71", "endOffsets": "258,330"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "8134,46529", "endColumns": "60,75", "endOffsets": "8190,46600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,223,305,395,482,563,643,780,870,971,1061,1190,1251,1379,1445,1645,1864,2020,2112,2213,2306,2400,2581,2678,2777,3030,3142,3280,3488,3714,3806,3935,4157,4252,4314,4381,4464,4560,4661,4741,4835,4922,5024,5232,5302,5378,5454,5590,5666,5756,5822,5911,5983,6053,6169,6256,6365,6464,6548,6639,6712,6802,6863,6970,7078,7187,7321,7386,7481,7581,7717,7789,7879,7987,8047,8109,8188,8295,8468,8777,9107,9205,9282,9378,9452,9563,9681,9769,9851,9924,10012,10095,10187,10308,10439,10506,10584,10644,10837,10906,10966,11047,11119,11197,11284,11430,11562,11685,11779,11860,11949,12025", "endColumns": "74,92,81,89,86,80,79,136,89,100,89,128,60,127,65,199,218,155,91,100,92,93,180,96,98,252,111,137,207,225,91,128,221,94,61,66,82,95,100,79,93,86,101,207,69,75,75,135,75,89,65,88,71,69,115,86,108,98,83,90,72,89,60,106,107,108,133,64,94,99,135,71,89,107,59,61,78,106,172,308,329,97,76,95,73,110,117,87,81,72,87,82,91,120,130,66,77,59,192,68,59,80,71,77,86,145,131,122,93,80,88,75,157", "endOffsets": "125,218,300,390,477,558,638,775,865,966,1056,1185,1246,1374,1440,1640,1859,2015,2107,2208,2301,2395,2576,2673,2772,3025,3137,3275,3483,3709,3801,3930,4152,4247,4309,4376,4459,4555,4656,4736,4830,4917,5019,5227,5297,5373,5449,5585,5661,5751,5817,5906,5978,6048,6164,6251,6360,6459,6543,6634,6707,6797,6858,6965,7073,7182,7316,7381,7476,7576,7712,7784,7874,7982,8042,8104,8183,8290,8463,8772,9102,9200,9277,9373,9447,9558,9676,9764,9846,9919,10007,10090,10182,10303,10434,10501,10579,10639,10832,10901,10961,11042,11114,11192,11279,11425,11557,11680,11774,11855,11944,12020,12178"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15233,15308,15401,20961,22889,22976,23057,25116,26402,26541,26642,29256,31083,31212,31464,31772,31972,32191,32510,32700,32904,32997,33091,33272,33369,33468,33721,33833,33971,34179,34405,34583,34712,34934,35029,35091,35158,35241,35337,35438,35518,35612,35699,35801,36009,36079,36155,36231,36367,36443,36533,36599,36688,36760,36830,37384,37471,37580,37679,37763,37854,37927,38017,38078,38185,38293,38402,38536,38601,38696,38796,38932,39223,39560,41250,41310,41372,41451,41558,41731,42040,42370,42468,43104,43200,43274,43385,43503,43591,43673,43746,43834,43917,44009,44130,44261,44328,44484,44544,44737,44806,44866,44947,45019,45097,45184,45330,45462,45585,45679,45760,45849,45925", "endColumns": "74,92,81,89,86,80,79,136,89,100,89,128,60,127,65,199,218,155,91,100,92,93,180,96,98,252,111,137,207,225,91,128,221,94,61,66,82,95,100,79,93,86,101,207,69,75,75,135,75,89,65,88,71,69,115,86,108,98,83,90,72,89,60,106,107,108,133,64,94,99,135,71,89,107,59,61,78,106,172,308,329,97,76,95,73,110,117,87,81,72,87,82,91,120,130,66,77,59,192,68,59,80,71,77,86,145,131,122,93,80,88,75,157", "endOffsets": "15303,15396,15478,21046,22971,23052,23132,25248,26487,26637,26727,29380,31139,31335,31525,31967,32186,32342,32597,32796,32992,33086,33267,33364,33463,33716,33828,33966,34174,34400,34492,34707,34929,35024,35086,35153,35236,35332,35433,35513,35607,35694,35796,36004,36074,36150,36226,36362,36438,36528,36594,36683,36755,36825,36941,37466,37575,37674,37758,37849,37922,38012,38073,38180,38288,38397,38531,38596,38691,38791,38927,38999,39308,39663,41305,41367,41446,41553,41726,42035,42365,42463,42540,43195,43269,43380,43498,43586,43668,43741,43829,43912,44004,44125,44256,44323,44401,44539,44732,44801,44861,44942,45014,45092,45179,45325,45457,45580,45674,45755,45844,45920,46078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "28497", "endColumns": "87", "endOffsets": "28580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1066,1130,1219,1298,1361,1454,1516,1582,1640,1713,1777,1833,1955,2012,2074,2130,2206,2340,2425,2504,2602,2688,2774,2912,2993,3072,3196,3286,3363,3420,3471,3537,3615,3698,3769,3845,3920,3999,4072,4143,4252,4346,4424,4513,4603,4677,4758,4845,4898,4977,5044,5125,5209,5271,5335,5398,5469,5577,5689,5791,5902,5963,6018,6099,6182,6258", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "264,350,432,509,607,701,798,920,1001,1061,1125,1214,1293,1356,1449,1511,1577,1635,1708,1772,1828,1950,2007,2069,2125,2201,2335,2420,2499,2597,2683,2769,2907,2988,3067,3191,3281,3358,3415,3466,3532,3610,3693,3764,3840,3915,3994,4067,4138,4247,4341,4419,4508,4598,4672,4753,4840,4893,4972,5039,5120,5204,5266,5330,5393,5464,5572,5684,5786,5897,5958,6013,6094,6177,6253,6325"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3041,3127,3209,3286,3384,4212,4309,4431,7531,7591,7734,8195,8446,8509,8602,8664,8730,8788,8861,8925,8981,9103,9160,9222,9278,9354,9488,9573,9652,9750,9836,9922,10060,10141,10220,10344,10434,10511,10568,10619,10685,10763,10846,10917,10993,11068,11147,11220,11291,11400,11494,11572,11661,11751,11825,11906,11993,12046,12125,12192,12273,12357,12419,12483,12546,12617,12725,12837,12939,13050,13111,13472,13800,13883,14036", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "314,3122,3204,3281,3379,3473,4304,4426,4507,7586,7650,7818,8269,8504,8597,8659,8725,8783,8856,8920,8976,9098,9155,9217,9273,9349,9483,9568,9647,9745,9831,9917,10055,10136,10215,10339,10429,10506,10563,10614,10680,10758,10841,10912,10988,11063,11142,11215,11286,11395,11489,11567,11656,11746,11820,11901,11988,12041,12120,12187,12268,12352,12414,12478,12541,12612,12720,12832,12934,13045,13106,13161,13548,13878,13954,14103"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3478,3580,3682,3782,3882,3989,4093,14108", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3575,3677,3777,3877,3984,4088,4207,14204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,194,262,346,417,478,550,617,680,748,818,883,946,1020,1084,1151,1214,1290,1355,1441,1518,1598,1684,1791,1874,1925,1978,2058,2122,2187,2257,2358,2443,2540", "endColumns": "62,75,67,83,70,60,71,66,62,67,69,64,62,73,63,66,62,75,64,85,76,79,85,106,82,50,52,79,63,64,69,100,84,96,95", "endOffsets": "113,189,257,341,412,473,545,612,675,743,813,878,941,1015,1079,1146,1209,1285,1350,1436,1513,1593,1679,1786,1869,1920,1973,2053,2117,2182,2252,2353,2438,2535,2631"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15774,16010,16268,16415,16499,16570,17072,17144,17285,17348,17416,17486,17551,17614,17688,17811,17878,18116,19001,19066,20063,20215,20295,24871,24978,26351,27229,27282,27415,28162,28227,29070,29171,29908,30005", "endColumns": "62,75,67,83,70,60,71,66,62,67,69,64,62,73,63,66,62,75,64,85,76,79,85,106,82,50,52,79,63,64,69,100,84,96,95", "endOffsets": "15832,16081,16331,16494,16565,16626,17139,17206,17343,17411,17481,17546,17609,17683,17747,17873,17936,18187,19061,19147,20135,20290,20376,24973,25056,26397,27277,27357,27474,28222,28292,29166,29251,30000,30096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,85", "endOffsets": "136,222"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "46357,46443", "endColumns": "85,85", "endOffsets": "46438,46524"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,144,246,313,380,447,524", "endColumns": "88,101,66,66,66,76,76", "endOffsets": "139,241,308,375,442,519,596"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14209,14298,14400,14467,14534,14601,14678", "endColumns": "88,101,66,66,66,76,76", "endOffsets": "14293,14395,14462,14529,14596,14673,14750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5790", "endColumns": "142", "endOffsets": "5928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7067,7823,7924,8035", "endColumns": "102,100,110,98", "endOffsets": "7165,7919,8030,8129"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,325,423,658,704,774,873,945,1213,1273,1362,1426,1481,1545,1624,1719,2056,2127,2193,2246,2299,2391,2524,2643,2700,2784,2863,2945,3012,3108,3434,3511,3589,3657,3717,3781,3841,3914,4004,4102,4205,4291,4389,4489,4563,4642,4729,4948,5180,5298,5428,5493,6232,6334,6398", "endColumns": "109,159,97,234,45,69,98,71,267,59,88,63,54,63,78,94,336,70,65,52,52,91,132,118,56,83,78,81,66,95,325,76,77,67,59,63,59,72,89,97,102,85,97,99,73,78,86,218,231,117,129,64,738,101,63,54", "endOffsets": "160,320,418,653,699,769,868,940,1208,1268,1357,1421,1476,1540,1619,1714,2051,2122,2188,2241,2294,2386,2519,2638,2695,2779,2858,2940,3007,3103,3429,3506,3584,3652,3712,3776,3836,3909,3999,4097,4200,4286,4384,4484,4558,4637,4724,4943,5175,5293,5423,5488,6227,6329,6393,6448"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20593,20703,20863,21051,22185,22231,22301,22400,22472,22740,22800,24807,25061,25253,25563,25642,26014,26732,26803,27362,28297,28585,28677,28810,28929,28986,29570,29826,30262,30403,30499,30825,30902,31144,31340,31400,31712,32347,32420,32602,32801,34497,36946,37044,37144,37218,37297,39004,39725,39957,40075,40205,40345,41084,41186,43049", "endColumns": "109,159,97,234,45,69,98,71,267,59,88,63,54,63,78,94,336,70,65,52,52,91,132,118,56,83,78,81,66,95,325,76,77,67,59,63,59,72,89,97,102,85,97,99,73,78,86,218,231,117,129,64,738,101,63,54", "endOffsets": "20698,20858,20956,21281,22226,22296,22395,22467,22735,22795,22884,24866,25111,25312,25637,25732,26346,26798,26864,27410,28345,28672,28805,28924,28981,29065,29644,29903,30324,30494,30820,30897,30975,31207,31395,31459,31767,32415,32505,32695,32899,34578,37039,37139,37213,37292,37379,39218,39952,40070,40200,40265,41079,41181,41245,43099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,192,254,335,402,476,535,612,682,750,811", "endColumns": "75,60,61,80,66,73,58,76,69,67,60,66", "endOffsets": "126,187,249,330,397,471,530,607,677,745,806,873"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15837,16631,16774,16836,16917,17211,17752,17941,18192,18508,18858,19152", "endColumns": "75,60,61,80,66,73,58,76,69,67,60,66", "endOffsets": "15908,16687,16831,16912,16979,17280,17806,18013,18257,18571,18914,19214"}}]}]}
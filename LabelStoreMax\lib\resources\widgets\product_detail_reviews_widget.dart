//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '/resources/pages/product_reviews_page.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/product_detail_review_tile_widget.dart';
import '/app/services/woocommerce_service.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '/app/models/app_config.dart';

class ProductDetailReviewsWidget extends StatefulWidget {
  const ProductDetailReviewsWidget(
      {super.key, required this.product, required this.appConfig});
  final MyProduct? product;
  final AppConfig? appConfig;

  @override
  createState() => _ProductDetailReviewsWidgetState();
}

class _ProductDetailReviewsWidgetState
    extends NyState<ProductDetailReviewsWidget> {
  bool _ratingExpanded = false;
  bool _isSubmittingReview = false;
  double _userRating = 0.0;
  final TextEditingController _reviewController = TextEditingController();

  @override
  void dispose() {
    _reviewController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Note: MyProduct doesn't have reviewsAllowed field, so we check appConfig only
    if (widget.appConfig?.showProductReviews == false) {
      return SizedBox.shrink();
    }

    return Row(
      children: <Widget>[
        Expanded(
            child: ExpansionTile(
          textColor: ThemeColor.get(context).primaryAccent,
          iconColor: ThemeColor.get(context).primaryAccent,
          tilePadding: EdgeInsets.symmetric(horizontal: 16),
          childrenPadding: EdgeInsets.all(0),
          title: AutoSizeText(
            "${trans("Reviews")}",
            maxLines: 1,
          ),
          onExpansionChanged: (value) {
            setState(() {
              _ratingExpanded = value;
            });
          },
          trailing: SizedBox(
            width: MediaQuery.of(context).size.width / 2,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                RatingBarIndicator(
                  rating: 0.0, // MyProduct doesn't have averageRating, will be calculated from reviews
                  itemBuilder: (context, index) => Icon(
                    Icons.star,
                    color: Colors.amber,
                  ),
                  itemCount: 5,
                  itemSize: 25.0,
                  direction: Axis.horizontal,
                ),
                Icon(
                    _ratingExpanded
                        ? Icons.keyboard_arrow_down_rounded
                        : Icons.keyboard_arrow_up_rounded,
                    size: 30)
              ],
            ),
          ),
          initiallyExpanded: false,
          children: [
            if (_ratingExpanded == true)
              NyFutureBuilder<List<Map<String, dynamic>>>(
                future: fetchReviews(),
                child: (context, reviews) {
                  if (reviews == null) {
                    return ListTile(
                      title: Text(
                        trans('There are no reviews yet.'),
                      ),
                    );
                  }
                  int reviewsCount = reviews.length;
                  List<Widget> childrenWidgets = [];
                  List<ProductDetailReviewTileWidget> children = reviews
                      .map((review) =>
                          ProductDetailReviewTileWidget(productReview: review))
                      .toList();
                  childrenWidgets.addAll(children);

                  if (reviewsCount >= 5) {
                    childrenWidgets.add(
                      ListTile(
                          contentPadding: EdgeInsets.symmetric(horizontal: 16),
                          title: Text(
                            trans('See More Reviews'),
                          ),
                          onTap: () => routeTo(ProductReviewsPage.path,
                              data: widget.product)),
                    );
                  }
                  return ListView(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.all(0),
                    children: reviews.isEmpty
                        ? [
                            ListTile(
                              title: Text(
                                trans('There are no reviews yet.'),
                              ),
                            )
                          ]
                        : childrenWidgets,
                  );
                },
                loadingStyle: LoadingStyle.normal(
                    child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: CupertinoActivityIndicator(),
                )),
              ),

            // Review Submission Form for logged-in users
            FutureBuilder<bool>(
              future: isUserLoggedIn(),
              builder: (context, snapshot) {
                if (snapshot.data == true) {
                  return _buildReviewSubmissionForm();
                }
                return SizedBox.shrink();
              },
            ),
          ],
        )),
      ],
    );
  }

  Future<List<Map<String, dynamic>>> fetchReviews() async {
    if (widget.product?.id == null) return [];

    try {
      print('📝 Fetching reviews for product ${widget.product!.id}...');
      print('📝 Product type: ${widget.product!.type}');
      print('📝 Product name: ${widget.product!.name}');

      final wooCommerceService = WooCommerceService();
      List<WooProductReview> reviews = await wooCommerceService.getProductReviews(
        perPage: 5,
        product: [widget.product!.id],
      );

      // Filter for approved reviews only
      List<WooProductReview> approvedReviews = reviews
          .where((review) => review.status == WooProductReviewStatus.approved)
          .toList();

      print('✅ Found ${approvedReviews.length} approved reviews');

      // Convert WooProductReview objects to Map<String, dynamic> format
      // that the existing UI components expect
      List<Map<String, dynamic>> reviewMaps = approvedReviews.map((review) {
        return {
          'id': review.id,
          'review': review.review ?? '',
          'rating': review.rating ?? 0,
          'reviewer': review.reviewer ?? 'Anonymous',
          'reviewer_email': review.reviewerEmail ?? '',
          'date_created': review.dateCreated?.toIso8601String() ?? '',
          'verified': review.verified ?? false,
          'status': review.status?.name ?? 'approved',
        };
      }).toList();

      return reviewMaps;

    } catch (e) {
      print('❌ Error fetching product reviews: $e');
      print('❌ Error type: ${e.runtimeType}');
      print('❌ Error details: ${e.toString()}');

      // Check if it's a 400 error specifically
      if (e.toString().contains('400') || e.toString().contains('bad response')) {
        print('❌ 400 Bad Request detected - this might indicate:');
        print('   1. Reviews are not enabled for this WooCommerce store');
        print('   2. The product ID is invalid');
        print('   3. The reviews endpoint requires different parameters');
        print('   4. Authentication issues with the reviews endpoint');

        // Try a fallback approach - fetch all reviews without product filter
        try {
          print('🔄 Attempting fallback: fetching all reviews...');
          final wooCommerceService = WooCommerceService();
          List<WooProductReview> allReviews = await wooCommerceService.getProductReviews(
            perPage: 50, // Get more reviews to filter locally
            // No product filter - get all reviews
          );

          // Filter locally for this product
          List<WooProductReview> productReviews = allReviews
              .where((review) => review.productId == widget.product!.id)
              .where((review) => review.status == WooProductReviewStatus.approved)
              .toList();

          print('✅ Fallback successful: Found ${productReviews.length} reviews for product ${widget.product!.id}');

          // Convert to expected format
          List<Map<String, dynamic>> reviewMaps = productReviews.map((review) {
            return {
              'id': review.id,
              'review': review.review ?? '',
              'rating': review.rating ?? 0,
              'reviewer': review.reviewer ?? 'Anonymous',
              'reviewer_email': review.reviewerEmail ?? '',
              'date_created': review.dateCreated?.toIso8601String() ?? '',
              'verified': review.verified ?? false,
              'status': review.status?.name ?? 'approved',
            };
          }).toList();

          return reviewMaps;

        } catch (fallbackError) {
          print('❌ Fallback also failed: $fallbackError');
          print('💡 Reviews functionality may not be available for this store');
        }
      }

      // Return empty list instead of null to avoid UI issues
      return <Map<String, dynamic>>[];
    }
  }

  Widget _buildReviewSubmissionForm() {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.rate_review_outlined,
                color: Color(0xFFB76E79),
                size: 24,
              ),
              SizedBox(width: 8),
              Text(
                'اكتب مراجعة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.headlineSmall?.color,
                ),
              ),
            ],
          ),

          SizedBox(height: 16),

          // Star Rating Selector
          Text(
            'التقييم',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
          SizedBox(height: 8),
          RatingBar.builder(
            initialRating: _userRating,
            minRating: 1,
            direction: Axis.horizontal,
            allowHalfRating: false,
            itemCount: 5,
            itemPadding: EdgeInsets.symmetric(horizontal: 4.0),
            itemBuilder: (context, _) => Icon(
              Icons.star,
              color: Colors.amber,
            ),
            onRatingUpdate: (rating) {
              setState(() {
                _userRating = rating;
              });
            },
          ),

          SizedBox(height: 16),

          // Review Text Area
          Text(
            'المراجعة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
          SizedBox(height: 8),
          TextField(
            controller: _reviewController,
            maxLines: 4,
            textDirection: TextDirection.rtl,
            decoration: InputDecoration(
              hintText: 'شاركنا رأيك في هذا المنتج...',
              hintStyle: TextStyle(color: Colors.grey[500]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Color(0xFFB76E79), width: 2),
              ),
              contentPadding: EdgeInsets.all(12),
            ),
          ),

          SizedBox(height: 16),

          // Submit Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isSubmittingReview ? null : _submitReview,
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFFB76E79),
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isSubmittingReview
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('جاري الإرسال...'),
                      ],
                    )
                  : Text(
                      'إرسال المراجعة',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _submitReview() async {
    // Validate input
    if (_userRating == 0) {
      showToast(
        title: "تقييم مطلوب",
        description: "يرجى اختيار تقييم للمنتج",
        style: ToastNotificationStyleType.warning,
      );
      return;
    }

    if (_reviewController.text.trim().isEmpty) {
      showToast(
        title: "مراجعة مطلوبة",
        description: "يرجى كتابة مراجعة للمنتج",
        style: ToastNotificationStyleType.warning,
      );
      return;
    }

    if (_reviewController.text.trim().length < 10) {
      showToast(
        title: "مراجعة قصيرة",
        description: "يرجى كتابة مراجعة أطول (على الأقل 10 أحرف)",
        style: ToastNotificationStyleType.warning,
      );
      return;
    }

    setState(() {
      _isSubmittingReview = true;
    });

    try {
      // Get current user information
      WooCustomer? currentUser = await getCurrentUser();

      if (currentUser == null) {
        showToast(
          title: "خطأ في المصادقة",
          description: "يرجى تسجيل الدخول أولاً",
          style: ToastNotificationStyleType.danger,
        );
        return;
      }

      // Create review object
      WooProductReview reviewToCreate = WooProductReview(
        productId: widget.product!.id,
        review: _reviewController.text.trim(),
        rating: _userRating.toInt(),
        reviewer: '${currentUser.firstName ?? ''} ${currentUser.lastName ?? ''}'.trim(),
        reviewerEmail: currentUser.email ?? '',
        verified: true, // Mark as verified since user is logged in
        status: WooProductReviewStatus.approved, // Set to approved for immediate display
      );

      print('📝 Submitting review for product ${widget.product!.id}...');
      print('📝 Rating: ${_userRating.toInt()}');
      print('📝 Review: ${_reviewController.text.trim()}');
      print('📝 Reviewer: ${reviewToCreate.reviewer}');

      // Submit review via WooCommerce API
      final wooCommerceService = WooCommerceService();
      WooProductReview submittedReview = await wooCommerceService.createProductReview(
        widget.product!.id,
        reviewToCreate,
      );

      print('✅ Review submitted successfully with ID: ${submittedReview.id}');

      // Show success message
      showToast(
        title: "تم إرسال مراجعتك بنجاح",
        description: "شكراً لك على مراجعة المنتج",
        style: ToastNotificationStyleType.success,
      );

      // Clear form
      setState(() {
        _userRating = 0.0;
        _reviewController.clear();
        _isSubmittingReview = false;
      });

      // Refresh the reviews list by calling setState on parent
      setState(() {});

    } catch (e) {
      print('❌ Error submitting review: $e');

      showToast(
        title: "خطأ في إرسال المراجعة",
        description: "حدث خطأ أثناء إرسال مراجعتك. يرجى المحاولة مرة أخرى",
        style: ToastNotificationStyleType.danger,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isSubmittingReview = false;
        });
      }
    }
  }
}

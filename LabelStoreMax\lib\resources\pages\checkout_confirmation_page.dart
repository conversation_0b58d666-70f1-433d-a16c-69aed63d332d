//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import '/resources/widgets/checkout_customer_note_widget.dart';
import '/app/models/cart.dart';
import '/app/models/checkout_session.dart';
import '/app/models/payment_type.dart';

import '/bootstrap/helpers.dart';
import '/bootstrap/app_helper.dart';
import '/resources/widgets/app_loader_widget.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/checkout_coupon_amount_widget.dart';
import '/resources/widgets/checkout_payment_type_widget.dart';
import '/resources/widgets/checkout_select_coupon_widget.dart';
import '/resources/widgets/checkout_shipping_type_widget.dart';
import '/resources/widgets/checkout_store_heading_widget.dart';
import '/resources/widgets/checkout_user_details_widget.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';


class CheckoutConfirmationPage extends NyStatefulWidget {
  static RouteView path = ("/checkout", (_) => CheckoutConfirmationPage());

  CheckoutConfirmationPage({super.key})
      : super(child: () => _CheckoutConfirmationPageState());
}

class _CheckoutConfirmationPageState extends NyPage<CheckoutConfirmationPage> {
  bool _showFullLoader = false;


  @override
  bool get stateManaged => true;

  @override
  get init => () async {
        CheckoutSession.getInstance.coupon = null;

        // Ensure checkout session is properly initialized
        if (CheckoutSession.getInstance.billingDetails == null) {
          CheckoutSession.getInstance.initSession();
        }

        List<PaymentType?> paymentTypes = await getPaymentTypes();

        if (CheckoutSession.getInstance.paymentType == null &&
            paymentTypes.isNotEmpty) {
          CheckoutSession.getInstance.paymentType = paymentTypes.firstWhere(
              (paymentType) => paymentType?.id == 20,
              orElse: () => paymentTypes.first);
        }
      };

  @override
  stateUpdated(dynamic data) async {
    super.stateUpdated(data);
    if (data == null) return;
    if (data['reloadState'] != null) {
      reloadState(showLoader: data['reloadState']);
    }
    if (data['refresh'] != null) {
      setState(() {});
    }
    if (data['toast'] != null) {
      showToast(
        title: data['toast']['title'],
        description: data['toast']['description'],
      );
    }
  }

  reloadState({required bool showLoader}) {
    setState(() {
      _showFullLoader = showLoader;
    });
  }



  @override
  Widget view(BuildContext context) {
    CheckoutSession checkoutSession = CheckoutSession.getInstance;

    if (_showFullLoader == true) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              AppLoaderWidget(),
              Padding(
                padding: const EdgeInsets.only(top: 15),
                child: Text(
                  "${trans("One moment")}...",
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              )
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(trans("Checkout")).onTap(() {
              StateAction.refreshPage(CheckoutConfirmationPage.path,
                  setState: () {});
            }),
            Text(getEnv('APP_NAME', defaultValue: 'Velvete')).bodySmall(),
          ],
        ),
        centerTitle: false,
        leading: Container(
          margin: EdgeInsets.only(left: 0),
          child: IconButton(
            icon: Icon(Icons.arrow_back_ios),
            onPressed: () {
              CheckoutSession.getInstance.coupon = null;
              Navigator.pop(context);
            },
          ),
        ),
      ),
      resizeToAvoidBottomInset: false,
      body: SafeAreaWidget(
        child: Column(
          children: <Widget>[
            Expanded(
              child: ListView(
                padding: EdgeInsets.symmetric(horizontal: 8),
                children: [
                  CheckoutStoreHeadingWidget(),
                  CheckoutUserDetailsWidget(
                    context: context,
                    checkoutSession: checkoutSession,
                  ),
                  CheckoutPaymentTypeWidget(
                    context: context,
                    checkoutSession: checkoutSession,
                  ),
                  CheckoutShippingTypeWidget(
                    context: context,
                    checkoutSession: checkoutSession,
                    // wooSignalApp: _appConfig, // Replaced with AppConfig
                  ),
                  // Check coupon enabled from app config - coupons are enabled by default
                  if (true) // Coupons are enabled
                    CheckoutSelectCouponWidget(
                      context: context,
                      checkoutSession: checkoutSession,
                    ),
                  CheckoutCustomerNote(checkoutSession: checkoutSession),
                  Container(
                    decoration: BoxDecoration(
                      boxShadow: wsBoxShadow(),
                      color: Colors.white,
                    ),
                    padding: EdgeInsets.symmetric(vertical: 16),
                    margin: EdgeInsets.only(top: 20),
                    child: Column(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 8),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Icon(Icons.receipt),
                              SizedBox(width: 8),
                              Expanded(
                                child: Text(trans("Order Summary")).fontWeightBold(),
                              ),
                            ],
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Padding(padding: EdgeInsets.only(top: 16)),
                            CheckoutSubtotal(
                              title: trans("Subtotal"),
                            ),
                            CheckoutCouponAmountWidget(
                                checkoutSession: checkoutSession),
                            // Check shipping settings from app config - MASTER'S ORDER: Use reactive widget
                            if (!(AppHelper.instance.appConfig?.disableShipping ?? false))
                              CheckoutShippingTotal(checkoutSession: checkoutSession),

                            Padding(
                                padding:
                                    EdgeInsets.only(top: 8, left: 8, right: 8)),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 8),
                              child: RichText(
                                textAlign: TextAlign.left,
                                text: TextSpan(
                                  text:
                                      '${trans('By completing this order, I agree to all')} ',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall!
                                      .copyWith(
                                        fontSize: 12,
                                      ),
                                  children: <TextSpan>[
                                    TextSpan(
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = _openTermsLink,
                                      text: trans("Terms and conditions")
                                          .toLowerCase(),
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall!
                                          .copyWith(
                                            color: ThemeColor.get(context)
                                                .primaryAccent,
                                            fontSize: 12,
                                          ),
                                    ),
                                    TextSpan(
                                      text: ".",
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall!
                                          .copyWith(
                                            color: Colors.black87,
                                            fontSize: 12,
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 16), // Add spacing between order summary and checkout button
                ],
              ),
            ),
            // Bottom section with checkout button - fixed at bottom
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 4,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CheckoutGrandTotal(checkoutSession: checkoutSession),
                  SizedBox(height: 16),
                  PrimaryButton(
                    isLoading: isLocked('payment'),
                    title: trans("CHECKOUT"),
                    action: () async {
                      lockRelease('payment', perform: () async {
                        await _handleCheckout();
                      });
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  _openTermsLink() =>
      openBrowserTab(url: "https://velvete.ly/terms-conditions");

  _handleCheckout() async {
    CheckoutSession checkoutSession = CheckoutSession.getInstance;
    if (checkoutSession.billingDetails!.billingAddress == null) {
      showToast(
        title: trans("Oops"),
        description:
            trans("Please select add your billing/shipping address to proceed"),
        style: ToastNotificationStyleType.warning,
        icon: Icons.local_shipping,
      );
      return;
    }

    if (checkoutSession.billingDetails?.billingAddress?.hasMissingFields() ??
        true) {
      showToast(
        title: trans("Oops"),
        description: trans("Your billing/shipping details are incomplete"),
        style: ToastNotificationStyleType.warning,
        icon: Icons.local_shipping,
      );
      return;
    }

    // Check shipping settings from app config
    if (!(AppHelper.instance.appConfig?.disableShipping ?? false) &&
        checkoutSession.shippingType == null) {
      showToast(
        title: trans("Oops"),
        description: trans("Please select a delivery method to proceed"),
        style: ToastNotificationStyleType.warning,
        icon: Icons.local_shipping,
      );
      return;
    }

    if (checkoutSession.paymentType == null) {
      showToast(
        title: trans("Oops"),
        description: trans("Please select a payment method to proceed"),
        style: ToastNotificationStyleType.warning,
        icon: Icons.payment,
      );
      return;
    }

    // Check shipping settings from app config
    if (!(AppHelper.instance.appConfig?.disableShipping ?? false) &&
        checkoutSession.shippingType?.minimumValue != null) {
      String total = await Cart.getInstance.getTotal();

      double doubleTotal = double.parse(total);

      double doubleMinimumValue =
          double.parse(checkoutSession.shippingType!.minimumValue!);

      if (doubleTotal < doubleMinimumValue) {
        showToast(
            title: trans("Sorry"),
            description:
                "${trans("Spend a minimum of")} ${formatDoubleCurrency(total: doubleMinimumValue)} ${trans("for")} ${checkoutSession.shippingType!.getTitle()}",
            style: ToastNotificationStyleType.info,
            duration: Duration(seconds: 3));
        return;
      }
    }

    // Check app status from AppConfig
    if (AppHelper.instance.appConfig?.appStatus != "active") {
      showToast(
        title: trans("Service Unavailable"),
        description: trans("The service is temporarily unavailable. Please try again later."),
        style: ToastNotificationStyleType.warning,
      );
      return;
    }

    try {
      if (!mounted) return;
      await checkoutSession.paymentType!.pay(context);
    } on Exception catch (e) {
      printError(e.toString());
    }
  }
}

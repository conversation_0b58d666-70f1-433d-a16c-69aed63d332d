[{"merged": "com.flutter.stripe.stripe_android-release-91:/animator/mtrl_extended_fab_show_motion_spec.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/mtrl_extended_fab_show_motion_spec.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/m3_chip_state_list_anim.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/m3_chip_state_list_anim.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/mtrl_btn_state_list_anim.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/mtrl_btn_state_list_anim.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/m3_extended_fab_change_size_expand_motion_spec.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/m3_extended_fab_change_size_expand_motion_spec.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/mtrl_extended_fab_change_size_expand_motion_spec.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/mtrl_extended_fab_change_size_expand_motion_spec.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/fragment_open_enter.xml", "source": "com.flutter.stripe.stripe_android-fragment-1.8.6-12:/animator/fragment_open_enter.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/mtrl_fab_show_motion_spec.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/mtrl_fab_show_motion_spec.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/m3_extended_fab_state_list_animator.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/m3_extended_fab_state_list_animator.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/mtrl_fab_transformation_sheet_collapse_spec.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/mtrl_fab_transformation_sheet_collapse_spec.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/m3_card_state_list_anim.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/m3_card_state_list_anim.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/m3_btn_state_list_anim.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/m3_btn_state_list_anim.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/mtrl_chip_state_list_anim.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/mtrl_chip_state_list_anim.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/fragment_fade_enter.xml", "source": "com.flutter.stripe.stripe_android-fragment-1.8.6-12:/animator/fragment_fade_enter.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/mtrl_extended_fab_state_list_animator.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/mtrl_extended_fab_state_list_animator.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/m3_extended_fab_hide_motion_spec.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/m3_extended_fab_hide_motion_spec.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/mtrl_extended_fab_hide_motion_spec.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/mtrl_extended_fab_hide_motion_spec.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/mtrl_fab_transformation_sheet_expand_spec.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/mtrl_fab_transformation_sheet_expand_spec.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/mtrl_extended_fab_change_size_collapse_motion_spec.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/mtrl_extended_fab_change_size_collapse_motion_spec.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/mtrl_fab_hide_motion_spec.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/mtrl_fab_hide_motion_spec.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/mtrl_btn_unelevated_state_list_anim.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/mtrl_btn_unelevated_state_list_anim.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/design_fab_hide_motion_spec.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/design_fab_hide_motion_spec.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/m3_elevated_chip_state_list_anim.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/m3_elevated_chip_state_list_anim.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/m3_btn_elevated_btn_state_list_anim.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/m3_btn_elevated_btn_state_list_anim.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/design_fab_show_motion_spec.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/design_fab_show_motion_spec.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/fragment_open_exit.xml", "source": "com.flutter.stripe.stripe_android-fragment-1.8.6-12:/animator/fragment_open_exit.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/m3_extended_fab_change_size_collapse_motion_spec.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/m3_extended_fab_change_size_collapse_motion_spec.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/m3_extended_fab_show_motion_spec.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/m3_extended_fab_show_motion_spec.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/fragment_close_exit.xml", "source": "com.flutter.stripe.stripe_android-fragment-1.8.6-12:/animator/fragment_close_exit.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/mtrl_card_state_list_anim.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/mtrl_card_state_list_anim.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/fragment_fade_exit.xml", "source": "com.flutter.stripe.stripe_android-fragment-1.8.6-12:/animator/fragment_fade_exit.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/m3_card_elevated_state_list_anim.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-65:/animator/m3_card_elevated_state_list_anim.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/animator/fragment_close_enter.xml", "source": "com.flutter.stripe.stripe_android-fragment-1.8.6-12:/animator/fragment_close_enter.xml"}]
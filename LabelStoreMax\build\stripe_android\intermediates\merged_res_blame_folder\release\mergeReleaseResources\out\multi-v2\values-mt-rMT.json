{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-mt-rMT/values-mt-rMT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-mt-rMT\\values-mt-rMT.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,256,322,390,458,530", "endColumns": "89,110,65,67,67,71,77", "endOffsets": "140,251,317,385,453,525,603"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-mt-rMT/values-mt-rMT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-mt-rMT\\values-mt-rMT.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,256,322,390,458,530", "endColumns": "89,110,65,67,67,71,77", "endOffsets": "140,251,317,385,453,525,603"}}]}]}
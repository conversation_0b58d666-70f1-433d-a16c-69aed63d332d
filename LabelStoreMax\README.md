<p align="center">
  <img width="200" src="image_6d27bc.png" alt="Velvete Store App Screenshot">
</p>

# Velvete Store App

### Official Velvete.ly E-commerce Mobile App

![Velvete Store App Screenshot](image_6d27bc.png "Velvete Store App UI Example")

### About Velvete Store App
[cite_start]The Velvete Store App is a high-performance native mobile e-commerce application, meticulously built with Flutter for deep and seamless integration with the velvete.ly WooCommerce backend[cite: 2]. [cite_start]Our core philosophy is to deliver a fully customizable, professional, and intuitive user experience that dynamically mirrors the website's configuration, avoiding static data or less performant webviews[cite: 3]. [cite_start]This app is engineered to serve as a robust and reusable template for future e-commerce ventures[cite: 4, 43].

### Requirements
-   [cite_start]WooCommerce Store (specifically velvete.ly) [cite: 2]
-   Android Studio/VSCode (for running the app)
-   Flutter SDK installed

### Getting Started
1.  Download or clone this repository.
2.  [cite_start]Ensure your WooCommerce store (velvete.ly) is properly configured with the WooCommerce REST API, WordPress REST API, Ultimate Member Plugin, and JWT Authentication for WP-API Plugin[cite: 7, 9, 10, 11].
3.  Configure the necessary API endpoints and authentication details within the Flutter project.
4.  Build and run the app using Android Studio or VSCode.

### Key Features Integrated
-   [cite_start]**Native Cross-Platform Performance:** Built with Flutter for a high-performance native experience on both iOS and Android[cite: 5, 6].
-   [cite_start]**Real-time Data Synchronization:** All critical e-commerce data, configurations, product information, order processing, and payment settings are retrieved dynamically and in real-time from the WooCommerce REST API[cite: 7, 8, 36].
-   [cite_start]**Dynamic Product Display:** Full support for variable products, dynamically fetching variations, attributes (color, size), corresponding prices, and images for seamless user selection[cite: 13, 14, 15].
-   **Intelligent Checkout Process:**
    * [cite_start]**Dynamic Payment Gateways:** Fetches all enabled payment gateways from WooCommerce, displaying only relevant options in the correct language (e.g., Arabic)[cite: 17, 18].
    * [cite_start]**Dynamic Shipping Methods:** Dynamically retrieves shipping zones and associated methods (including local options like "Al-sabil (درب السبيل)")[cite: 19, 20].
    * [cite_start]**Adaptive Checkout Forms:** Builds standard checkout forms dynamically based on WooCommerce configuration, including billing/shipping address components and validation rules[cite: 21, 22].
    * [cite_start]**Custom Checkout Fields Integration:** Specialized support for custom fields like the Libyan cities dropdown for delivery cost calculation, integrated via WordPress REST API or custom endpoints[cite: 23, 24, 25, 26].
-   [cite_start]**Secure User Account Management:** Enables direct app-based account creation and login, securely synced with the WooCommerce/Ultimate Member user database using JWT authentication[cite: 27, 28, 29, 30]. [cite_start]User data (name, email, WhatsApp-compatible phone number) is manageable in WordPress admin[cite: 30].
-   [cite_start]**Robust Order Management:** Orders placed via the app are successfully transmitted to and displayed in the WooCommerce order management system with all details[cite: 31].
-   [cite_start]**Enhanced User Interface:** Fixed critical UI bugs, including the blank "Create Account" page, which now correctly displays all registration fields, including custom dropdowns[cite: 33, 34, 41, 42].

### Security Vulnerabilities
If you discover a security vulnerability within the Velvete Store App, please send an e-mail to `<EMAIL>` (placeholder email, replace with actual).

### Uploading to the App Stores
-   [IOS - Deployment](https://flutter.dev/docs/deployment/ios)
-   [Android - Deployment](https://flutter.dev/docs/deployment/android)

### Licence
The Velvete Store App framework is closed-sourced software licensed under the MIT license.
{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,606,719,796,871,964,1059,1154,1248,1350,1445,1542,1640,1736,1829,1909,2015,2114,2210,2315,2418,2520,2674,2776", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,601,714,791,866,959,1054,1149,1243,1345,1440,1537,1635,1731,1824,1904,2010,2109,2205,2310,2413,2515,2669,2771,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,419,522,633,717,817,930,1007,1082,1175,1270,1365,1459,1561,1656,1753,1851,1947,2040,2120,2226,2325,2421,2526,2629,2731,2885,13405", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "414,517,628,712,812,925,1002,1077,1170,1265,1360,1454,1556,1651,1748,1846,1942,2035,2115,2221,2320,2416,2521,2624,2726,2880,2982,13480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "306", "startColumns": "4", "startOffsets": "27304", "endColumns": "84", "endOffsets": "27384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,337,434,639,688,755,851,920,1177,1254,1360,1430,1484,1548,1625,1718,2025,2099,2165,2218,2271,2353,2470,2577,2634,2720,2798,2880,2946,3045,3323,3401,3484,3547,3607,3670,3730,3802,3883,3983,4083,4167,4265,4357,4430,4509,4594,4787,4989,5102,5225,5282,5983,6085,6151", "endColumns": "112,168,96,204,48,66,95,68,256,76,105,69,53,63,76,92,306,73,65,52,52,81,116,106,56,85,77,81,65,98,277,77,82,62,59,62,59,71,80,99,99,83,97,91,72,78,84,192,201,112,122,56,700,101,65,54", "endOffsets": "163,332,429,634,683,750,846,915,1172,1249,1355,1425,1479,1543,1620,1713,2020,2094,2160,2213,2266,2348,2465,2572,2629,2715,2793,2875,2941,3040,3318,3396,3479,3542,3602,3665,3725,3797,3878,3978,4078,4162,4260,4352,4425,4504,4589,4782,4984,5097,5220,5277,5978,6080,6146,6201"}, "to": {"startLines": "233,234,235,237,241,242,243,244,245,246,247,263,266,268,272,273,278,284,285,293,303,307,308,309,310,311,317,320,325,327,328,329,330,333,335,336,340,344,345,347,349,361,386,387,388,389,390,408,415,416,417,418,420,421,422,439", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19697,19810,19979,20169,21168,21217,21284,21380,21449,21706,21783,23688,23950,24129,24412,24489,24860,25559,25633,26178,27099,27389,27471,27588,27695,27752,28305,28542,28963,29110,29209,29487,29565,29815,29996,30056,30358,30944,31016,31189,31394,33054,35420,35518,35610,35683,35762,37395,38075,38277,38390,38513,38637,39338,39440,41225", "endColumns": "112,168,96,204,48,66,95,68,256,76,105,69,53,63,76,92,306,73,65,52,52,81,116,106,56,85,77,81,65,98,277,77,82,62,59,62,59,71,80,99,99,83,97,91,72,78,84,192,201,112,122,56,700,101,65,54", "endOffsets": "19805,19974,20071,20369,21212,21279,21375,21444,21701,21778,21884,23753,23999,24188,24484,24577,25162,25628,25694,26226,27147,27466,27583,27690,27747,27833,28378,28619,29024,29204,29482,29560,29643,29873,30051,30114,30413,31011,31092,31284,31489,33133,35513,35605,35678,35757,35842,37583,38272,38385,38508,38565,39333,39435,39501,41275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,266,343,429,486,549,601,667,748,817,912,1007,1091,1169,1247,1330,1426,1518,1587,1676,1762,1849,1956,2039,2128,2204,2300,2372,2461,2553,2636,2710,2805,2869,2940,3052,3157,3228,3293,3951,4590,4664,4776,4877,4932,5029,5118,5184,5272,5365,5422,5506,5555,5631,5725,5792,5870,5937,6003,6050,6133,6230,6306,6351,6400,6467,6525,6593,6773,6928,7064,7129,7216,7291,7378,7457,7537,7614,7701,7782,7887,7974,8061,8114,8246,8296,8358,8425,8492,8565,8629,8701,8784,8852,8904", "endColumns": "67,76,65,76,85,56,62,51,65,80,68,94,94,83,77,77,82,95,91,68,88,85,86,106,82,88,75,95,71,88,91,82,73,94,63,70,111,104,70,64,657,638,73,111,100,54,96,88,65,87,92,56,83,48,75,93,66,77,66,65,46,82,96,75,44,48,66,57,67,179,154,135,64,86,74,86,78,79,76,86,80,104,86,86,52,131,49,61,66,66,72,63,71,82,67,51,78", "endOffsets": "118,195,261,338,424,481,544,596,662,743,812,907,1002,1086,1164,1242,1325,1421,1513,1582,1671,1757,1844,1951,2034,2123,2199,2295,2367,2456,2548,2631,2705,2800,2864,2935,3047,3152,3223,3288,3946,4585,4659,4771,4872,4927,5024,5113,5179,5267,5360,5417,5501,5550,5626,5720,5787,5865,5932,5998,6045,6128,6225,6301,6346,6395,6462,6520,6588,6768,6923,7059,7124,7211,7286,7373,7452,7532,7609,7696,7777,7882,7969,8056,8109,8241,8291,8353,8420,8487,8560,8624,8696,8779,8847,8899,8978"}, "to": {"startLines": "157,158,159,160,161,162,163,167,168,169,170,173,175,176,178,183,187,202,205,206,207,209,210,211,213,217,218,219,220,221,222,223,224,225,226,228,231,232,238,239,240,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,274,275,276,277,281,286,287,288,289,290,295,296,297,298,299,300,304,305,315,316,318,319,323,324,326,331,338,339,410,411,412,414,419,432,433,434,435,436,437,438,454", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13903,13971,14048,14114,14191,14277,14334,14642,14694,14760,14841,15049,15219,15314,15465,15816,16106,17142,17385,17477,17546,17701,17787,17874,18044,18341,18430,18506,18602,18674,18763,18855,18938,19012,19107,19245,19480,19592,20374,20445,20510,22133,22772,22846,22958,23059,23114,23211,23300,23366,23454,23547,23604,24193,24242,24318,24582,24649,24727,24794,25308,25699,25782,25879,25955,26000,26293,26360,26418,26486,26666,26821,27152,27217,28143,28218,28383,28462,28799,28876,29029,29648,30184,30271,37678,37731,37863,38013,38570,40746,40813,40886,40950,41022,41105,41173,42572", "endColumns": "67,76,65,76,85,56,62,51,65,80,68,94,94,83,77,77,82,95,91,68,88,85,86,106,82,88,75,95,71,88,91,82,73,94,63,70,111,104,70,64,657,638,73,111,100,54,96,88,65,87,92,56,83,48,75,93,66,77,66,65,46,82,96,75,44,48,66,57,67,179,154,135,64,86,74,86,78,79,76,86,80,104,86,86,52,131,49,61,66,66,72,63,71,82,67,51,78", "endOffsets": "13966,14043,14109,14186,14272,14329,14392,14689,14755,14836,14905,15139,15309,15393,15538,15889,16184,17233,17472,17541,17630,17782,17869,17976,18122,18425,18501,18597,18669,18758,18850,18933,19007,19102,19166,19311,19587,19692,20440,20505,21163,22767,22841,22953,23054,23109,23206,23295,23361,23449,23542,23599,23683,24237,24313,24407,24644,24722,24789,24855,25350,25777,25874,25950,25995,26044,26355,26413,26481,26661,26816,26952,27212,27299,28213,28300,28457,28537,28871,28958,29105,29748,30266,30353,37726,37858,37908,38070,38632,40808,40881,40945,41017,41100,41168,41220,42646"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,188,250,335,400,477,536,612,684,750,813", "endColumns": "74,57,61,84,64,76,58,75,71,65,62,68", "endOffsets": "125,183,245,330,395,472,531,607,679,745,808,877"}, "to": {"startLines": "172,182,184,185,186,190,198,201,204,208,212,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14974,15758,15894,15956,16041,16328,16873,17066,17313,17635,17981,18272", "endColumns": "74,57,61,84,64,76,58,75,71,65,62,68", "endOffsets": "15044,15811,15951,16036,16101,16400,16927,17137,17380,17696,18039,18336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,194,261,346,415,476,548,615,679,747,817,877,935,1008,1083,1154,1217,1292,1355,1437,1511,1590,1675,1782,1867,1915,1963,2044,2106,2179,2248,2345,2432,2521", "endColumns": "63,74,66,84,68,60,71,66,63,67,69,59,57,72,74,70,62,74,62,81,73,78,84,106,84,47,47,80,61,72,68,96,86,88,85", "endOffsets": "114,189,256,341,410,471,543,610,674,742,812,872,930,1003,1078,1149,1212,1287,1350,1432,1506,1585,1670,1777,1862,1910,1958,2039,2101,2174,2243,2340,2427,2516,2602"}, "to": {"startLines": "171,174,177,179,180,181,188,189,191,192,193,194,195,196,197,199,200,203,214,215,227,229,230,264,265,279,291,292,294,301,302,312,313,321,322", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14910,15144,15398,15543,15628,15697,16189,16261,16405,16469,16537,16607,16667,16725,16798,16932,17003,17238,18127,18190,19171,19316,19395,23758,23865,25167,26049,26097,26231,26957,27030,27838,27935,28624,28713", "endColumns": "63,74,66,84,68,60,71,66,63,67,69,59,57,72,74,70,62,74,62,81,73,78,84,106,84,47,47,80,61,72,68,96,86,88,85", "endOffsets": "14969,15214,15460,15623,15692,15753,16256,16323,16464,16532,16602,16662,16720,16793,16868,16998,17061,17308,18185,18267,19240,19390,19475,23860,23945,25210,26092,26173,26288,27025,27094,27930,28017,28708,28794"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,286,382,481,569,645,733,822,903,967,1031,1117,1207,1276,1354,1421", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "193,281,377,476,564,640,728,817,898,962,1026,1112,1202,1271,1349,1416,1536"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,471,472,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4468,4561,7035,7131,7306,8118,8194,12853,12942,13023,13087,13229,13315,13645,44152,44230,44297", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "4556,4644,7126,7225,7389,8189,8277,12937,13018,13082,13146,13310,13400,13709,44225,44292,44412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,88", "endOffsets": "140,229"}, "to": {"startLines": "474,475", "startColumns": "4,4", "startOffsets": "44417,44507", "endColumns": "89,88", "endOffsets": "44502,44591"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6935,7682,7782,7895", "endColumns": "99,99,112,97", "endOffsets": "7030,7777,7890,7988"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,217,300,393,482,560,637,762,855,964,1059,1180,1242,1360,1425,1614,1817,1951,2043,2148,2240,2336,2524,2623,2720,2974,3078,3201,3397,3613,3708,3818,4013,4102,4162,4227,4313,4405,4503,4576,4673,4763,4870,5083,5153,5220,5294,5402,5494,5585,5653,5737,5809,5878,5990,6078,6180,6272,6345,6425,6494,6582,6641,6743,6847,6951,7082,7143,7234,7334,7466,7538,7628,7728,7783,7845,7923,8025,8207,8491,8795,8893,8968,9058,9130,9240,9356,9449,9529,9606,9691,9771,9861,9981,10120,10187,10260,10314,10495,10565,10624,10697,10772,10850,10933,11052,11177,11287,11372,11451,11533,11602", "endColumns": "71,89,82,92,88,77,76,124,92,108,94,120,61,117,64,188,202,133,91,104,91,95,187,98,96,253,103,122,195,215,94,109,194,88,59,64,85,91,97,72,96,89,106,212,69,66,73,107,91,90,67,83,71,68,111,87,101,91,72,79,68,87,58,101,103,103,130,60,90,99,131,71,89,99,54,61,77,101,181,283,303,97,74,89,71,109,115,92,79,76,84,79,89,119,138,66,72,53,180,69,58,72,74,77,82,118,124,109,84,78,81,68,158", "endOffsets": "122,212,295,388,477,555,632,757,850,959,1054,1175,1237,1355,1420,1609,1812,1946,2038,2143,2235,2331,2519,2618,2715,2969,3073,3196,3392,3608,3703,3813,4008,4097,4157,4222,4308,4400,4498,4571,4668,4758,4865,5078,5148,5215,5289,5397,5489,5580,5648,5732,5804,5873,5985,6073,6175,6267,6340,6420,6489,6577,6636,6738,6842,6946,7077,7138,7229,7329,7461,7533,7623,7723,7778,7840,7918,8020,8202,8486,8790,8888,8963,9053,9125,9235,9351,9444,9524,9601,9686,9766,9856,9976,10115,10182,10255,10309,10490,10560,10619,10692,10767,10845,10928,11047,11172,11282,11367,11446,11528,11597,11756"}, "to": {"startLines": "164,165,166,236,248,249,250,267,280,282,283,314,332,334,337,341,342,343,346,348,350,351,352,353,354,355,356,357,358,359,360,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,409,413,423,424,425,426,427,428,429,430,431,440,441,442,443,444,445,446,447,448,449,450,451,452,453,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14397,14469,14559,20076,21889,21978,22056,24004,25215,25355,25464,28022,29753,29878,30119,30418,30607,30810,31097,31289,31494,31586,31682,31870,31969,32066,32320,32424,32547,32743,32959,33138,33248,33443,33532,33592,33657,33743,33835,33933,34006,34103,34193,34300,34513,34583,34650,34724,34832,34924,35015,35083,35167,35239,35308,35847,35935,36037,36129,36202,36282,36351,36439,36498,36600,36704,36808,36939,37000,37091,37191,37323,37588,37913,39506,39561,39623,39701,39803,39985,40269,40573,40671,41280,41370,41442,41552,41668,41761,41841,41918,42003,42083,42173,42293,42432,42499,42651,42705,42886,42956,43015,43088,43163,43241,43324,43443,43568,43678,43763,43842,43924,43993", "endColumns": "71,89,82,92,88,77,76,124,92,108,94,120,61,117,64,188,202,133,91,104,91,95,187,98,96,253,103,122,195,215,94,109,194,88,59,64,85,91,97,72,96,89,106,212,69,66,73,107,91,90,67,83,71,68,111,87,101,91,72,79,68,87,58,101,103,103,130,60,90,99,131,71,89,99,54,61,77,101,181,283,303,97,74,89,71,109,115,92,79,76,84,79,89,119,138,66,72,53,180,69,58,72,74,77,82,118,124,109,84,78,81,68,158", "endOffsets": "14464,14554,14637,20164,21973,22051,22128,24124,25303,25459,25554,28138,29810,29991,30179,30602,30805,30939,31184,31389,31581,31677,31865,31964,32061,32315,32419,32542,32738,32954,33049,33243,33438,33527,33587,33652,33738,33830,33928,34001,34098,34188,34295,34508,34578,34645,34719,34827,34919,35010,35078,35162,35234,35303,35415,35930,36032,36124,36197,36277,36346,36434,36493,36595,36699,36803,36934,36995,37086,37186,37318,37390,37673,38008,39556,39618,39696,39798,39980,40264,40568,40666,40741,41365,41437,41547,41663,41756,41836,41913,41998,42078,42168,42288,42427,42494,42567,42700,42881,42951,43010,43083,43158,43236,43319,43438,43563,43673,43758,43837,43919,43988,44147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,365,457,538,640,720,818,940,1019,1078,1141,1233,1297,1357,1449,1514,1577,1639,1706,1770,1824,1929,1988,2049,2103,2172,2291,2374,2451,2541,2625,2709,2845,2924,3008,3130,3216,3294,3348,3399,3465,3534,3608,3679,3755,3827,3904,3975,4049,4160,4251,4330,4417,4505,4577,4651,4736,4787,4866,4933,5014,5098,5160,5224,5287,5355,5462,5561,5660,5755,5813,5868,5946,6027,6106", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "261,360,452,533,635,715,813,935,1014,1073,1136,1228,1292,1352,1444,1509,1572,1634,1701,1765,1819,1924,1983,2044,2098,2167,2286,2369,2446,2536,2620,2704,2840,2919,3003,3125,3211,3289,3343,3394,3460,3529,3603,3674,3750,3822,3899,3970,4044,4155,4246,4325,4412,4500,4572,4646,4731,4782,4861,4928,5009,5093,5155,5219,5282,5350,5457,5556,5655,5750,5808,5863,5941,6022,6101,6189"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2987,3086,3178,3259,3361,4169,4267,4389,7394,7453,7590,8054,8282,8342,8434,8499,8562,8624,8691,8755,8809,8914,8973,9034,9088,9157,9276,9359,9436,9526,9610,9694,9830,9909,9993,10115,10201,10279,10333,10384,10450,10519,10593,10664,10740,10812,10889,10960,11034,11145,11236,11315,11402,11490,11562,11636,11721,11772,11851,11918,11999,12083,12145,12209,12272,12340,12447,12546,12645,12740,12798,13151,13485,13566,13714", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "311,3081,3173,3254,3356,3436,4262,4384,4463,7448,7511,7677,8113,8337,8429,8494,8557,8619,8686,8750,8804,8909,8968,9029,9083,9152,9271,9354,9431,9521,9605,9689,9825,9904,9988,10110,10196,10274,10328,10379,10445,10514,10588,10659,10735,10807,10884,10955,11029,11140,11231,11310,11397,11485,11557,11631,11716,11767,11846,11913,11994,12078,12140,12204,12267,12335,12442,12541,12640,12735,12793,12848,13224,13561,13640,13797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4729,4836,4993,5120,5230,5371,5496,5619,5871,6019,6127,6289,6417,6571,6727,6793,6856", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "4831,4988,5115,5225,5366,5491,5614,5718,6014,6122,6284,6412,6566,6722,6788,6851,6930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3441,3536,3638,3736,3835,3943,4048,13802", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "3531,3633,3731,3830,3938,4043,4164,13898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,69", "endOffsets": "258,328"}, "to": {"startLines": "81,476", "startColumns": "4,4", "startOffsets": "7993,44596", "endColumns": "60,73", "endOffsets": "8049,44665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5723", "endColumns": "147", "endOffsets": "5866"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,211", "endColumns": "79,75,73", "endOffsets": "130,206,280"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4649,7230,7516", "endColumns": "79,75,73", "endOffsets": "4724,7301,7585"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,606,719,796,871,964,1059,1154,1248,1350,1445,1542,1640,1736,1829,1909,2015,2114,2210,2315,2418,2520,2674,2776", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,601,714,791,866,959,1054,1149,1243,1345,1440,1537,1635,1731,1824,1904,2010,2109,2205,2310,2413,2515,2669,2771,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,419,522,633,717,817,930,1007,1082,1175,1270,1365,1459,1561,1656,1753,1851,1947,2040,2120,2226,2325,2421,2526,2629,2731,2885,13405", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "414,517,628,712,812,925,1002,1077,1170,1265,1360,1454,1556,1651,1748,1846,1942,2035,2115,2221,2320,2416,2521,2624,2726,2880,2982,13480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "306", "startColumns": "4", "startOffsets": "27304", "endColumns": "84", "endOffsets": "27384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,337,434,639,688,755,851,920,1177,1254,1360,1430,1484,1548,1625,1718,2025,2099,2165,2218,2271,2353,2470,2577,2634,2720,2798,2880,2946,3045,3323,3401,3484,3547,3607,3670,3730,3802,3883,3983,4083,4167,4265,4357,4430,4509,4594,4787,4989,5102,5225,5282,5983,6085,6151", "endColumns": "112,168,96,204,48,66,95,68,256,76,105,69,53,63,76,92,306,73,65,52,52,81,116,106,56,85,77,81,65,98,277,77,82,62,59,62,59,71,80,99,99,83,97,91,72,78,84,192,201,112,122,56,700,101,65,54", "endOffsets": "163,332,429,634,683,750,846,915,1172,1249,1355,1425,1479,1543,1620,1713,2020,2094,2160,2213,2266,2348,2465,2572,2629,2715,2793,2875,2941,3040,3318,3396,3479,3542,3602,3665,3725,3797,3878,3978,4078,4162,4260,4352,4425,4504,4589,4782,4984,5097,5220,5277,5978,6080,6146,6201"}, "to": {"startLines": "233,234,235,237,241,242,243,244,245,246,247,263,266,268,272,273,278,284,285,293,303,307,308,309,310,311,317,320,325,327,328,329,330,333,335,336,340,344,345,347,349,361,386,387,388,389,390,408,415,416,417,418,420,421,422,439", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19697,19810,19979,20169,21168,21217,21284,21380,21449,21706,21783,23688,23950,24129,24412,24489,24860,25559,25633,26178,27099,27389,27471,27588,27695,27752,28305,28542,28963,29110,29209,29487,29565,29815,29996,30056,30358,30944,31016,31189,31394,33054,35420,35518,35610,35683,35762,37395,38075,38277,38390,38513,38637,39338,39440,41225", "endColumns": "112,168,96,204,48,66,95,68,256,76,105,69,53,63,76,92,306,73,65,52,52,81,116,106,56,85,77,81,65,98,277,77,82,62,59,62,59,71,80,99,99,83,97,91,72,78,84,192,201,112,122,56,700,101,65,54", "endOffsets": "19805,19974,20071,20369,21212,21279,21375,21444,21701,21778,21884,23753,23999,24188,24484,24577,25162,25628,25694,26226,27147,27466,27583,27690,27747,27833,28378,28619,29024,29204,29482,29560,29643,29873,30051,30114,30413,31011,31092,31284,31489,33133,35513,35605,35678,35757,35842,37583,38272,38385,38508,38565,39333,39435,39501,41275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,266,343,429,486,549,601,667,748,817,912,1007,1091,1169,1247,1330,1426,1518,1587,1676,1762,1849,1956,2039,2128,2204,2300,2372,2461,2553,2636,2710,2805,2869,2940,3052,3157,3228,3293,3951,4590,4664,4776,4877,4932,5029,5118,5184,5272,5365,5422,5506,5555,5631,5725,5792,5870,5937,6003,6050,6133,6230,6306,6351,6400,6467,6525,6593,6773,6928,7064,7129,7216,7291,7378,7457,7537,7614,7701,7782,7887,7974,8061,8114,8246,8296,8358,8425,8492,8565,8629,8701,8784,8852,8904", "endColumns": "67,76,65,76,85,56,62,51,65,80,68,94,94,83,77,77,82,95,91,68,88,85,86,106,82,88,75,95,71,88,91,82,73,94,63,70,111,104,70,64,657,638,73,111,100,54,96,88,65,87,92,56,83,48,75,93,66,77,66,65,46,82,96,75,44,48,66,57,67,179,154,135,64,86,74,86,78,79,76,86,80,104,86,86,52,131,49,61,66,66,72,63,71,82,67,51,78", "endOffsets": "118,195,261,338,424,481,544,596,662,743,812,907,1002,1086,1164,1242,1325,1421,1513,1582,1671,1757,1844,1951,2034,2123,2199,2295,2367,2456,2548,2631,2705,2800,2864,2935,3047,3152,3223,3288,3946,4585,4659,4771,4872,4927,5024,5113,5179,5267,5360,5417,5501,5550,5626,5720,5787,5865,5932,5998,6045,6128,6225,6301,6346,6395,6462,6520,6588,6768,6923,7059,7124,7211,7286,7373,7452,7532,7609,7696,7777,7882,7969,8056,8109,8241,8291,8353,8420,8487,8560,8624,8696,8779,8847,8899,8978"}, "to": {"startLines": "157,158,159,160,161,162,163,167,168,169,170,173,175,176,178,183,187,202,205,206,207,209,210,211,213,217,218,219,220,221,222,223,224,225,226,228,231,232,238,239,240,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,274,275,276,277,281,286,287,288,289,290,295,296,297,298,299,300,304,305,315,316,318,319,323,324,326,331,338,339,410,411,412,414,419,432,433,434,435,436,437,438,454", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13903,13971,14048,14114,14191,14277,14334,14642,14694,14760,14841,15049,15219,15314,15465,15816,16106,17142,17385,17477,17546,17701,17787,17874,18044,18341,18430,18506,18602,18674,18763,18855,18938,19012,19107,19245,19480,19592,20374,20445,20510,22133,22772,22846,22958,23059,23114,23211,23300,23366,23454,23547,23604,24193,24242,24318,24582,24649,24727,24794,25308,25699,25782,25879,25955,26000,26293,26360,26418,26486,26666,26821,27152,27217,28143,28218,28383,28462,28799,28876,29029,29648,30184,30271,37678,37731,37863,38013,38570,40746,40813,40886,40950,41022,41105,41173,42572", "endColumns": "67,76,65,76,85,56,62,51,65,80,68,94,94,83,77,77,82,95,91,68,88,85,86,106,82,88,75,95,71,88,91,82,73,94,63,70,111,104,70,64,657,638,73,111,100,54,96,88,65,87,92,56,83,48,75,93,66,77,66,65,46,82,96,75,44,48,66,57,67,179,154,135,64,86,74,86,78,79,76,86,80,104,86,86,52,131,49,61,66,66,72,63,71,82,67,51,78", "endOffsets": "13966,14043,14109,14186,14272,14329,14392,14689,14755,14836,14905,15139,15309,15393,15538,15889,16184,17233,17472,17541,17630,17782,17869,17976,18122,18425,18501,18597,18669,18758,18850,18933,19007,19102,19166,19311,19587,19692,20440,20505,21163,22767,22841,22953,23054,23109,23206,23295,23361,23449,23542,23599,23683,24237,24313,24407,24644,24722,24789,24855,25350,25777,25874,25950,25995,26044,26355,26413,26481,26661,26816,26952,27212,27299,28213,28300,28457,28537,28871,28958,29105,29748,30266,30353,37726,37858,37908,38070,38632,40808,40881,40945,41017,41100,41168,41220,42646"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,188,250,335,400,477,536,612,684,750,813", "endColumns": "74,57,61,84,64,76,58,75,71,65,62,68", "endOffsets": "125,183,245,330,395,472,531,607,679,745,808,877"}, "to": {"startLines": "172,182,184,185,186,190,198,201,204,208,212,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14974,15758,15894,15956,16041,16328,16873,17066,17313,17635,17981,18272", "endColumns": "74,57,61,84,64,76,58,75,71,65,62,68", "endOffsets": "15044,15811,15951,16036,16101,16400,16927,17137,17380,17696,18039,18336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,194,261,346,415,476,548,615,679,747,817,877,935,1008,1083,1154,1217,1292,1355,1437,1511,1590,1675,1782,1867,1915,1963,2044,2106,2179,2248,2345,2432,2521", "endColumns": "63,74,66,84,68,60,71,66,63,67,69,59,57,72,74,70,62,74,62,81,73,78,84,106,84,47,47,80,61,72,68,96,86,88,85", "endOffsets": "114,189,256,341,410,471,543,610,674,742,812,872,930,1003,1078,1149,1212,1287,1350,1432,1506,1585,1670,1777,1862,1910,1958,2039,2101,2174,2243,2340,2427,2516,2602"}, "to": {"startLines": "171,174,177,179,180,181,188,189,191,192,193,194,195,196,197,199,200,203,214,215,227,229,230,264,265,279,291,292,294,301,302,312,313,321,322", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14910,15144,15398,15543,15628,15697,16189,16261,16405,16469,16537,16607,16667,16725,16798,16932,17003,17238,18127,18190,19171,19316,19395,23758,23865,25167,26049,26097,26231,26957,27030,27838,27935,28624,28713", "endColumns": "63,74,66,84,68,60,71,66,63,67,69,59,57,72,74,70,62,74,62,81,73,78,84,106,84,47,47,80,61,72,68,96,86,88,85", "endOffsets": "14969,15214,15460,15623,15692,15753,16256,16323,16464,16532,16602,16662,16720,16793,16868,16998,17061,17308,18185,18267,19240,19390,19475,23860,23945,25210,26092,26173,26288,27025,27094,27930,28017,28708,28794"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,286,382,481,569,645,733,822,903,967,1031,1117,1207,1276,1354,1421", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "193,281,377,476,564,640,728,817,898,962,1026,1112,1202,1271,1349,1416,1536"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,471,472,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4468,4561,7035,7131,7306,8118,8194,12853,12942,13023,13087,13229,13315,13645,44152,44230,44297", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "4556,4644,7126,7225,7389,8189,8277,12937,13018,13082,13146,13310,13400,13709,44225,44292,44412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,88", "endOffsets": "140,229"}, "to": {"startLines": "474,475", "startColumns": "4,4", "startOffsets": "44417,44507", "endColumns": "89,88", "endOffsets": "44502,44591"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6935,7682,7782,7895", "endColumns": "99,99,112,97", "endOffsets": "7030,7777,7890,7988"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,217,300,393,482,560,637,762,855,964,1059,1180,1242,1360,1425,1614,1817,1951,2043,2148,2240,2336,2524,2623,2720,2974,3078,3201,3397,3613,3708,3818,4013,4102,4162,4227,4313,4405,4503,4576,4673,4763,4870,5083,5153,5220,5294,5402,5494,5585,5653,5737,5809,5878,5990,6078,6180,6272,6345,6425,6494,6582,6641,6743,6847,6951,7082,7143,7234,7334,7466,7538,7628,7728,7783,7845,7923,8025,8207,8491,8795,8893,8968,9058,9130,9240,9356,9449,9529,9606,9691,9771,9861,9981,10120,10187,10260,10314,10495,10565,10624,10697,10772,10850,10933,11052,11177,11287,11372,11451,11533,11602", "endColumns": "71,89,82,92,88,77,76,124,92,108,94,120,61,117,64,188,202,133,91,104,91,95,187,98,96,253,103,122,195,215,94,109,194,88,59,64,85,91,97,72,96,89,106,212,69,66,73,107,91,90,67,83,71,68,111,87,101,91,72,79,68,87,58,101,103,103,130,60,90,99,131,71,89,99,54,61,77,101,181,283,303,97,74,89,71,109,115,92,79,76,84,79,89,119,138,66,72,53,180,69,58,72,74,77,82,118,124,109,84,78,81,68,158", "endOffsets": "122,212,295,388,477,555,632,757,850,959,1054,1175,1237,1355,1420,1609,1812,1946,2038,2143,2235,2331,2519,2618,2715,2969,3073,3196,3392,3608,3703,3813,4008,4097,4157,4222,4308,4400,4498,4571,4668,4758,4865,5078,5148,5215,5289,5397,5489,5580,5648,5732,5804,5873,5985,6073,6175,6267,6340,6420,6489,6577,6636,6738,6842,6946,7077,7138,7229,7329,7461,7533,7623,7723,7778,7840,7918,8020,8202,8486,8790,8888,8963,9053,9125,9235,9351,9444,9524,9601,9686,9766,9856,9976,10115,10182,10255,10309,10490,10560,10619,10692,10767,10845,10928,11047,11172,11282,11367,11446,11528,11597,11756"}, "to": {"startLines": "164,165,166,236,248,249,250,267,280,282,283,314,332,334,337,341,342,343,346,348,350,351,352,353,354,355,356,357,358,359,360,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,409,413,423,424,425,426,427,428,429,430,431,440,441,442,443,444,445,446,447,448,449,450,451,452,453,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14397,14469,14559,20076,21889,21978,22056,24004,25215,25355,25464,28022,29753,29878,30119,30418,30607,30810,31097,31289,31494,31586,31682,31870,31969,32066,32320,32424,32547,32743,32959,33138,33248,33443,33532,33592,33657,33743,33835,33933,34006,34103,34193,34300,34513,34583,34650,34724,34832,34924,35015,35083,35167,35239,35308,35847,35935,36037,36129,36202,36282,36351,36439,36498,36600,36704,36808,36939,37000,37091,37191,37323,37588,37913,39506,39561,39623,39701,39803,39985,40269,40573,40671,41280,41370,41442,41552,41668,41761,41841,41918,42003,42083,42173,42293,42432,42499,42651,42705,42886,42956,43015,43088,43163,43241,43324,43443,43568,43678,43763,43842,43924,43993", "endColumns": "71,89,82,92,88,77,76,124,92,108,94,120,61,117,64,188,202,133,91,104,91,95,187,98,96,253,103,122,195,215,94,109,194,88,59,64,85,91,97,72,96,89,106,212,69,66,73,107,91,90,67,83,71,68,111,87,101,91,72,79,68,87,58,101,103,103,130,60,90,99,131,71,89,99,54,61,77,101,181,283,303,97,74,89,71,109,115,92,79,76,84,79,89,119,138,66,72,53,180,69,58,72,74,77,82,118,124,109,84,78,81,68,158", "endOffsets": "14464,14554,14637,20164,21973,22051,22128,24124,25303,25459,25554,28138,29810,29991,30179,30602,30805,30939,31184,31389,31581,31677,31865,31964,32061,32315,32419,32542,32738,32954,33049,33243,33438,33527,33587,33652,33738,33830,33928,34001,34098,34188,34295,34508,34578,34645,34719,34827,34919,35010,35078,35162,35234,35303,35415,35930,36032,36124,36197,36277,36346,36434,36493,36595,36699,36803,36934,36995,37086,37186,37318,37390,37673,38008,39556,39618,39696,39798,39980,40264,40568,40666,40741,41365,41437,41547,41663,41756,41836,41913,41998,42078,42168,42288,42427,42494,42567,42700,42881,42951,43010,43083,43158,43236,43319,43438,43563,43673,43758,43837,43919,43988,44147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,365,457,538,640,720,818,940,1019,1078,1141,1233,1297,1357,1449,1514,1577,1639,1706,1770,1824,1929,1988,2049,2103,2172,2291,2374,2451,2541,2625,2709,2845,2924,3008,3130,3216,3294,3348,3399,3465,3534,3608,3679,3755,3827,3904,3975,4049,4160,4251,4330,4417,4505,4577,4651,4736,4787,4866,4933,5014,5098,5160,5224,5287,5355,5462,5561,5660,5755,5813,5868,5946,6027,6106", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "261,360,452,533,635,715,813,935,1014,1073,1136,1228,1292,1352,1444,1509,1572,1634,1701,1765,1819,1924,1983,2044,2098,2167,2286,2369,2446,2536,2620,2704,2840,2919,3003,3125,3211,3289,3343,3394,3460,3529,3603,3674,3750,3822,3899,3970,4044,4155,4246,4325,4412,4500,4572,4646,4731,4782,4861,4928,5009,5093,5155,5219,5282,5350,5457,5556,5655,5750,5808,5863,5941,6022,6101,6189"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2987,3086,3178,3259,3361,4169,4267,4389,7394,7453,7590,8054,8282,8342,8434,8499,8562,8624,8691,8755,8809,8914,8973,9034,9088,9157,9276,9359,9436,9526,9610,9694,9830,9909,9993,10115,10201,10279,10333,10384,10450,10519,10593,10664,10740,10812,10889,10960,11034,11145,11236,11315,11402,11490,11562,11636,11721,11772,11851,11918,11999,12083,12145,12209,12272,12340,12447,12546,12645,12740,12798,13151,13485,13566,13714", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "311,3081,3173,3254,3356,3436,4262,4384,4463,7448,7511,7677,8113,8337,8429,8494,8557,8619,8686,8750,8804,8909,8968,9029,9083,9152,9271,9354,9431,9521,9605,9689,9825,9904,9988,10110,10196,10274,10328,10379,10445,10514,10588,10659,10735,10807,10884,10955,11029,11140,11231,11310,11397,11485,11557,11631,11716,11767,11846,11913,11994,12078,12140,12204,12267,12335,12442,12541,12640,12735,12793,12848,13224,13561,13640,13797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4729,4836,4993,5120,5230,5371,5496,5619,5871,6019,6127,6289,6417,6571,6727,6793,6856", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "4831,4988,5115,5225,5366,5491,5614,5718,6014,6122,6284,6412,6566,6722,6788,6851,6930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3441,3536,3638,3736,3835,3943,4048,13802", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "3531,3633,3731,3830,3938,4043,4164,13898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,69", "endOffsets": "258,328"}, "to": {"startLines": "81,476", "startColumns": "4,4", "startOffsets": "7993,44596", "endColumns": "60,73", "endOffsets": "8049,44665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5723", "endColumns": "147", "endOffsets": "5866"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,211", "endColumns": "79,75,73", "endOffsets": "130,206,280"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4649,7230,7516", "endColumns": "79,75,73", "endOffsets": "4724,7301,7585"}}]}]}
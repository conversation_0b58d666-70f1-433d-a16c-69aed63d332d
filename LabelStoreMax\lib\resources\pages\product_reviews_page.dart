//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/widgets/no_results_for_products_widget.dart';
import '/resources/widgets/product_review_item_container_widget.dart';
import '/app/services/woocommerce_service.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';

class ProductReviewsPage extends NyStatefulWidget {
  static RouteView path = ("/product-reviews", (_) => ProductReviewsPage());

  ProductReviewsPage({super.key})
      : super(child: () => _ProductReviewsPageState());
}

class _ProductReviewsPageState extends NyPage<ProductReviewsPage> {
  WooProduct? _product;

  @override
  get init => () async {
        _product = widget.data() as WooProduct?;
      };

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(trans('Reviews')),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Column(
          children: [
            Container(
              height: mediaQuery.size.height / 5,
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              margin: EdgeInsets.symmetric(vertical: 16),
              decoration: BoxDecoration(
                  border: Border(bottom: BorderSide(color: Colors.black12))),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _product?.name ?? "",
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 8),
                    child: Text(
                      "${_product?.ratingCount} Reviews",
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  Row(
                    children: [
                      Container(
                        margin: EdgeInsets.only(right: 8),
                        child: Text(
                          "${_product?.averageRating!} Stars",
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                      RatingBarIndicator(
                        rating: double.parse(_product?.averageRating ?? "0"),
                        itemBuilder: (context, index) => Icon(
                          Icons.star,
                          color: Colors.amber,
                        ),
                        itemCount: 5,
                        itemSize: 20.0,
                        direction: Axis.horizontal,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Expanded(
                child: NyPullToRefresh(
              data: (page) async {
                if (_product == null) {
                  return null;
                }

                try {
                  print('📝 Fetching product reviews...');
                  print('Product ID: ${_product!.id}');
                  print('Page: $page');

                  // Fetch product reviews from WooCommerce API
                  final wooCommerceService = WooCommerceService();
                  List<WooProductReview> reviews = await wooCommerceService.getProductReviews(
                    page: page,
                    perPage: 50,
                    product: [_product!.id!],
                  );

                  print('✅ Found ${reviews.length} reviews for product ${_product!.id}');

                  // Filter for approved reviews only
                  List<WooProductReview> approvedReviews = reviews
                      .where((review) => review.status == WooProductReviewStatus.approved)
                      .toList();

                  print('✅ ${approvedReviews.length} approved reviews');

                  return approvedReviews;

                } catch (e) {
                  print('❌ Error fetching product reviews: $e');
                  return <WooProductReview>[];
                }
              },
              child: (context, review) {
                review as WooProductReview;
                return Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  margin: EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                      border:
                          Border(bottom: BorderSide(color: Colors.black12))),
                  child:
                      ProductReviewItemContainerWidget(productReview: review),
                );
              },
              empty: NoResultsForProductsWidget(),
            ))
          ],
        ),
      ),
    );
  }
}

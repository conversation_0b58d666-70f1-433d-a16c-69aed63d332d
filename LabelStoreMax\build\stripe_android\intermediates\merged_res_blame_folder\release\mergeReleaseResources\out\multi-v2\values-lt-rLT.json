{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-lt-rLT/values-lt-rLT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-lt-rLT\\values-lt-rLT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,199,268,360,431,492,560,626,690,768,840,902,963,1037,1101,1173,1236,1319,1387,1475,1553,1641,1735,1836,1924,1973,2025,2110,2174,2241,2314,2415,2500,2605", "endColumns": "64,78,68,91,70,60,67,65,63,77,71,61,60,73,63,71,62,82,67,87,77,87,93,100,87,48,51,84,63,66,72,100,84,104,100", "endOffsets": "115,194,263,355,426,487,555,621,685,763,835,897,958,1032,1096,1168,1231,1314,1382,1470,1548,1636,1730,1831,1919,1968,2020,2105,2169,2236,2309,2410,2495,2600,2701"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,116,117,131,143,144,146,153,154,163,164,172,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1646,1892,2162,2313,2405,2476,2988,3056,3197,3261,3339,3411,3473,3534,3608,3742,3814,4059,5005,5073,6144,6303,6391,10966,11067,12384,13274,13326,13466,14205,14272,15007,15108,15827,15932", "endColumns": "64,78,68,91,70,60,67,65,63,77,71,61,60,73,63,71,62,82,67,87,77,87,93,100,87,48,51,84,63,66,72,100,84,104,100", "endOffsets": "1706,1966,2226,2400,2471,2532,3051,3117,3256,3334,3406,3468,3529,3603,3667,3809,3872,4137,5068,5156,6217,6386,6480,11062,11150,12428,13321,13406,13525,14267,14340,15103,15188,15927,16028"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-lt-rLT\\values-lt-rLT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,185,345,439,646,692,765,860,934,1196,1268,1351,1423,1479,1543,1615,1703,1989,2067,2130,2185,2240,2313,2436,2548,2607,2690,2771,2861,2929,3046,3328,3418,3497,3586,3646,3709,3771,3844,3927,4029,4124,4213,4318,4421,4497,4576,4663,4858,5058,5172,5299,5363,6056,6161,6225", "endColumns": "129,159,93,206,45,72,94,73,261,71,82,71,55,63,71,87,285,77,62,54,54,72,122,111,58,82,80,89,67,116,281,89,78,88,59,62,61,72,82,101,94,88,104,102,75,78,86,194,199,113,126,63,692,104,63,54", "endOffsets": "180,340,434,641,687,760,855,929,1191,1263,1346,1418,1474,1538,1610,1698,1984,2062,2125,2180,2235,2308,2431,2543,2602,2685,2766,2856,2924,3041,3323,3413,3492,3581,3641,3704,3766,3839,3922,4024,4119,4208,4313,4416,4492,4571,4658,4853,5053,5167,5294,5358,6051,6156,6220,6275"}, "to": {"startLines": "85,86,87,89,93,94,95,96,97,98,99,115,118,120,124,125,130,136,137,145,155,158,159,160,161,162,168,171,176,178,179,180,181,184,186,187,191,195,196,198,200,212,237,238,239,240,241,259,266,267,268,269,271,272,273,290", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6704,6834,6994,7199,8265,8311,8384,8479,8553,8815,8887,10894,11155,11343,11660,11732,12098,12783,12861,13411,14345,14557,14630,14753,14865,14924,15489,15737,16204,16342,16459,16741,16831,17088,17290,17350,17673,18211,18284,18457,18663,20271,22656,22761,22864,22940,23019,24697,25397,25597,25711,25838,25979,26672,26777,28548", "endColumns": "129,159,93,206,45,72,94,73,261,71,82,71,55,63,71,87,285,77,62,54,54,72,122,111,58,82,80,89,67,116,281,89,78,88,59,62,61,72,82,101,94,88,104,102,75,78,86,194,199,113,126,63,692,104,63,54", "endOffsets": "6829,6989,7083,7401,8306,8379,8474,8548,8810,8882,8965,10961,11206,11402,11727,11815,12379,12856,12919,13461,14395,14625,14748,14860,14919,15002,15565,15822,16267,16454,16736,16826,16905,17172,17345,17408,17730,18279,18362,18554,18753,20355,22756,22859,22935,23014,23101,24887,25592,25706,25833,25897,26667,26772,26836,28598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-lt-rLT\\values-lt-rLT.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,140,243,309,378,444,527", "endColumns": "84,102,65,68,65,82,67", "endOffsets": "135,238,304,373,439,522,590"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-lt-rLT\\values-lt-rLT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,274,355,445,506,573,626,704,785,867,969,1071,1160,1242,1328,1416,1519,1616,1686,1780,1872,1969,2089,2176,2270,2347,2448,2534,2629,2725,2817,2903,3016,3089,3170,3281,3389,3482,3548,4248,4922,5002,5109,5217,5272,5365,5456,5527,5627,5726,5794,5890,5941,6024,6143,6213,6288,6355,6421,6470,6552,6647,6722,6770,6820,6890,6950,7028,7202,7361,7495,7566,7652,7723,7809,7896,7976,8055,8147,8217,8328,8416,8513,8569,8694,8746,8803,8880,8948,9021,9090,9161,9250,9322,9375", "endColumns": "73,82,61,80,89,60,66,52,77,80,81,101,101,88,81,85,87,102,96,69,93,91,96,119,86,93,76,100,85,94,95,91,85,112,72,80,110,107,92,65,699,673,79,106,107,54,92,90,70,99,98,67,95,50,82,118,69,74,66,65,48,81,94,74,47,49,69,59,77,173,158,133,70,85,70,85,86,79,78,91,69,110,87,96,55,124,51,56,76,67,72,68,70,88,71,52,83", "endOffsets": "124,207,269,350,440,501,568,621,699,780,862,964,1066,1155,1237,1323,1411,1514,1611,1681,1775,1867,1964,2084,2171,2265,2342,2443,2529,2624,2720,2812,2898,3011,3084,3165,3276,3384,3477,3543,4243,4917,4997,5104,5212,5267,5360,5451,5522,5622,5721,5789,5885,5936,6019,6138,6208,6283,6350,6416,6465,6547,6642,6717,6765,6815,6885,6945,7023,7197,7356,7490,7561,7647,7718,7804,7891,7971,8050,8142,8212,8323,8411,8508,8564,8689,8741,8798,8875,8943,9016,9085,9156,9245,9317,9370,9454"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,84,90,91,92,103,104,105,106,107,108,109,110,111,112,113,114,121,122,123,126,127,128,129,133,138,139,140,141,142,147,148,149,150,151,152,156,157,166,167,169,170,174,175,177,182,189,190,261,262,263,265,270,283,284,285,286,287,288,289,305", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "595,669,752,814,895,985,1046,1352,1405,1483,1564,1790,1971,2073,2231,2599,2900,3956,4215,4312,4382,4545,4637,4734,4918,5231,5325,5402,5503,5589,5684,5780,5872,5958,6071,6222,6485,6596,7406,7499,7565,9252,9926,10006,10113,10221,10276,10369,10460,10531,10631,10730,10798,11407,11458,11541,11820,11890,11965,12032,12544,12924,13006,13101,13176,13224,13530,13600,13660,13738,13912,14071,14400,14471,15332,15403,15570,15657,16033,16112,16272,16910,17488,17576,24985,25041,25166,25340,25902,28053,28121,28194,28263,28334,28423,28495,29950", "endColumns": "73,82,61,80,89,60,66,52,77,80,81,101,101,88,81,85,87,102,96,69,93,91,96,119,86,93,76,100,85,94,95,91,85,112,72,80,110,107,92,65,699,673,79,106,107,54,92,90,70,99,98,67,95,50,82,118,69,74,66,65,48,81,94,74,47,49,69,59,77,173,158,133,70,85,70,85,86,79,78,91,69,110,87,96,55,124,51,56,76,67,72,68,70,88,71,52,83", "endOffsets": "664,747,809,890,980,1041,1108,1400,1478,1559,1641,1887,2068,2157,2308,2680,2983,4054,4307,4377,4471,4632,4729,4849,5000,5320,5397,5498,5584,5679,5775,5867,5953,6066,6139,6298,6591,6699,7494,7560,8260,9921,10001,10108,10216,10271,10364,10455,10526,10626,10725,10793,10889,11453,11536,11655,11885,11960,12027,12093,12588,13001,13096,13171,13219,13269,13595,13655,13733,13907,14066,14200,14466,14552,15398,15484,15652,15732,16107,16199,16337,17016,17571,17668,25036,25161,25213,25392,25974,28116,28189,28258,28329,28418,28490,28543,30029"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-lt-rLT\\values-lt-rLT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,216,294,405,522,605,687,819,930,1030,1120,1259,1326,1439,1514,1670,1850,1990,2080,2184,2280,2378,2543,2657,2754,2974,3075,3203,3398,3603,3697,3810,3989,4085,4147,4215,4303,4402,4498,4575,4669,4756,4862,5051,5122,5193,5270,5391,5474,5559,5629,5714,5786,5863,5993,6081,6190,6289,6367,6452,6523,6611,6674,6778,6895,7002,7127,7189,7284,7385,7507,7584,7677,7799,7857,7926,8016,8123,8286,8558,8847,8939,9011,9096,9171,9285,9404,9490,9572,9646,9735,9817,9920,10048,10213,10278,10358,10422,10570,10640,10702,10784,10856,10933,11010,11133,11251,11358,11448,11531,11613,11686", "endColumns": "74,85,77,110,116,82,81,131,110,99,89,138,66,112,74,155,179,139,89,103,95,97,164,113,96,219,100,127,194,204,93,112,178,95,61,67,87,98,95,76,93,86,105,188,70,70,76,120,82,84,69,84,71,76,129,87,108,98,77,84,70,87,62,103,116,106,124,61,94,100,121,76,92,121,57,68,89,106,162,271,288,91,71,84,74,113,118,85,81,73,88,81,102,127,164,64,79,63,147,69,61,81,71,76,76,122,117,106,89,82,81,72,167", "endOffsets": "125,211,289,400,517,600,682,814,925,1025,1115,1254,1321,1434,1509,1665,1845,1985,2075,2179,2275,2373,2538,2652,2749,2969,3070,3198,3393,3598,3692,3805,3984,4080,4142,4210,4298,4397,4493,4570,4664,4751,4857,5046,5117,5188,5265,5386,5469,5554,5624,5709,5781,5858,5988,6076,6185,6284,6362,6447,6518,6606,6669,6773,6890,6997,7122,7184,7279,7380,7502,7579,7672,7794,7852,7921,8011,8118,8281,8553,8842,8934,9006,9091,9166,9280,9399,9485,9567,9641,9730,9812,9915,10043,10208,10273,10353,10417,10565,10635,10697,10779,10851,10928,11005,11128,11246,11353,11443,11526,11608,11681,11849"}, "to": {"startLines": "16,17,18,88,100,101,102,119,132,134,135,165,183,185,188,192,193,194,197,199,201,202,203,204,205,206,207,208,209,210,211,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,260,264,274,275,276,277,278,279,280,281,282,291,292,293,294,295,296,297,298,299,300,301,302,303,304,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1113,1188,1274,7088,8970,9087,9170,11211,12433,12593,12693,15193,17021,17177,17413,17735,17891,18071,18367,18559,18758,18854,18952,19117,19231,19328,19548,19649,19777,19972,20177,20360,20473,20652,20748,20810,20878,20966,21065,21161,21238,21332,21419,21525,21714,21785,21856,21933,22054,22137,22222,22292,22377,22449,22526,23106,23194,23303,23402,23480,23565,23636,23724,23787,23891,24008,24115,24240,24302,24397,24498,24620,24892,25218,26841,26899,26968,27058,27165,27328,27600,27889,27981,28603,28688,28763,28877,28996,29082,29164,29238,29327,29409,29512,29640,29805,29870,30034,30098,30246,30316,30378,30460,30532,30609,30686,30809,30927,31034,31124,31207,31289,31362", "endColumns": "74,85,77,110,116,82,81,131,110,99,89,138,66,112,74,155,179,139,89,103,95,97,164,113,96,219,100,127,194,204,93,112,178,95,61,67,87,98,95,76,93,86,105,188,70,70,76,120,82,84,69,84,71,76,129,87,108,98,77,84,70,87,62,103,116,106,124,61,94,100,121,76,92,121,57,68,89,106,162,271,288,91,71,84,74,113,118,85,81,73,88,81,102,127,164,64,79,63,147,69,61,81,71,76,76,122,117,106,89,82,81,72,167", "endOffsets": "1183,1269,1347,7194,9082,9165,9247,11338,12539,12688,12778,15327,17083,17285,17483,17886,18066,18206,18452,18658,18849,18947,19112,19226,19323,19543,19644,19772,19967,20172,20266,20468,20647,20743,20805,20873,20961,21060,21156,21233,21327,21414,21520,21709,21780,21851,21928,22049,22132,22217,22287,22372,22444,22521,22651,23189,23298,23397,23475,23560,23631,23719,23782,23886,24003,24110,24235,24297,24392,24493,24615,24692,24980,25335,26894,26963,27053,27160,27323,27595,27884,27976,28048,28683,28758,28872,28991,29077,29159,29233,29322,29404,29507,29635,29800,29865,29945,30093,30241,30311,30373,30455,30527,30604,30681,30804,30922,31029,31119,31202,31284,31357,31525"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-lt-rLT\\values-lt-rLT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,196,259,346,411,486,556,635,708,777,841", "endColumns": "78,61,62,86,64,74,69,78,72,68,63,69", "endOffsets": "129,191,254,341,406,481,551,630,703,772,836,906"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1711,2537,2685,2748,2835,3122,3672,3877,4142,4476,4854,5161", "endColumns": "78,61,62,86,64,74,69,78,72,68,63,69", "endOffsets": "1785,2594,2743,2830,2895,3192,3737,3951,4210,4540,4913,5226"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-lt-rLT/values-lt-rLT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-lt-rLT\\values-lt-rLT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,199,268,360,431,492,560,626,690,768,840,902,963,1037,1101,1173,1236,1319,1387,1475,1553,1641,1735,1836,1924,1973,2025,2110,2174,2241,2314,2415,2500,2605", "endColumns": "64,78,68,91,70,60,67,65,63,77,71,61,60,73,63,71,62,82,67,87,77,87,93,100,87,48,51,84,63,66,72,100,84,104,100", "endOffsets": "115,194,263,355,426,487,555,621,685,763,835,897,958,1032,1096,1168,1231,1314,1382,1470,1548,1636,1730,1831,1919,1968,2020,2105,2169,2236,2309,2410,2495,2600,2701"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,116,117,131,143,144,146,153,154,163,164,172,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1646,1892,2162,2313,2405,2476,2988,3056,3197,3261,3339,3411,3473,3534,3608,3742,3814,4059,5005,5073,6144,6303,6391,10966,11067,12384,13274,13326,13466,14205,14272,15007,15108,15827,15932", "endColumns": "64,78,68,91,70,60,67,65,63,77,71,61,60,73,63,71,62,82,67,87,77,87,93,100,87,48,51,84,63,66,72,100,84,104,100", "endOffsets": "1706,1966,2226,2400,2471,2532,3051,3117,3256,3334,3406,3468,3529,3603,3667,3809,3872,4137,5068,5156,6217,6386,6480,11062,11150,12428,13321,13406,13525,14267,14340,15103,15188,15927,16028"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-lt-rLT\\values-lt-rLT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,185,345,439,646,692,765,860,934,1196,1268,1351,1423,1479,1543,1615,1703,1989,2067,2130,2185,2240,2313,2436,2548,2607,2690,2771,2861,2929,3046,3328,3418,3497,3586,3646,3709,3771,3844,3927,4029,4124,4213,4318,4421,4497,4576,4663,4858,5058,5172,5299,5363,6056,6161,6225", "endColumns": "129,159,93,206,45,72,94,73,261,71,82,71,55,63,71,87,285,77,62,54,54,72,122,111,58,82,80,89,67,116,281,89,78,88,59,62,61,72,82,101,94,88,104,102,75,78,86,194,199,113,126,63,692,104,63,54", "endOffsets": "180,340,434,641,687,760,855,929,1191,1263,1346,1418,1474,1538,1610,1698,1984,2062,2125,2180,2235,2308,2431,2543,2602,2685,2766,2856,2924,3041,3323,3413,3492,3581,3641,3704,3766,3839,3922,4024,4119,4208,4313,4416,4492,4571,4658,4853,5053,5167,5294,5358,6051,6156,6220,6275"}, "to": {"startLines": "85,86,87,89,93,94,95,96,97,98,99,115,118,120,124,125,130,136,137,145,155,158,159,160,161,162,168,171,176,178,179,180,181,184,186,187,191,195,196,198,200,212,237,238,239,240,241,259,266,267,268,269,271,272,273,290", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6704,6834,6994,7199,8265,8311,8384,8479,8553,8815,8887,10894,11155,11343,11660,11732,12098,12783,12861,13411,14345,14557,14630,14753,14865,14924,15489,15737,16204,16342,16459,16741,16831,17088,17290,17350,17673,18211,18284,18457,18663,20271,22656,22761,22864,22940,23019,24697,25397,25597,25711,25838,25979,26672,26777,28548", "endColumns": "129,159,93,206,45,72,94,73,261,71,82,71,55,63,71,87,285,77,62,54,54,72,122,111,58,82,80,89,67,116,281,89,78,88,59,62,61,72,82,101,94,88,104,102,75,78,86,194,199,113,126,63,692,104,63,54", "endOffsets": "6829,6989,7083,7401,8306,8379,8474,8548,8810,8882,8965,10961,11206,11402,11727,11815,12379,12856,12919,13461,14395,14625,14748,14860,14919,15002,15565,15822,16267,16454,16736,16826,16905,17172,17345,17408,17730,18279,18362,18554,18753,20355,22756,22859,22935,23014,23101,24887,25592,25706,25833,25897,26667,26772,26836,28598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-lt-rLT\\values-lt-rLT.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,140,243,309,378,444,527", "endColumns": "84,102,65,68,65,82,67", "endOffsets": "135,238,304,373,439,522,590"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-lt-rLT\\values-lt-rLT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,274,355,445,506,573,626,704,785,867,969,1071,1160,1242,1328,1416,1519,1616,1686,1780,1872,1969,2089,2176,2270,2347,2448,2534,2629,2725,2817,2903,3016,3089,3170,3281,3389,3482,3548,4248,4922,5002,5109,5217,5272,5365,5456,5527,5627,5726,5794,5890,5941,6024,6143,6213,6288,6355,6421,6470,6552,6647,6722,6770,6820,6890,6950,7028,7202,7361,7495,7566,7652,7723,7809,7896,7976,8055,8147,8217,8328,8416,8513,8569,8694,8746,8803,8880,8948,9021,9090,9161,9250,9322,9375", "endColumns": "73,82,61,80,89,60,66,52,77,80,81,101,101,88,81,85,87,102,96,69,93,91,96,119,86,93,76,100,85,94,95,91,85,112,72,80,110,107,92,65,699,673,79,106,107,54,92,90,70,99,98,67,95,50,82,118,69,74,66,65,48,81,94,74,47,49,69,59,77,173,158,133,70,85,70,85,86,79,78,91,69,110,87,96,55,124,51,56,76,67,72,68,70,88,71,52,83", "endOffsets": "124,207,269,350,440,501,568,621,699,780,862,964,1066,1155,1237,1323,1411,1514,1611,1681,1775,1867,1964,2084,2171,2265,2342,2443,2529,2624,2720,2812,2898,3011,3084,3165,3276,3384,3477,3543,4243,4917,4997,5104,5212,5267,5360,5451,5522,5622,5721,5789,5885,5936,6019,6138,6208,6283,6350,6416,6465,6547,6642,6717,6765,6815,6885,6945,7023,7197,7356,7490,7561,7647,7718,7804,7891,7971,8050,8142,8212,8323,8411,8508,8564,8689,8741,8798,8875,8943,9016,9085,9156,9245,9317,9370,9454"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,84,90,91,92,103,104,105,106,107,108,109,110,111,112,113,114,121,122,123,126,127,128,129,133,138,139,140,141,142,147,148,149,150,151,152,156,157,166,167,169,170,174,175,177,182,189,190,261,262,263,265,270,283,284,285,286,287,288,289,305", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "595,669,752,814,895,985,1046,1352,1405,1483,1564,1790,1971,2073,2231,2599,2900,3956,4215,4312,4382,4545,4637,4734,4918,5231,5325,5402,5503,5589,5684,5780,5872,5958,6071,6222,6485,6596,7406,7499,7565,9252,9926,10006,10113,10221,10276,10369,10460,10531,10631,10730,10798,11407,11458,11541,11820,11890,11965,12032,12544,12924,13006,13101,13176,13224,13530,13600,13660,13738,13912,14071,14400,14471,15332,15403,15570,15657,16033,16112,16272,16910,17488,17576,24985,25041,25166,25340,25902,28053,28121,28194,28263,28334,28423,28495,29950", "endColumns": "73,82,61,80,89,60,66,52,77,80,81,101,101,88,81,85,87,102,96,69,93,91,96,119,86,93,76,100,85,94,95,91,85,112,72,80,110,107,92,65,699,673,79,106,107,54,92,90,70,99,98,67,95,50,82,118,69,74,66,65,48,81,94,74,47,49,69,59,77,173,158,133,70,85,70,85,86,79,78,91,69,110,87,96,55,124,51,56,76,67,72,68,70,88,71,52,83", "endOffsets": "664,747,809,890,980,1041,1108,1400,1478,1559,1641,1887,2068,2157,2308,2680,2983,4054,4307,4377,4471,4632,4729,4849,5000,5320,5397,5498,5584,5679,5775,5867,5953,6066,6139,6298,6591,6699,7494,7560,8260,9921,10001,10108,10216,10271,10364,10455,10526,10626,10725,10793,10889,11453,11536,11655,11885,11960,12027,12093,12588,13001,13096,13171,13219,13269,13595,13655,13733,13907,14066,14200,14466,14552,15398,15484,15652,15732,16107,16199,16337,17016,17571,17668,25036,25161,25213,25392,25974,28116,28189,28258,28329,28418,28490,28543,30029"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-lt-rLT\\values-lt-rLT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,216,294,405,522,605,687,819,930,1030,1120,1259,1326,1439,1514,1670,1850,1990,2080,2184,2280,2378,2543,2657,2754,2974,3075,3203,3398,3603,3697,3810,3989,4085,4147,4215,4303,4402,4498,4575,4669,4756,4862,5051,5122,5193,5270,5391,5474,5559,5629,5714,5786,5863,5993,6081,6190,6289,6367,6452,6523,6611,6674,6778,6895,7002,7127,7189,7284,7385,7507,7584,7677,7799,7857,7926,8016,8123,8286,8558,8847,8939,9011,9096,9171,9285,9404,9490,9572,9646,9735,9817,9920,10048,10213,10278,10358,10422,10570,10640,10702,10784,10856,10933,11010,11133,11251,11358,11448,11531,11613,11686", "endColumns": "74,85,77,110,116,82,81,131,110,99,89,138,66,112,74,155,179,139,89,103,95,97,164,113,96,219,100,127,194,204,93,112,178,95,61,67,87,98,95,76,93,86,105,188,70,70,76,120,82,84,69,84,71,76,129,87,108,98,77,84,70,87,62,103,116,106,124,61,94,100,121,76,92,121,57,68,89,106,162,271,288,91,71,84,74,113,118,85,81,73,88,81,102,127,164,64,79,63,147,69,61,81,71,76,76,122,117,106,89,82,81,72,167", "endOffsets": "125,211,289,400,517,600,682,814,925,1025,1115,1254,1321,1434,1509,1665,1845,1985,2075,2179,2275,2373,2538,2652,2749,2969,3070,3198,3393,3598,3692,3805,3984,4080,4142,4210,4298,4397,4493,4570,4664,4751,4857,5046,5117,5188,5265,5386,5469,5554,5624,5709,5781,5858,5988,6076,6185,6284,6362,6447,6518,6606,6669,6773,6890,6997,7122,7184,7279,7380,7502,7579,7672,7794,7852,7921,8011,8118,8281,8553,8842,8934,9006,9091,9166,9280,9399,9485,9567,9641,9730,9812,9915,10043,10208,10273,10353,10417,10565,10635,10697,10779,10851,10928,11005,11128,11246,11353,11443,11526,11608,11681,11849"}, "to": {"startLines": "16,17,18,88,100,101,102,119,132,134,135,165,183,185,188,192,193,194,197,199,201,202,203,204,205,206,207,208,209,210,211,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,260,264,274,275,276,277,278,279,280,281,282,291,292,293,294,295,296,297,298,299,300,301,302,303,304,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1113,1188,1274,7088,8970,9087,9170,11211,12433,12593,12693,15193,17021,17177,17413,17735,17891,18071,18367,18559,18758,18854,18952,19117,19231,19328,19548,19649,19777,19972,20177,20360,20473,20652,20748,20810,20878,20966,21065,21161,21238,21332,21419,21525,21714,21785,21856,21933,22054,22137,22222,22292,22377,22449,22526,23106,23194,23303,23402,23480,23565,23636,23724,23787,23891,24008,24115,24240,24302,24397,24498,24620,24892,25218,26841,26899,26968,27058,27165,27328,27600,27889,27981,28603,28688,28763,28877,28996,29082,29164,29238,29327,29409,29512,29640,29805,29870,30034,30098,30246,30316,30378,30460,30532,30609,30686,30809,30927,31034,31124,31207,31289,31362", "endColumns": "74,85,77,110,116,82,81,131,110,99,89,138,66,112,74,155,179,139,89,103,95,97,164,113,96,219,100,127,194,204,93,112,178,95,61,67,87,98,95,76,93,86,105,188,70,70,76,120,82,84,69,84,71,76,129,87,108,98,77,84,70,87,62,103,116,106,124,61,94,100,121,76,92,121,57,68,89,106,162,271,288,91,71,84,74,113,118,85,81,73,88,81,102,127,164,64,79,63,147,69,61,81,71,76,76,122,117,106,89,82,81,72,167", "endOffsets": "1183,1269,1347,7194,9082,9165,9247,11338,12539,12688,12778,15327,17083,17285,17483,17886,18066,18206,18452,18658,18849,18947,19112,19226,19323,19543,19644,19772,19967,20172,20266,20468,20647,20743,20805,20873,20961,21060,21156,21233,21327,21414,21520,21709,21780,21851,21928,22049,22132,22217,22287,22372,22444,22521,22651,23189,23298,23397,23475,23560,23631,23719,23782,23886,24003,24110,24235,24297,24392,24493,24615,24692,24980,25335,26894,26963,27053,27160,27323,27595,27884,27976,28048,28683,28758,28872,28991,29077,29159,29233,29322,29404,29507,29635,29800,29865,29945,30093,30241,30311,30373,30455,30527,30604,30681,30804,30922,31029,31119,31202,31284,31357,31525"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-lt-rLT\\values-lt-rLT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,196,259,346,411,486,556,635,708,777,841", "endColumns": "78,61,62,86,64,74,69,78,72,68,63,69", "endOffsets": "129,191,254,341,406,481,551,630,703,772,836,906"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1711,2537,2685,2748,2835,3122,3672,3877,4142,4476,4854,5161", "endColumns": "78,61,62,86,64,74,69,78,72,68,63,69", "endOffsets": "1785,2594,2743,2830,2895,3192,3737,3951,4210,4540,4913,5226"}}]}]}
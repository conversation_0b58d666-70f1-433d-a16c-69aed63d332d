//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

class LibyanCity {
  String name;
  String nameArabic;
  double deliveryCost;
  String region;

  LibyanCity({
    required this.name,
    required this.nameArabic,
    required this.deliveryCost,
    required this.region,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'nameArabic': nameArabic,
      'deliveryCost': deliveryCost,
      'region': region,
    };
  }

  factory LibyanCity.fromJson(Map<String, dynamic> json) {
    return LibyanCity(
      name: json['name'] ?? '',
      nameArabic: json['nameArabic'] ?? '',
      deliveryCost: (json['deliveryCost'] ?? 0.0).toDouble(),
      region: json['region'] ?? '',
    );
  }

  @override
  String toString() {
    return '$nameArabic ($name) - ${deliveryCost.toStringAsFixed(0)} LYD';
  }

  String getDisplayName() {
    return nameArabic.isNotEmpty ? nameArabic : name;
  }

  String getFormattedDeliveryCost() {
    return '${deliveryCost.toStringAsFixed(0)} د.ل';
  }

  // CRITICAL FIX: Add proper equality and hashCode for DropdownButton
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LibyanCity &&
           other.name == name &&
           other.nameArabic == nameArabic &&
           other.deliveryCost == deliveryCost &&
           other.region == region;
  }

  @override
  int get hashCode {
    return Object.hash(name, nameArabic, deliveryCost, region);
  }
}

class LibyanCitiesData {
  static List<LibyanCity> getAllCities() {
    return [
      // Tripoli Region
      LibyanCity(
        name: 'Tripoli',
        nameArabic: 'طرابلس',
        deliveryCost: 10.0,
        region: 'Tripoli',
      ),
      LibyanCity(
        name: 'Tajoura',
        nameArabic: 'تاجوراء',
        deliveryCost: 15.0,
        region: 'Tripoli',
      ),
      LibyanCity(
        name: 'Janzour',
        nameArabic: 'جنزور',
        deliveryCost: 15.0,
        region: 'Tripoli',
      ),
      LibyanCity(
        name: 'Zawiya',
        nameArabic: 'الزاوية',
        deliveryCost: 20.0,
        region: 'Tripoli',
      ),
      LibyanCity(
        name: 'Sabratha',
        nameArabic: 'صبراتة',
        deliveryCost: 25.0,
        region: 'Tripoli',
      ),
      LibyanCity(
        name: 'Surman',
        nameArabic: 'صرمان',
        deliveryCost: 25.0,
        region: 'Tripoli',
      ),
      LibyanCity(
        name: 'Zuwara',
        nameArabic: 'زوارة',
        deliveryCost: 30.0,
        region: 'Tripoli',
      ),
      LibyanCity(
        name: 'Gharyan',
        nameArabic: 'غريان',
        deliveryCost: 25.0,
        region: 'Tripoli',
      ),
      LibyanCity(
        name: 'Khoms',
        nameArabic: 'الخمس',
        deliveryCost: 20.0,
        region: 'Tripoli',
      ),
      LibyanCity(
        name: 'Zliten',
        nameArabic: 'زليتن',
        deliveryCost: 25.0,
        region: 'Tripoli',
      ),

      // Benghazi Region
      LibyanCity(
        name: 'Benghazi',
        nameArabic: 'بنغازي',
        deliveryCost: 35.0,
        region: 'Benghazi',
      ),
      LibyanCity(
        name: 'Bayda',
        nameArabic: 'البيضاء',
        deliveryCost: 40.0,
        region: 'Benghazi',
      ),
      LibyanCity(
        name: 'Derna',
        nameArabic: 'درنة',
        deliveryCost: 45.0,
        region: 'Benghazi',
      ),
      LibyanCity(
        name: 'Tobruk',
        nameArabic: 'طبرق',
        deliveryCost: 50.0,
        region: 'Benghazi',
      ),
      LibyanCity(
        name: 'Marj',
        nameArabic: 'المرج',
        deliveryCost: 40.0,
        region: 'Benghazi',
      ),
      LibyanCity(
        name: 'Shahat',
        nameArabic: 'شحات',
        deliveryCost: 45.0,
        region: 'Benghazi',
      ),

      // Misrata Region
      LibyanCity(
        name: 'Misrata',
        nameArabic: 'مصراتة',
        deliveryCost: 30.0,
        region: 'Misrata',
      ),
      LibyanCity(
        name: 'Sirte',
        nameArabic: 'سرت',
        deliveryCost: 35.0,
        region: 'Misrata',
      ),
      LibyanCity(
        name: 'Bani Walid',
        nameArabic: 'بني وليد',
        deliveryCost: 30.0,
        region: 'Misrata',
      ),

      // Sabha Region (South)
      LibyanCity(
        name: 'Sabha',
        nameArabic: 'سبها',
        deliveryCost: 55.0,
        region: 'Sabha',
      ),
      LibyanCity(
        name: 'Ubari',
        nameArabic: 'أوباري',
        deliveryCost: 60.0,
        region: 'Sabha',
      ),
      LibyanCity(
        name: 'Murzuq',
        nameArabic: 'مرزق',
        deliveryCost: 60.0,
        region: 'Sabha',
      ),

      // Other Major Cities
      LibyanCity(
        name: 'Ajdabiya',
        nameArabic: 'أجدابيا',
        deliveryCost: 40.0,
        region: 'Benghazi',
      ),
      LibyanCity(
        name: 'Nalut',
        nameArabic: 'نالوت',
        deliveryCost: 35.0,
        region: 'Tripoli',
      ),
      LibyanCity(
        name: 'Ghadames',
        nameArabic: 'غدامس',
        deliveryCost: 55.0,
        region: 'Tripoli',
      ),
      LibyanCity(
        name: 'Hun',
        nameArabic: 'هون',
        deliveryCost: 45.0,
        region: 'Sabha',
      ),
      LibyanCity(
        name: 'Waddan',
        nameArabic: 'ودان',
        deliveryCost: 50.0,
        region: 'Sabha',
      ),
      LibyanCity(
        name: 'Jufra',
        nameArabic: 'الجفرة',
        deliveryCost: 50.0,
        region: 'Sabha',
      ),
      LibyanCity(
        name: 'Kufra',
        nameArabic: 'الكفرة',
        deliveryCost: 60.0,
        region: 'Sabha',
      ),
    ];
  }

  static List<LibyanCity> getCitiesByRegion(String region) {
    return getAllCities().where((city) => city.region == region).toList();
  }

  static List<String> getAllRegions() {
    return getAllCities()
        .map((city) => city.region)
        .toSet()
        .toList()
        ..sort();
  }

  static LibyanCity? findCityByName(String name) {
    try {
      return getAllCities().firstWhere(
        (city) => city.name.toLowerCase() == name.toLowerCase() ||
                  city.nameArabic == name,
      );
    } catch (e) {
      return null;
    }
  }

  static LibyanCity? findCityByArabicName(String arabicName) {
    try {
      return getAllCities().firstWhere(
        (city) => city.nameArabic == arabicName,
      );
    } catch (e) {
      return null;
    }
  }

  static List<LibyanCity> searchCities(String query) {
    if (query.isEmpty) return getAllCities();
    
    String lowerQuery = query.toLowerCase();
    return getAllCities().where((city) {
      return city.name.toLowerCase().contains(lowerQuery) ||
             city.nameArabic.contains(query) ||
             city.region.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  static double getDeliveryCostForCity(String cityName) {
    LibyanCity? city = findCityByName(cityName);
    return city?.deliveryCost ?? 0.0;
  }

  static double getDeliveryCostForArabicCity(String arabicCityName) {
    LibyanCity? city = findCityByArabicName(arabicCityName);
    return city?.deliveryCost ?? 0.0;
  }
}

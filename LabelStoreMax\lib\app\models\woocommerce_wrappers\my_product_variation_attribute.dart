

class MyProductVariationAttribute {
  final int id;
  final String name;
  final String option;

  MyProductVariationAttribute({
    required this.id,
    required this.name,
    required this.option,
  });

  factory MyProductVariationAttribute.fromJson(Map<String, dynamic> json) {
    return MyProductVariationAttribute(
      id: json['id'] ?? 0,
      name: json['name']?.toString() ?? '',
      option: json['option']?.toString() ?? '',
    );
  }

  factory MyProductVariationAttribute.fromWooProductVariationAttribute(dynamic wooAttr) {
    return MyProductVariationAttribute(
      id: wooAttr?.id ?? 0,
      name: wooAttr?.name?.toString() ?? '',
      option: wooAttr?.option?.toString() ?? '',
    );
  }

  @override
  String toString() {
    return option;
  }
}

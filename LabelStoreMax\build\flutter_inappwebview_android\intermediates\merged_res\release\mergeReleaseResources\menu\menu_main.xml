<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:appcompat="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity">

    <item
        android:id="@+id/action_go_back"
        android:onClick="goBackButtonClicked"
        android:orderInCategory="100"
        android:title="@string/action_go_back"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_go_forward"
        android:title="@string/action_go_forward"
        android:orderInCategory="101"
        android:onClick="goForwardButtonClicked"
        app:showAsAction="never"/>

    <item
        android:id="@+id/action_share"
        android:title="@string/action_share"
        android:orderInCategory="102"
        android:onClick="shareButtonClicked"
        app:showAsAction="never"/>

    <item
        android:id="@+id/action_reload"
        android:title="@string/action_reload"
        android:orderInCategory="103"
        android:onClick="reloadButtonClicked"
        app:showAsAction="never"/>

    <item
        android:id="@+id/action_close"
        android:title="@string/action_close"
        android:orderInCategory="104"
        android:onClick="closeButtonClicked"
        app:showAsAction="never"/>

    <item
        android:id="@+id/menu_search"
        android:title="@string/menu_search"
        appcompat:actionViewClass="android.widget.SearchView"
        appcompat:showAsAction="ifRoom|withText" />

</menu>

[{"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/layout-v21/notification_template_custom_big.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-core-1.13.1-15:/layout-v21/notification_template_custom_big.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/layout-v21/notification_action.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-core-1.13.1-15:/layout-v21/notification_action.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/layout-v21/notification_action_tombstone.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-core-1.13.1-15:/layout-v21/notification_action_tombstone.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/layout-v21/notification_template_icon_group.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-core-1.13.1-15:/layout-v21/notification_template_icon_group.xml"}]
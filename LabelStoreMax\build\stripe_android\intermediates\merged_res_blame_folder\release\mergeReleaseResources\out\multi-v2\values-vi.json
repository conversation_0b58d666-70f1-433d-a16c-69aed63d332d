{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,283,397", "endColumns": "116,110,113,110", "endOffsets": "167,278,392,503"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6920,7708,7819,7933", "endColumns": "116,110,113,110", "endOffsets": "7032,7814,7928,8039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,274,364,434,498,567,636,701,772,842,905,965,1033,1106,1179,1240,1322,1386,1471,1550,1625,1716,1823,1909,1960,2007,2090,2157,2228,2297,2391,2476,2577", "endColumns": "64,77,75,89,69,63,68,68,64,70,69,62,59,67,72,72,60,81,63,84,78,74,90,106,85,50,46,82,66,70,68,93,84,100,97", "endOffsets": "115,193,269,359,429,493,562,631,696,767,837,900,960,1028,1101,1174,1235,1317,1381,1466,1545,1620,1711,1818,1904,1955,2002,2085,2152,2223,2292,2386,2471,2572,2670"}, "to": {"startLines": "171,174,177,179,180,181,188,189,191,192,193,194,195,196,197,199,200,203,214,215,227,229,230,264,265,279,291,292,294,301,302,312,313,321,322", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15054,15295,15557,15713,15803,15873,16382,16451,16586,16651,16722,16792,16855,16915,16983,17114,17187,17420,18318,18382,19421,19586,19661,24136,24243,25635,26523,26570,26711,27471,27542,28408,28502,29224,29325", "endColumns": "64,77,75,89,69,63,68,68,64,70,69,62,59,67,72,72,60,81,63,84,78,74,90,106,85,50,46,82,66,70,68,93,84,100,97", "endOffsets": "15114,15368,15628,15798,15868,15932,16446,16515,16646,16717,16787,16850,16910,16978,17051,17182,17243,17497,18377,18462,19495,19656,19747,24238,24324,25681,26565,26648,26773,27537,27606,28497,28582,29320,29418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,209", "endColumns": "78,74,77", "endOffsets": "129,204,282"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4607,7243,7536", "endColumns": "78,74,77", "endOffsets": "4681,7313,7609"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,499,579,659,758,872,952,1015,1078,1172,1246,1305,1391,1453,1514,1572,1636,1697,1751,1868,1925,1985,2039,2114,2241,2325,2405,2500,2584,2662,2792,2876,2954,3088,3179,3260,3311,3362,3428,3496,3572,3643,3723,3802,3877,3950,4026,4132,4221,4298,4389,4483,4557,4627,4720,4769,4850,4916,5001,5087,5149,5213,5276,5347,5446,5551,5649,5754,5809,5864,5942,6024,6103", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,76,77,79,79,98,113,79,62,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77,81,78,73", "endOffsets": "260,339,416,494,574,654,753,867,947,1010,1073,1167,1241,1300,1386,1448,1509,1567,1631,1692,1746,1863,1920,1980,2034,2109,2236,2320,2400,2495,2579,2657,2787,2871,2949,3083,3174,3255,3306,3357,3423,3491,3567,3638,3718,3797,3872,3945,4021,4127,4216,4293,4384,4478,4552,4622,4715,4764,4845,4911,4996,5082,5144,5208,5271,5342,5441,5546,5644,5749,5804,5859,5937,6019,6098,6172"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3008,3087,3164,3242,3322,4132,4231,4345,7410,7473,7614,8105,8357,8416,8502,8564,8625,8683,8747,8808,8862,8979,9036,9096,9150,9225,9352,9436,9516,9611,9695,9773,9903,9987,10065,10199,10290,10371,10422,10473,10539,10607,10683,10754,10834,10913,10988,11061,11137,11243,11332,11409,11500,11594,11668,11738,11831,11880,11961,12027,12112,12198,12260,12324,12387,12458,12557,12662,12760,12865,12920,13290,13634,13716,13867", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,78,76,77,79,79,98,113,79,62,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77,81,78,73", "endOffsets": "310,3082,3159,3237,3317,3397,4226,4340,4420,7468,7531,7703,8174,8411,8497,8559,8620,8678,8742,8803,8857,8974,9031,9091,9145,9220,9347,9431,9511,9606,9690,9768,9898,9982,10060,10194,10285,10366,10417,10468,10534,10602,10678,10749,10829,10908,10983,11056,11132,11238,11327,11404,11495,11589,11663,11733,11826,11875,11956,12022,12107,12193,12255,12319,12382,12453,12552,12657,12755,12860,12915,12970,13363,13711,13790,13936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "306", "startColumns": "4", "startOffsets": "27838", "endColumns": "82", "endOffsets": "27916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3402,3499,3601,3700,3800,3903,4016,13941", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "3494,3596,3695,3795,3898,4011,4127,14037"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-vi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,449,570,675,836,962,1077,1177,1346,1449,1602,1728,1883,2028,2092,2152", "endColumns": "97,157,120,104,160,125,114,99,168,102,152,125,154,144,63,59,78", "endOffsets": "290,448,569,674,835,961,1076,1176,1345,1448,1601,1727,1882,2027,2091,2151,2230"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4686,4788,4950,5075,5184,5349,5479,5598,5830,6003,6110,6267,6397,6556,6705,6773,6837", "endColumns": "101,161,124,108,164,129,118,103,172,106,156,129,158,148,67,63,82", "endOffsets": "4783,4945,5070,5179,5344,5474,5593,5697,5998,6105,6262,6392,6551,6700,6768,6832,6915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,393,493,585,670,763,857,938,1008,1078,1168,1259,1331,1408,1474", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "196,282,388,488,580,665,758,852,933,1003,1073,1163,1254,1326,1403,1469,1583"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,471,472,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4425,4521,7037,7143,7318,8179,8264,12975,13069,13150,13220,13368,13458,13795,45238,45315,45381", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "4516,4602,7138,7238,7405,8259,8352,13064,13145,13215,13285,13453,13544,13862,45310,45376,45490"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,86", "endOffsets": "138,225"}, "to": {"startLines": "474,475", "startColumns": "4,4", "startOffsets": "45495,45583", "endColumns": "87,86", "endOffsets": "45578,45665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,222,304,400,495,583,670,827,923,1029,1126,1257,1320,1433,1503,1680,1882,2024,2111,2218,2309,2409,2582,2693,2801,3061,3182,3319,3518,3727,3828,3941,4138,4236,4295,4360,4446,4539,4642,4714,4820,4906,5025,5233,5298,5363,5445,5561,5643,5733,5798,5874,5950,6024,6156,6244,6351,6448,6516,6591,6656,6749,6806,6915,7023,7147,7272,7333,7431,7542,7669,7744,7837,7942,8006,8063,8155,8258,8437,8693,8975,9084,9156,9243,9318,9454,9578,9674,9745,9818,9899,9979,10078,10200,10330,10403,10469,10523,10695,10766,10825,10906,10986,11064,11148,11274,11402,11512,11599,11673,11769,11836", "endColumns": "76,89,81,95,94,87,86,156,95,105,96,130,62,112,69,176,201,141,86,106,90,99,172,110,107,259,120,136,198,208,100,112,196,97,58,64,85,92,102,71,105,85,118,207,64,64,81,115,81,89,64,75,75,73,131,87,106,96,67,74,64,92,56,108,107,123,124,60,97,110,126,74,92,104,63,56,91,102,178,255,281,108,71,86,74,135,123,95,70,72,80,79,98,121,129,72,65,53,171,70,58,80,79,77,83,125,127,109,86,73,95,66,165", "endOffsets": "127,217,299,395,490,578,665,822,918,1024,1121,1252,1315,1428,1498,1675,1877,2019,2106,2213,2304,2404,2577,2688,2796,3056,3177,3314,3513,3722,3823,3936,4133,4231,4290,4355,4441,4534,4637,4709,4815,4901,5020,5228,5293,5358,5440,5556,5638,5728,5793,5869,5945,6019,6151,6239,6346,6443,6511,6586,6651,6744,6801,6910,7018,7142,7267,7328,7426,7537,7664,7739,7832,7937,8001,8058,8150,8253,8432,8688,8970,9079,9151,9238,9313,9449,9573,9669,9740,9813,9894,9974,10073,10195,10325,10398,10464,10518,10690,10761,10820,10901,10981,11059,11143,11269,11397,11507,11594,11668,11764,11831,11997"}, "to": {"startLines": "164,165,166,236,248,249,250,267,280,282,283,314,332,334,337,341,342,343,346,348,350,351,352,353,354,355,356,357,358,359,360,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,409,413,423,424,425,426,427,428,429,430,431,440,441,442,443,444,445,446,447,448,449,450,451,452,453,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14505,14582,14672,20331,22179,22274,22362,24382,25686,25828,25934,28587,30481,30605,30850,31160,31337,31539,31821,32006,32213,32304,32404,32577,32688,32796,33056,33177,33314,33513,33722,33917,34030,34227,34325,34384,34449,34535,34628,34731,34803,34909,34995,35114,35322,35387,35452,35534,35650,35732,35822,35887,35963,36039,36113,36680,36768,36875,36972,37040,37115,37180,37273,37330,37439,37547,37671,37796,37857,37955,38066,38193,38496,38799,40543,40607,40664,40756,40859,41038,41294,41576,41685,42306,42393,42468,42604,42728,42824,42895,42968,43049,43129,43228,43350,43480,43553,43705,43759,43931,44002,44061,44142,44222,44300,44384,44510,44638,44748,44835,44909,45005,45072", "endColumns": "76,89,81,95,94,87,86,156,95,105,96,130,62,112,69,176,201,141,86,106,90,99,172,110,107,259,120,136,198,208,100,112,196,97,58,64,85,92,102,71,105,85,118,207,64,64,81,115,81,89,64,75,75,73,131,87,106,96,67,74,64,92,56,108,107,123,124,60,97,110,126,74,92,104,63,56,91,102,178,255,281,108,71,86,74,135,123,95,70,72,80,79,98,121,129,72,65,53,171,70,58,80,79,77,83,125,127,109,86,73,95,66,165", "endOffsets": "14577,14667,14749,20422,22269,22357,22444,24534,25777,25929,26026,28713,30539,30713,30915,31332,31534,31676,31903,32108,32299,32399,32572,32683,32791,33051,33172,33309,33508,33717,33818,34025,34222,34320,34379,34444,34530,34623,34726,34798,34904,34990,35109,35317,35382,35447,35529,35645,35727,35817,35882,35958,36034,36108,36240,36763,36870,36967,37035,37110,37175,37268,37325,37434,37542,37666,37791,37852,37950,38061,38188,38263,38584,38899,40602,40659,40751,40854,41033,41289,41571,41680,41752,42388,42463,42599,42723,42819,42890,42963,43044,43124,43223,43345,43475,43548,43614,43754,43926,43997,44056,44137,44217,44295,44379,44505,44633,44743,44830,44904,45000,45067,45233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,422,524,633,717,820,939,1017,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2114,2218,2326,2427,2532,2647,2752,2909,13549", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "417,519,628,712,815,934,1012,1088,1179,1272,1367,1461,1561,1654,1749,1843,1934,2025,2109,2213,2321,2422,2527,2642,2747,2904,3003,13629"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-vi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "123", "endOffsets": "318"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5702", "endColumns": "127", "endOffsets": "5825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,192,254,324,403,459,518,570,655,736,818,916,1014,1100,1180,1264,1345,1441,1534,1604,1694,1777,1864,1971,2056,2141,2222,2323,2406,2499,2588,2673,2757,2865,2945,3031,3129,3220,3295,3356,4064,4748,4823,4938,5043,5098,5189,5278,5345,5442,5539,5595,5681,5727,5811,5913,5977,6051,6118,6184,6230,6314,6412,6493,6538,6582,6645,6707,6769,6967,7147,7275,7344,7444,7516,7611,7695,7776,7859,7955,8027,8146,8225,8321,8376,8485,8531,8584,8654,8715,8783,8857,8936,9028,9094,9145", "endColumns": "63,72,61,69,78,55,58,51,84,80,81,97,97,85,79,83,80,95,92,69,89,82,86,106,84,84,80,100,82,92,88,84,83,107,79,85,97,90,74,60,707,683,74,114,104,54,90,88,66,96,96,55,85,45,83,101,63,73,66,65,45,83,97,80,44,43,62,61,61,197,179,127,68,99,71,94,83,80,82,95,71,118,78,95,54,108,45,52,69,60,67,73,78,91,65,50,85", "endOffsets": "114,187,249,319,398,454,513,565,650,731,813,911,1009,1095,1175,1259,1340,1436,1529,1599,1689,1772,1859,1966,2051,2136,2217,2318,2401,2494,2583,2668,2752,2860,2940,3026,3124,3215,3290,3351,4059,4743,4818,4933,5038,5093,5184,5273,5340,5437,5534,5590,5676,5722,5806,5908,5972,6046,6113,6179,6225,6309,6407,6488,6533,6577,6640,6702,6764,6962,7142,7270,7339,7439,7511,7606,7690,7771,7854,7950,8022,8141,8220,8316,8371,8480,8526,8579,8649,8710,8778,8852,8931,9023,9089,9140,9226"}, "to": {"startLines": "157,158,159,160,161,162,163,167,168,169,170,173,175,176,178,183,187,202,205,206,207,209,210,211,213,217,218,219,220,221,222,223,224,225,226,228,231,232,238,239,240,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,274,275,276,277,281,286,287,288,289,290,295,296,297,298,299,300,304,305,315,316,318,319,323,324,326,331,338,339,410,411,412,414,419,432,433,434,435,436,437,438,454", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14042,14106,14179,14241,14311,14390,14446,14754,14806,14891,14972,15197,15373,15471,15633,16001,16301,17324,17575,17668,17738,17891,17974,18061,18233,18532,18617,18698,18799,18882,18975,19064,19149,19233,19341,19500,19752,19850,20667,20742,20803,22449,23133,23208,23323,23428,23483,23574,23663,23730,23827,23924,23980,24603,24649,24733,25015,25079,25153,25220,25782,26171,26255,26353,26434,26479,26778,26841,26903,26965,27163,27343,27669,27738,28718,28790,28971,29055,29423,29506,29675,30362,30920,30999,38589,38644,38753,38904,39483,41757,41818,41886,41960,42039,42131,42197,43619", "endColumns": "63,72,61,69,78,55,58,51,84,80,81,97,97,85,79,83,80,95,92,69,89,82,86,106,84,84,80,100,82,92,88,84,83,107,79,85,97,90,74,60,707,683,74,114,104,54,90,88,66,96,96,55,85,45,83,101,63,73,66,65,45,83,97,80,44,43,62,61,61,197,179,127,68,99,71,94,83,80,82,95,71,118,78,95,54,108,45,52,69,60,67,73,78,91,65,50,85", "endOffsets": "14101,14174,14236,14306,14385,14441,14500,14801,14886,14967,15049,15290,15466,15552,15708,16080,16377,17415,17663,17733,17823,17969,18056,18163,18313,18612,18693,18794,18877,18970,19059,19144,19228,19336,19416,19581,19845,19936,20737,20798,21506,23128,23203,23318,23423,23478,23569,23658,23725,23822,23919,23975,24061,24644,24728,24830,25074,25148,25215,25281,25823,26250,26348,26429,26474,26518,26836,26898,26960,27158,27338,27466,27733,27833,28785,28880,29050,29131,29501,29597,29742,30476,30994,31090,38639,38748,38794,38952,39548,41813,41881,41955,42034,42126,42192,42243,43700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-vi\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,75", "endOffsets": "258,334"}, "to": {"startLines": "81,476", "startColumns": "4,4", "startOffsets": "8044,45670", "endColumns": "60,79", "endOffsets": "8100,45745"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,197,263,352,413,479,537,613,686,749,814", "endColumns": "77,63,65,88,60,65,57,75,72,62,64,64", "endOffsets": "128,192,258,347,408,474,532,608,681,744,809,874"}, "to": {"startLines": "172,182,184,185,186,190,198,201,204,208,212,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15119,15937,16085,16151,16240,16520,17056,17248,17502,17828,18168,18467", "endColumns": "77,63,65,88,60,65,57,75,72,62,64,64", "endOffsets": "15192,15996,16146,16235,16296,16581,17109,17319,17570,17886,18228,18527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,341,445,685,734,802,902,981,1207,1268,1353,1423,1476,1540,1622,1720,2069,2143,2209,2267,2325,2409,2545,2662,2724,2812,2898,2986,3059,3161,3514,3594,3674,3735,3800,3867,3932,3998,4072,4170,4270,4364,4466,4562,4634,4713,4799,5027,5258,5366,5499,5553,6376,6481,6543", "endColumns": "115,169,103,239,48,67,99,78,225,60,84,69,52,63,81,97,348,73,65,57,57,83,135,116,61,87,85,87,72,101,352,79,79,60,64,66,64,65,73,97,99,93,101,95,71,78,85,227,230,107,132,53,822,104,61,57", "endOffsets": "166,336,440,680,729,797,897,976,1202,1263,1348,1418,1471,1535,1617,1715,2064,2138,2204,2262,2320,2404,2540,2657,2719,2807,2893,2981,3054,3156,3509,3589,3669,3730,3795,3862,3927,3993,4067,4165,4265,4359,4461,4557,4629,4708,4794,5022,5253,5361,5494,5548,6371,6476,6538,6596"}, "to": {"startLines": "233,234,235,237,241,242,243,244,245,246,247,263,266,268,272,273,278,284,285,293,303,307,308,309,310,311,317,320,325,327,328,329,330,333,335,336,340,344,345,347,349,361,386,387,388,389,390,408,415,416,417,418,420,421,422,439", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19941,20057,20227,20427,21511,21560,21628,21728,21807,22033,22094,24066,24329,24539,24835,24917,25286,26031,26105,26653,27611,27921,28005,28141,28258,28320,28885,29136,29602,29747,29849,30202,30282,30544,30718,30783,31095,31681,31747,31908,32113,33823,36245,36347,36443,36515,36594,38268,38957,39188,39296,39429,39553,40376,40481,42248", "endColumns": "115,169,103,239,48,67,99,78,225,60,84,69,52,63,81,97,348,73,65,57,57,83,135,116,61,87,85,87,72,101,352,79,79,60,64,66,64,65,73,97,99,93,101,95,71,78,85,227,230,107,132,53,822,104,61,57", "endOffsets": "20052,20222,20326,20662,21555,21623,21723,21802,22028,22089,22174,24131,24377,24598,24912,25010,25630,26100,26166,26706,27664,28000,28136,28253,28315,28403,28966,29219,29670,29844,30197,30277,30357,30600,30778,30845,31155,31742,31816,32001,32208,33912,36342,36438,36510,36589,36675,38491,39183,39291,39424,39478,40371,40476,40538,42301"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,283,397", "endColumns": "116,110,113,110", "endOffsets": "167,278,392,503"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6920,7708,7819,7933", "endColumns": "116,110,113,110", "endOffsets": "7032,7814,7928,8039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,274,364,434,498,567,636,701,772,842,905,965,1033,1106,1179,1240,1322,1386,1471,1550,1625,1716,1823,1909,1960,2007,2090,2157,2228,2297,2391,2476,2577", "endColumns": "64,77,75,89,69,63,68,68,64,70,69,62,59,67,72,72,60,81,63,84,78,74,90,106,85,50,46,82,66,70,68,93,84,100,97", "endOffsets": "115,193,269,359,429,493,562,631,696,767,837,900,960,1028,1101,1174,1235,1317,1381,1466,1545,1620,1711,1818,1904,1955,2002,2085,2152,2223,2292,2386,2471,2572,2670"}, "to": {"startLines": "171,174,177,179,180,181,188,189,191,192,193,194,195,196,197,199,200,203,214,215,227,229,230,264,265,279,291,292,294,301,302,312,313,321,322", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15054,15295,15557,15713,15803,15873,16382,16451,16586,16651,16722,16792,16855,16915,16983,17114,17187,17420,18318,18382,19421,19586,19661,24136,24243,25635,26523,26570,26711,27471,27542,28408,28502,29224,29325", "endColumns": "64,77,75,89,69,63,68,68,64,70,69,62,59,67,72,72,60,81,63,84,78,74,90,106,85,50,46,82,66,70,68,93,84,100,97", "endOffsets": "15114,15368,15628,15798,15868,15932,16446,16515,16646,16717,16787,16850,16910,16978,17051,17182,17243,17497,18377,18462,19495,19656,19747,24238,24324,25681,26565,26648,26773,27537,27606,28497,28582,29320,29418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,209", "endColumns": "78,74,77", "endOffsets": "129,204,282"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4607,7243,7536", "endColumns": "78,74,77", "endOffsets": "4681,7313,7609"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,499,579,659,758,872,952,1015,1078,1172,1246,1305,1391,1453,1514,1572,1636,1697,1751,1868,1925,1985,2039,2114,2241,2325,2405,2500,2584,2662,2792,2876,2954,3088,3179,3260,3311,3362,3428,3496,3572,3643,3723,3802,3877,3950,4026,4132,4221,4298,4389,4483,4557,4627,4720,4769,4850,4916,5001,5087,5149,5213,5276,5347,5446,5551,5649,5754,5809,5864,5942,6024,6103", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,76,77,79,79,98,113,79,62,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77,81,78,73", "endOffsets": "260,339,416,494,574,654,753,867,947,1010,1073,1167,1241,1300,1386,1448,1509,1567,1631,1692,1746,1863,1920,1980,2034,2109,2236,2320,2400,2495,2579,2657,2787,2871,2949,3083,3174,3255,3306,3357,3423,3491,3567,3638,3718,3797,3872,3945,4021,4127,4216,4293,4384,4478,4552,4622,4715,4764,4845,4911,4996,5082,5144,5208,5271,5342,5441,5546,5644,5749,5804,5859,5937,6019,6098,6172"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3008,3087,3164,3242,3322,4132,4231,4345,7410,7473,7614,8105,8357,8416,8502,8564,8625,8683,8747,8808,8862,8979,9036,9096,9150,9225,9352,9436,9516,9611,9695,9773,9903,9987,10065,10199,10290,10371,10422,10473,10539,10607,10683,10754,10834,10913,10988,11061,11137,11243,11332,11409,11500,11594,11668,11738,11831,11880,11961,12027,12112,12198,12260,12324,12387,12458,12557,12662,12760,12865,12920,13290,13634,13716,13867", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,78,76,77,79,79,98,113,79,62,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77,81,78,73", "endOffsets": "310,3082,3159,3237,3317,3397,4226,4340,4420,7468,7531,7703,8174,8411,8497,8559,8620,8678,8742,8803,8857,8974,9031,9091,9145,9220,9347,9431,9511,9606,9690,9768,9898,9982,10060,10194,10285,10366,10417,10468,10534,10602,10678,10749,10829,10908,10983,11056,11132,11238,11327,11404,11495,11589,11663,11733,11826,11875,11956,12022,12107,12193,12255,12319,12382,12453,12552,12657,12755,12860,12915,12970,13363,13711,13790,13936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "306", "startColumns": "4", "startOffsets": "27838", "endColumns": "82", "endOffsets": "27916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3402,3499,3601,3700,3800,3903,4016,13941", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "3494,3596,3695,3795,3898,4011,4127,14037"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-vi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,449,570,675,836,962,1077,1177,1346,1449,1602,1728,1883,2028,2092,2152", "endColumns": "97,157,120,104,160,125,114,99,168,102,152,125,154,144,63,59,78", "endOffsets": "290,448,569,674,835,961,1076,1176,1345,1448,1601,1727,1882,2027,2091,2151,2230"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4686,4788,4950,5075,5184,5349,5479,5598,5830,6003,6110,6267,6397,6556,6705,6773,6837", "endColumns": "101,161,124,108,164,129,118,103,172,106,156,129,158,148,67,63,82", "endOffsets": "4783,4945,5070,5179,5344,5474,5593,5697,5998,6105,6262,6392,6551,6700,6768,6832,6915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,393,493,585,670,763,857,938,1008,1078,1168,1259,1331,1408,1474", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "196,282,388,488,580,665,758,852,933,1003,1073,1163,1254,1326,1403,1469,1583"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,471,472,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4425,4521,7037,7143,7318,8179,8264,12975,13069,13150,13220,13368,13458,13795,45238,45315,45381", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "4516,4602,7138,7238,7405,8259,8352,13064,13145,13215,13285,13453,13544,13862,45310,45376,45490"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,86", "endOffsets": "138,225"}, "to": {"startLines": "474,475", "startColumns": "4,4", "startOffsets": "45495,45583", "endColumns": "87,86", "endOffsets": "45578,45665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,222,304,400,495,583,670,827,923,1029,1126,1257,1320,1433,1503,1680,1882,2024,2111,2218,2309,2409,2582,2693,2801,3061,3182,3319,3518,3727,3828,3941,4138,4236,4295,4360,4446,4539,4642,4714,4820,4906,5025,5233,5298,5363,5445,5561,5643,5733,5798,5874,5950,6024,6156,6244,6351,6448,6516,6591,6656,6749,6806,6915,7023,7147,7272,7333,7431,7542,7669,7744,7837,7942,8006,8063,8155,8258,8437,8693,8975,9084,9156,9243,9318,9454,9578,9674,9745,9818,9899,9979,10078,10200,10330,10403,10469,10523,10695,10766,10825,10906,10986,11064,11148,11274,11402,11512,11599,11673,11769,11836", "endColumns": "76,89,81,95,94,87,86,156,95,105,96,130,62,112,69,176,201,141,86,106,90,99,172,110,107,259,120,136,198,208,100,112,196,97,58,64,85,92,102,71,105,85,118,207,64,64,81,115,81,89,64,75,75,73,131,87,106,96,67,74,64,92,56,108,107,123,124,60,97,110,126,74,92,104,63,56,91,102,178,255,281,108,71,86,74,135,123,95,70,72,80,79,98,121,129,72,65,53,171,70,58,80,79,77,83,125,127,109,86,73,95,66,165", "endOffsets": "127,217,299,395,490,578,665,822,918,1024,1121,1252,1315,1428,1498,1675,1877,2019,2106,2213,2304,2404,2577,2688,2796,3056,3177,3314,3513,3722,3823,3936,4133,4231,4290,4355,4441,4534,4637,4709,4815,4901,5020,5228,5293,5358,5440,5556,5638,5728,5793,5869,5945,6019,6151,6239,6346,6443,6511,6586,6651,6744,6801,6910,7018,7142,7267,7328,7426,7537,7664,7739,7832,7937,8001,8058,8150,8253,8432,8688,8970,9079,9151,9238,9313,9449,9573,9669,9740,9813,9894,9974,10073,10195,10325,10398,10464,10518,10690,10761,10820,10901,10981,11059,11143,11269,11397,11507,11594,11668,11764,11831,11997"}, "to": {"startLines": "164,165,166,236,248,249,250,267,280,282,283,314,332,334,337,341,342,343,346,348,350,351,352,353,354,355,356,357,358,359,360,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,409,413,423,424,425,426,427,428,429,430,431,440,441,442,443,444,445,446,447,448,449,450,451,452,453,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14505,14582,14672,20331,22179,22274,22362,24382,25686,25828,25934,28587,30481,30605,30850,31160,31337,31539,31821,32006,32213,32304,32404,32577,32688,32796,33056,33177,33314,33513,33722,33917,34030,34227,34325,34384,34449,34535,34628,34731,34803,34909,34995,35114,35322,35387,35452,35534,35650,35732,35822,35887,35963,36039,36113,36680,36768,36875,36972,37040,37115,37180,37273,37330,37439,37547,37671,37796,37857,37955,38066,38193,38496,38799,40543,40607,40664,40756,40859,41038,41294,41576,41685,42306,42393,42468,42604,42728,42824,42895,42968,43049,43129,43228,43350,43480,43553,43705,43759,43931,44002,44061,44142,44222,44300,44384,44510,44638,44748,44835,44909,45005,45072", "endColumns": "76,89,81,95,94,87,86,156,95,105,96,130,62,112,69,176,201,141,86,106,90,99,172,110,107,259,120,136,198,208,100,112,196,97,58,64,85,92,102,71,105,85,118,207,64,64,81,115,81,89,64,75,75,73,131,87,106,96,67,74,64,92,56,108,107,123,124,60,97,110,126,74,92,104,63,56,91,102,178,255,281,108,71,86,74,135,123,95,70,72,80,79,98,121,129,72,65,53,171,70,58,80,79,77,83,125,127,109,86,73,95,66,165", "endOffsets": "14577,14667,14749,20422,22269,22357,22444,24534,25777,25929,26026,28713,30539,30713,30915,31332,31534,31676,31903,32108,32299,32399,32572,32683,32791,33051,33172,33309,33508,33717,33818,34025,34222,34320,34379,34444,34530,34623,34726,34798,34904,34990,35109,35317,35382,35447,35529,35645,35727,35817,35882,35958,36034,36108,36240,36763,36870,36967,37035,37110,37175,37268,37325,37434,37542,37666,37791,37852,37950,38061,38188,38263,38584,38899,40602,40659,40751,40854,41033,41289,41571,41680,41752,42388,42463,42599,42723,42819,42890,42963,43044,43124,43223,43345,43475,43548,43614,43754,43926,43997,44056,44137,44217,44295,44379,44505,44633,44743,44830,44904,45000,45067,45233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,422,524,633,717,820,939,1017,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2114,2218,2326,2427,2532,2647,2752,2909,13549", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "417,519,628,712,815,934,1012,1088,1179,1272,1367,1461,1561,1654,1749,1843,1934,2025,2109,2213,2321,2422,2527,2642,2747,2904,3003,13629"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-vi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "123", "endOffsets": "318"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5702", "endColumns": "127", "endOffsets": "5825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,192,254,324,403,459,518,570,655,736,818,916,1014,1100,1180,1264,1345,1441,1534,1604,1694,1777,1864,1971,2056,2141,2222,2323,2406,2499,2588,2673,2757,2865,2945,3031,3129,3220,3295,3356,4064,4748,4823,4938,5043,5098,5189,5278,5345,5442,5539,5595,5681,5727,5811,5913,5977,6051,6118,6184,6230,6314,6412,6493,6538,6582,6645,6707,6769,6967,7147,7275,7344,7444,7516,7611,7695,7776,7859,7955,8027,8146,8225,8321,8376,8485,8531,8584,8654,8715,8783,8857,8936,9028,9094,9145", "endColumns": "63,72,61,69,78,55,58,51,84,80,81,97,97,85,79,83,80,95,92,69,89,82,86,106,84,84,80,100,82,92,88,84,83,107,79,85,97,90,74,60,707,683,74,114,104,54,90,88,66,96,96,55,85,45,83,101,63,73,66,65,45,83,97,80,44,43,62,61,61,197,179,127,68,99,71,94,83,80,82,95,71,118,78,95,54,108,45,52,69,60,67,73,78,91,65,50,85", "endOffsets": "114,187,249,319,398,454,513,565,650,731,813,911,1009,1095,1175,1259,1340,1436,1529,1599,1689,1772,1859,1966,2051,2136,2217,2318,2401,2494,2583,2668,2752,2860,2940,3026,3124,3215,3290,3351,4059,4743,4818,4933,5038,5093,5184,5273,5340,5437,5534,5590,5676,5722,5806,5908,5972,6046,6113,6179,6225,6309,6407,6488,6533,6577,6640,6702,6764,6962,7142,7270,7339,7439,7511,7606,7690,7771,7854,7950,8022,8141,8220,8316,8371,8480,8526,8579,8649,8710,8778,8852,8931,9023,9089,9140,9226"}, "to": {"startLines": "157,158,159,160,161,162,163,167,168,169,170,173,175,176,178,183,187,202,205,206,207,209,210,211,213,217,218,219,220,221,222,223,224,225,226,228,231,232,238,239,240,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,274,275,276,277,281,286,287,288,289,290,295,296,297,298,299,300,304,305,315,316,318,319,323,324,326,331,338,339,410,411,412,414,419,432,433,434,435,436,437,438,454", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14042,14106,14179,14241,14311,14390,14446,14754,14806,14891,14972,15197,15373,15471,15633,16001,16301,17324,17575,17668,17738,17891,17974,18061,18233,18532,18617,18698,18799,18882,18975,19064,19149,19233,19341,19500,19752,19850,20667,20742,20803,22449,23133,23208,23323,23428,23483,23574,23663,23730,23827,23924,23980,24603,24649,24733,25015,25079,25153,25220,25782,26171,26255,26353,26434,26479,26778,26841,26903,26965,27163,27343,27669,27738,28718,28790,28971,29055,29423,29506,29675,30362,30920,30999,38589,38644,38753,38904,39483,41757,41818,41886,41960,42039,42131,42197,43619", "endColumns": "63,72,61,69,78,55,58,51,84,80,81,97,97,85,79,83,80,95,92,69,89,82,86,106,84,84,80,100,82,92,88,84,83,107,79,85,97,90,74,60,707,683,74,114,104,54,90,88,66,96,96,55,85,45,83,101,63,73,66,65,45,83,97,80,44,43,62,61,61,197,179,127,68,99,71,94,83,80,82,95,71,118,78,95,54,108,45,52,69,60,67,73,78,91,65,50,85", "endOffsets": "14101,14174,14236,14306,14385,14441,14500,14801,14886,14967,15049,15290,15466,15552,15708,16080,16377,17415,17663,17733,17823,17969,18056,18163,18313,18612,18693,18794,18877,18970,19059,19144,19228,19336,19416,19581,19845,19936,20737,20798,21506,23128,23203,23318,23423,23478,23569,23658,23725,23822,23919,23975,24061,24644,24728,24830,25074,25148,25215,25281,25823,26250,26348,26429,26474,26518,26836,26898,26960,27158,27338,27466,27733,27833,28785,28880,29050,29131,29501,29597,29742,30476,30994,31090,38639,38748,38794,38952,39548,41813,41881,41955,42034,42126,42192,42243,43700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-vi\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,75", "endOffsets": "258,334"}, "to": {"startLines": "81,476", "startColumns": "4,4", "startOffsets": "8044,45670", "endColumns": "60,79", "endOffsets": "8100,45745"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,197,263,352,413,479,537,613,686,749,814", "endColumns": "77,63,65,88,60,65,57,75,72,62,64,64", "endOffsets": "128,192,258,347,408,474,532,608,681,744,809,874"}, "to": {"startLines": "172,182,184,185,186,190,198,201,204,208,212,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15119,15937,16085,16151,16240,16520,17056,17248,17502,17828,18168,18467", "endColumns": "77,63,65,88,60,65,57,75,72,62,64,64", "endOffsets": "15192,15996,16146,16235,16296,16581,17109,17319,17570,17886,18228,18527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,341,445,685,734,802,902,981,1207,1268,1353,1423,1476,1540,1622,1720,2069,2143,2209,2267,2325,2409,2545,2662,2724,2812,2898,2986,3059,3161,3514,3594,3674,3735,3800,3867,3932,3998,4072,4170,4270,4364,4466,4562,4634,4713,4799,5027,5258,5366,5499,5553,6376,6481,6543", "endColumns": "115,169,103,239,48,67,99,78,225,60,84,69,52,63,81,97,348,73,65,57,57,83,135,116,61,87,85,87,72,101,352,79,79,60,64,66,64,65,73,97,99,93,101,95,71,78,85,227,230,107,132,53,822,104,61,57", "endOffsets": "166,336,440,680,729,797,897,976,1202,1263,1348,1418,1471,1535,1617,1715,2064,2138,2204,2262,2320,2404,2540,2657,2719,2807,2893,2981,3054,3156,3509,3589,3669,3730,3795,3862,3927,3993,4067,4165,4265,4359,4461,4557,4629,4708,4794,5022,5253,5361,5494,5548,6371,6476,6538,6596"}, "to": {"startLines": "233,234,235,237,241,242,243,244,245,246,247,263,266,268,272,273,278,284,285,293,303,307,308,309,310,311,317,320,325,327,328,329,330,333,335,336,340,344,345,347,349,361,386,387,388,389,390,408,415,416,417,418,420,421,422,439", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19941,20057,20227,20427,21511,21560,21628,21728,21807,22033,22094,24066,24329,24539,24835,24917,25286,26031,26105,26653,27611,27921,28005,28141,28258,28320,28885,29136,29602,29747,29849,30202,30282,30544,30718,30783,31095,31681,31747,31908,32113,33823,36245,36347,36443,36515,36594,38268,38957,39188,39296,39429,39553,40376,40481,42248", "endColumns": "115,169,103,239,48,67,99,78,225,60,84,69,52,63,81,97,348,73,65,57,57,83,135,116,61,87,85,87,72,101,352,79,79,60,64,66,64,65,73,97,99,93,101,95,71,78,85,227,230,107,132,53,822,104,61,57", "endOffsets": "20052,20222,20326,20662,21555,21623,21723,21802,22028,22089,22174,24131,24377,24598,24912,25010,25630,26100,26166,26706,27664,28000,28136,28253,28315,28403,28966,29219,29670,29844,30197,30277,30357,30600,30778,30845,31155,31742,31816,32001,32208,33912,36342,36438,36510,36589,36675,38491,39183,39291,39424,39478,40371,40476,40538,42301"}}]}]}
<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright 2017 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      https://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<selector xmlns:android="http://schemas.android.com/apk/res/android">

  <!-- 87% opacity. -->
  <item android:alpha="0.87" android:color="?attr/colorOnSurface" android:state_enabled="true"/>
  <!-- 38% of 87% opacity. -->
  <item android:alpha="0.33" android:color="?attr/colorOnSurface"/>

</selector>

{"logs": [{"outputFile": "com.pichillilorenzo.flutter_inappwebview_android-release-32:/values-v16/values-v16.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f1cde390e1d87141b64865ab32de83e\\transformed\\appcompat-1.6.1\\res\\values-v16\\values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "223"}}]}, {"outputFile": "com.pichillilorenzo.flutter_inappwebview_android-mergeReleaseResources-30:/values-v16/values-v16.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f1cde390e1d87141b64865ab32de83e\\transformed\\appcompat-1.6.1\\res\\values-v16\\values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "223"}}]}]}
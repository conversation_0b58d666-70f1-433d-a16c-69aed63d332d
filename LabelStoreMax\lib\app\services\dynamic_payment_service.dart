//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import '/app/models/payment_type.dart';
import '/app/services/woocommerce_service.dart';

/// Service to dynamically fetch and manage payment gateways from WooCommerce
class DynamicPaymentService {
  static final DynamicPaymentService _instance = DynamicPaymentService._internal();
  factory DynamicPaymentService() => _instance;
  DynamicPaymentService._internal();

  final WooCommerceService _wooService = WooCommerceService();
  List<PaymentType>? _cachedPaymentTypes;

  /// Fetch active payment gateways from WooCommerce API
  Future<List<PaymentType>> getPaymentGateways({bool forceRefresh = false}) async {
    print('=== Dynamic Payment Gateways Fetch ===');
    
    // Return cached data if available and not forcing refresh
    if (_cachedPaymentTypes != null && !forceRefresh) {
      print('✅ Returning cached payment gateways: ${_cachedPaymentTypes!.length}');
      return _cachedPaymentTypes!;
    }

    try {
      // Fetch payment gateways from WooCommerce API
      final response = await _wooService.wooCommerce.dio.get('/payment_gateways');
      
      print('✅ Payment Gateways API Success');
      print('Total gateways found: ${response.data.length}');
      
      List<PaymentType> paymentTypes = [];
      
      for (var gateway in response.data) {
        // Only include enabled gateways
        if (gateway['enabled'] == true) {
          PaymentType paymentType = PaymentType.fromWooGateway(gateway);
          paymentTypes.add(paymentType);
          
          print('✅ Added payment gateway: ${paymentType.name} (${paymentType.description})');
        } else {
          print('⚠️ Skipped disabled gateway: ${gateway['id']} - ${gateway['title']}');
        }
      }
      
      // Cache the results
      _cachedPaymentTypes = paymentTypes;
      
      print('✅ Dynamic payment gateways loaded: ${paymentTypes.length} active gateways');
      return paymentTypes;
      
    } catch (e) {
      print('❌ Failed to fetch payment gateways: $e');
      
      // Return empty list on error
      return [];
    }
  }

  /// Clear cached payment gateways
  void clearCache() {
    _cachedPaymentTypes = null;
    print('🗑️ Payment gateways cache cleared');
  }

  /// Get a specific payment gateway by ID
  Future<PaymentType?> getPaymentGateway(String gatewayId) async {
    final gateways = await getPaymentGateways();
    try {
      return gateways.firstWhere((gateway) => gateway.id == gatewayId);
    } catch (e) {
      print('❌ Payment gateway not found: $gatewayId');
      return null;
    }
  }

  /// Check if a specific payment gateway is available
  Future<bool> isPaymentGatewayAvailable(String gatewayId) async {
    final gateway = await getPaymentGateway(gatewayId);
    return gateway != null;
  }
}

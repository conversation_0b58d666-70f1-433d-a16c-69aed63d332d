{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-nn-rNO/values-nn-rNO.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,192,254,339,401,471,530,606,678,743,803", "endColumns": "77,58,61,84,61,69,58,75,71,64,59,68", "endOffsets": "128,187,249,334,396,466,525,601,673,738,798,867"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1653,2447,2583,2645,2730,3006,3534,3726,3971,4289,4621,4912", "endColumns": "77,58,61,84,61,69,58,75,71,64,59,68", "endOffsets": "1726,2501,2640,2725,2787,3071,3588,3797,4038,4349,4676,4976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,210,293,387,473,550,627,746,840,944,1033,1155,1214,1330,1394,1570,1760,1902,1993,2102,2195,2292,2468,2571,2667,2887,2992,3116,3309,3522,3618,3729,3914,4006,4065,4130,4218,4315,4407,4480,4576,4661,4766,4962,5031,5098,5170,5287,5372,5462,5528,5610,5681,5753,5872,5958,6059,6149,6220,6299,6366,6450,6509,6610,6720,6825,6955,7015,7105,7198,7326,7398,7485,7584,7638,7696,7770,7870,8029,8301,8595,8691,8763,8849,8921,9029,9139,9225,9299,9367,9452,9539,9630,9736,9860,9928,10001,10055,10223,10292,10351,10424,10498,10576,10658,10792,10920,11034,11119,11194,11284,11352", "endColumns": "70,83,82,93,85,76,76,118,93,103,88,121,58,115,63,175,189,141,90,108,92,96,175,102,95,219,104,123,192,212,95,110,184,91,58,64,87,96,91,72,95,84,104,195,68,66,71,116,84,89,65,81,70,71,118,85,100,89,70,78,66,83,58,100,109,104,129,59,89,92,127,71,86,98,53,57,73,99,158,271,293,95,71,85,71,107,109,85,73,67,84,86,90,105,123,67,72,53,167,68,58,72,73,77,81,133,127,113,84,74,89,67,155", "endOffsets": "121,205,288,382,468,545,622,741,835,939,1028,1150,1209,1325,1389,1565,1755,1897,1988,2097,2190,2287,2463,2566,2662,2882,2987,3111,3304,3517,3613,3724,3909,4001,4060,4125,4213,4310,4402,4475,4571,4656,4761,4957,5026,5093,5165,5282,5367,5457,5523,5605,5676,5748,5867,5953,6054,6144,6215,6294,6361,6445,6504,6605,6715,6820,6950,7010,7100,7193,7321,7393,7480,7579,7633,7691,7765,7865,8024,8296,8590,8686,8758,8844,8916,9024,9134,9220,9294,9362,9447,9534,9625,9731,9855,9923,9996,10050,10218,10287,10346,10419,10493,10571,10653,10787,10915,11029,11114,11189,11279,11347,11503"}, "to": {"startLines": "16,17,18,88,100,101,102,119,132,134,135,165,183,185,188,192,193,194,197,199,201,202,203,204,205,206,207,208,209,210,211,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,260,264,274,275,276,277,278,279,280,281,282,291,292,293,294,295,296,297,298,299,300,301,302,303,304,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1056,1127,1211,6745,8565,8651,8728,10706,11912,12052,12156,14618,16369,16489,16727,17021,17197,17387,17680,17871,18073,18166,18263,18439,18542,18638,18858,18963,19087,19280,19493,19682,19793,19978,20070,20129,20194,20282,20379,20471,20544,20640,20725,20830,21026,21095,21162,21234,21351,21436,21526,21592,21674,21745,21817,22371,22457,22558,22648,22719,22798,22865,22949,23008,23109,23219,23324,23454,23514,23604,23697,23825,24098,24418,25957,26011,26069,26143,26243,26402,26674,26968,27064,27662,27748,27820,27928,28038,28124,28198,28266,28351,28438,28529,28635,28759,28827,28978,29032,29200,29269,29328,29401,29475,29553,29635,29769,29897,30011,30096,30171,30261,30329", "endColumns": "70,83,82,93,85,76,76,118,93,103,88,121,58,115,63,175,189,141,90,108,92,96,175,102,95,219,104,123,192,212,95,110,184,91,58,64,87,96,91,72,95,84,104,195,68,66,71,116,84,89,65,81,70,71,118,85,100,89,70,78,66,83,58,100,109,104,129,59,89,92,127,71,86,98,53,57,73,99,158,271,293,95,71,85,71,107,109,85,73,67,84,86,90,105,123,67,72,53,167,68,58,72,73,77,81,133,127,113,84,74,89,67,155", "endOffsets": "1122,1206,1289,6834,8646,8723,8800,10820,12001,12151,12240,14735,16423,16600,16786,17192,17382,17524,17766,17975,18161,18258,18434,18537,18633,18853,18958,19082,19275,19488,19584,19788,19973,20065,20124,20189,20277,20374,20466,20539,20635,20720,20825,21021,21090,21157,21229,21346,21431,21521,21587,21669,21740,21812,21931,22452,22553,22643,22714,22793,22860,22944,23003,23104,23214,23319,23449,23509,23599,23692,23820,23892,24180,24512,26006,26064,26138,26238,26397,26669,26963,27059,27131,27743,27815,27923,28033,28119,28193,28261,28346,28433,28524,28630,28754,28822,28895,29027,29195,29264,29323,29396,29470,29548,29630,29764,29892,30006,30091,30166,30256,30324,30480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,244,311,378,442,514", "endColumns": "86,101,66,66,63,71,66", "endOffsets": "137,239,306,373,437,509,576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,262,330,407,467,530,583,661,743,824,920,1016,1099,1181,1258,1338,1432,1522,1591,1678,1761,1844,1945,2023,2110,2186,2280,2359,2455,2548,2637,2721,2817,2890,2969,3073,3170,3240,3305,3988,4647,4721,4839,4934,4994,5097,5192,5259,5349,5437,5494,5572,5621,5697,5793,5861,5932,5999,6065,6111,6188,6280,6359,6406,6454,6521,6579,6639,6804,6969,7087,7153,7236,7311,7411,7496,7583,7661,7749,7826,7929,8014,8099,8152,8284,8332,8385,8454,8520,8590,8654,8726,8807,8874,8925", "endColumns": "67,76,61,67,76,59,62,52,77,81,80,95,95,82,81,76,79,93,89,68,86,82,82,100,77,86,75,93,78,95,92,88,83,95,72,78,103,96,69,64,682,658,73,117,94,59,102,94,66,89,87,56,77,48,75,95,67,70,66,65,45,76,91,78,46,47,66,57,59,164,164,117,65,82,74,99,84,86,77,87,76,102,84,84,52,131,47,52,68,65,69,63,71,80,66,50,77", "endOffsets": "118,195,257,325,402,462,525,578,656,738,819,915,1011,1094,1176,1253,1333,1427,1517,1586,1673,1756,1839,1940,2018,2105,2181,2275,2354,2450,2543,2632,2716,2812,2885,2964,3068,3165,3235,3300,3983,4642,4716,4834,4929,4989,5092,5187,5254,5344,5432,5489,5567,5616,5692,5788,5856,5927,5994,6060,6106,6183,6275,6354,6401,6449,6516,6574,6634,6799,6964,7082,7148,7231,7306,7406,7491,7578,7656,7744,7821,7924,8009,8094,8147,8279,8327,8380,8449,8515,8585,8649,8721,8802,8869,8920,8998"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,84,90,91,92,103,104,105,106,107,108,109,110,111,112,113,114,121,122,123,126,127,128,129,133,138,139,140,141,142,147,148,149,150,151,152,156,157,166,167,169,170,174,175,177,182,189,190,261,262,263,265,270,283,284,285,286,287,288,289,305", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "581,649,726,788,856,933,993,1294,1347,1425,1507,1731,1905,2001,2151,2506,2792,3802,4043,4133,4202,4354,4437,4520,4681,4981,5068,5144,5238,5317,5413,5506,5595,5679,5775,5922,6177,6281,7052,7122,7187,8805,9464,9538,9656,9751,9811,9914,10009,10076,10166,10254,10311,10889,10938,11014,11276,11344,11415,11482,12006,12386,12463,12555,12634,12681,12975,13042,13100,13160,13325,13490,13802,13868,14740,14815,14991,15076,15431,15509,15662,16266,16791,16876,24185,24238,24370,24517,25057,27136,27202,27272,27336,27408,27489,27556,28900", "endColumns": "67,76,61,67,76,59,62,52,77,81,80,95,95,82,81,76,79,93,89,68,86,82,82,100,77,86,75,93,78,95,92,88,83,95,72,78,103,96,69,64,682,658,73,117,94,59,102,94,66,89,87,56,77,48,75,95,67,70,66,65,45,76,91,78,46,47,66,57,59,164,164,117,65,82,74,99,84,86,77,87,76,102,84,84,52,131,47,52,68,65,69,63,71,80,66,50,77", "endOffsets": "644,721,783,851,928,988,1051,1342,1420,1502,1583,1822,1996,2079,2228,2578,2867,3891,4128,4197,4284,4432,4515,4616,4754,5063,5139,5233,5312,5408,5501,5590,5674,5770,5843,5996,6276,6373,7117,7182,7865,9459,9533,9651,9746,9806,9909,10004,10071,10161,10249,10306,10384,10933,11009,11105,11339,11410,11477,11543,12047,12458,12550,12629,12676,12724,13037,13095,13155,13320,13485,13603,13863,13946,14810,14910,15071,15158,15504,15592,15734,16364,16871,16956,24233,24365,24413,24565,25121,27197,27267,27331,27403,27484,27551,27602,28973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,351,418,479,546,613,677,745,815,875,934,1007,1071,1141,1204,1279,1343,1432,1506,1591,1682,1790,1878,1926,1974,2055,2119,2191,2260,2359,2445,2537", "endColumns": "64,77,66,85,66,60,66,66,63,67,69,59,58,72,63,69,62,74,63,88,73,84,90,107,87,47,47,80,63,71,68,98,85,91,92", "endOffsets": "115,193,260,346,413,474,541,608,672,740,810,870,929,1002,1066,1136,1199,1274,1338,1427,1501,1586,1677,1785,1873,1921,1969,2050,2114,2186,2255,2354,2440,2532,2625"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,116,117,131,143,144,146,153,154,163,164,172,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1588,1827,2084,2233,2319,2386,2872,2939,3076,3140,3208,3278,3338,3397,3470,3593,3663,3896,4759,4823,5848,6001,6086,10455,10563,11864,12729,12777,12911,13608,13680,14433,14532,15246,15338", "endColumns": "64,77,66,85,66,60,66,66,63,67,69,59,58,72,63,69,62,74,63,88,73,84,90,107,87,47,47,80,63,71,68,98,85,91,92", "endOffsets": "1648,1900,2146,2314,2381,2442,2934,3001,3135,3203,3273,3333,3392,3465,3529,3658,3721,3966,4818,4907,5917,6081,6172,10558,10646,11907,12772,12853,12970,13675,13744,14527,14613,15333,15426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,326,422,635,683,750,852,921,1171,1236,1330,1396,1451,1515,1590,1681,1997,2072,2138,2191,2244,2339,2471,2585,2642,2726,2802,2885,2950,3050,3319,3397,3477,3538,3598,3660,3720,3791,3871,3971,4064,4157,4255,4352,4425,4504,4592,4793,4988,5102,5224,5280,5952,6049,6111", "endColumns": "113,156,95,212,47,66,101,68,249,64,93,65,54,63,74,90,315,74,65,52,52,94,131,113,56,83,75,82,64,99,268,77,79,60,59,61,59,70,79,99,92,92,97,96,72,78,87,200,194,113,121,55,671,96,61,54", "endOffsets": "164,321,417,630,678,745,847,916,1166,1231,1325,1391,1446,1510,1585,1676,1992,2067,2133,2186,2239,2334,2466,2580,2637,2721,2797,2880,2945,3045,3314,3392,3472,3533,3593,3655,3715,3786,3866,3966,4059,4152,4250,4347,4420,4499,4587,4788,4983,5097,5219,5275,5947,6044,6106,6161"}, "to": {"startLines": "85,86,87,89,93,94,95,96,97,98,99,115,118,120,124,125,130,136,137,145,155,158,159,160,161,162,168,171,176,178,179,180,181,184,186,187,191,195,196,198,200,212,237,238,239,240,241,259,266,267,268,269,271,272,273,290", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6378,6492,6649,6839,7870,7918,7985,8087,8156,8406,8471,10389,10651,10825,11110,11185,11548,12245,12320,12858,13749,13951,14046,14178,14292,14349,14915,15163,15597,15739,15839,16108,16186,16428,16605,16665,16961,17529,17600,17771,17980,19589,21936,22034,22131,22204,22283,23897,24570,24765,24879,25001,25126,25798,25895,27607", "endColumns": "113,156,95,212,47,66,101,68,249,64,93,65,54,63,74,90,315,74,65,52,52,94,131,113,56,83,75,82,64,99,268,77,79,60,59,61,59,70,79,99,92,92,97,96,72,78,87,200,194,113,121,55,671,96,61,54", "endOffsets": "6487,6644,6740,7047,7913,7980,8082,8151,8401,8466,8560,10450,10701,10884,11180,11271,11859,12315,12381,12906,13797,14041,14173,14287,14344,14428,14986,15241,15657,15834,16103,16181,16261,16484,16660,16722,17016,17595,17675,17866,18068,19677,22029,22126,22199,22278,22366,24093,24760,24874,24996,25052,25793,25890,25952,27657"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-nn-rNO/values-nn-rNO.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,192,254,339,401,471,530,606,678,743,803", "endColumns": "77,58,61,84,61,69,58,75,71,64,59,68", "endOffsets": "128,187,249,334,396,466,525,601,673,738,798,867"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1653,2447,2583,2645,2730,3006,3534,3726,3971,4289,4621,4912", "endColumns": "77,58,61,84,61,69,58,75,71,64,59,68", "endOffsets": "1726,2501,2640,2725,2787,3071,3588,3797,4038,4349,4676,4976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,210,293,387,473,550,627,746,840,944,1033,1155,1214,1330,1394,1570,1760,1902,1993,2102,2195,2292,2468,2571,2667,2887,2992,3116,3309,3522,3618,3729,3914,4006,4065,4130,4218,4315,4407,4480,4576,4661,4766,4962,5031,5098,5170,5287,5372,5462,5528,5610,5681,5753,5872,5958,6059,6149,6220,6299,6366,6450,6509,6610,6720,6825,6955,7015,7105,7198,7326,7398,7485,7584,7638,7696,7770,7870,8029,8301,8595,8691,8763,8849,8921,9029,9139,9225,9299,9367,9452,9539,9630,9736,9860,9928,10001,10055,10223,10292,10351,10424,10498,10576,10658,10792,10920,11034,11119,11194,11284,11352", "endColumns": "70,83,82,93,85,76,76,118,93,103,88,121,58,115,63,175,189,141,90,108,92,96,175,102,95,219,104,123,192,212,95,110,184,91,58,64,87,96,91,72,95,84,104,195,68,66,71,116,84,89,65,81,70,71,118,85,100,89,70,78,66,83,58,100,109,104,129,59,89,92,127,71,86,98,53,57,73,99,158,271,293,95,71,85,71,107,109,85,73,67,84,86,90,105,123,67,72,53,167,68,58,72,73,77,81,133,127,113,84,74,89,67,155", "endOffsets": "121,205,288,382,468,545,622,741,835,939,1028,1150,1209,1325,1389,1565,1755,1897,1988,2097,2190,2287,2463,2566,2662,2882,2987,3111,3304,3517,3613,3724,3909,4001,4060,4125,4213,4310,4402,4475,4571,4656,4761,4957,5026,5093,5165,5282,5367,5457,5523,5605,5676,5748,5867,5953,6054,6144,6215,6294,6361,6445,6504,6605,6715,6820,6950,7010,7100,7193,7321,7393,7480,7579,7633,7691,7765,7865,8024,8296,8590,8686,8758,8844,8916,9024,9134,9220,9294,9362,9447,9534,9625,9731,9855,9923,9996,10050,10218,10287,10346,10419,10493,10571,10653,10787,10915,11029,11114,11189,11279,11347,11503"}, "to": {"startLines": "16,17,18,88,100,101,102,119,132,134,135,165,183,185,188,192,193,194,197,199,201,202,203,204,205,206,207,208,209,210,211,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,260,264,274,275,276,277,278,279,280,281,282,291,292,293,294,295,296,297,298,299,300,301,302,303,304,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1056,1127,1211,6745,8565,8651,8728,10706,11912,12052,12156,14618,16369,16489,16727,17021,17197,17387,17680,17871,18073,18166,18263,18439,18542,18638,18858,18963,19087,19280,19493,19682,19793,19978,20070,20129,20194,20282,20379,20471,20544,20640,20725,20830,21026,21095,21162,21234,21351,21436,21526,21592,21674,21745,21817,22371,22457,22558,22648,22719,22798,22865,22949,23008,23109,23219,23324,23454,23514,23604,23697,23825,24098,24418,25957,26011,26069,26143,26243,26402,26674,26968,27064,27662,27748,27820,27928,28038,28124,28198,28266,28351,28438,28529,28635,28759,28827,28978,29032,29200,29269,29328,29401,29475,29553,29635,29769,29897,30011,30096,30171,30261,30329", "endColumns": "70,83,82,93,85,76,76,118,93,103,88,121,58,115,63,175,189,141,90,108,92,96,175,102,95,219,104,123,192,212,95,110,184,91,58,64,87,96,91,72,95,84,104,195,68,66,71,116,84,89,65,81,70,71,118,85,100,89,70,78,66,83,58,100,109,104,129,59,89,92,127,71,86,98,53,57,73,99,158,271,293,95,71,85,71,107,109,85,73,67,84,86,90,105,123,67,72,53,167,68,58,72,73,77,81,133,127,113,84,74,89,67,155", "endOffsets": "1122,1206,1289,6834,8646,8723,8800,10820,12001,12151,12240,14735,16423,16600,16786,17192,17382,17524,17766,17975,18161,18258,18434,18537,18633,18853,18958,19082,19275,19488,19584,19788,19973,20065,20124,20189,20277,20374,20466,20539,20635,20720,20825,21021,21090,21157,21229,21346,21431,21521,21587,21669,21740,21812,21931,22452,22553,22643,22714,22793,22860,22944,23003,23104,23214,23319,23449,23509,23599,23692,23820,23892,24180,24512,26006,26064,26138,26238,26397,26669,26963,27059,27131,27743,27815,27923,28033,28119,28193,28261,28346,28433,28524,28630,28754,28822,28895,29027,29195,29264,29323,29396,29470,29548,29630,29764,29892,30006,30091,30166,30256,30324,30480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,244,311,378,442,514", "endColumns": "86,101,66,66,63,71,66", "endOffsets": "137,239,306,373,437,509,576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,262,330,407,467,530,583,661,743,824,920,1016,1099,1181,1258,1338,1432,1522,1591,1678,1761,1844,1945,2023,2110,2186,2280,2359,2455,2548,2637,2721,2817,2890,2969,3073,3170,3240,3305,3988,4647,4721,4839,4934,4994,5097,5192,5259,5349,5437,5494,5572,5621,5697,5793,5861,5932,5999,6065,6111,6188,6280,6359,6406,6454,6521,6579,6639,6804,6969,7087,7153,7236,7311,7411,7496,7583,7661,7749,7826,7929,8014,8099,8152,8284,8332,8385,8454,8520,8590,8654,8726,8807,8874,8925", "endColumns": "67,76,61,67,76,59,62,52,77,81,80,95,95,82,81,76,79,93,89,68,86,82,82,100,77,86,75,93,78,95,92,88,83,95,72,78,103,96,69,64,682,658,73,117,94,59,102,94,66,89,87,56,77,48,75,95,67,70,66,65,45,76,91,78,46,47,66,57,59,164,164,117,65,82,74,99,84,86,77,87,76,102,84,84,52,131,47,52,68,65,69,63,71,80,66,50,77", "endOffsets": "118,195,257,325,402,462,525,578,656,738,819,915,1011,1094,1176,1253,1333,1427,1517,1586,1673,1756,1839,1940,2018,2105,2181,2275,2354,2450,2543,2632,2716,2812,2885,2964,3068,3165,3235,3300,3983,4642,4716,4834,4929,4989,5092,5187,5254,5344,5432,5489,5567,5616,5692,5788,5856,5927,5994,6060,6106,6183,6275,6354,6401,6449,6516,6574,6634,6799,6964,7082,7148,7231,7306,7406,7491,7578,7656,7744,7821,7924,8009,8094,8147,8279,8327,8380,8449,8515,8585,8649,8721,8802,8869,8920,8998"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,84,90,91,92,103,104,105,106,107,108,109,110,111,112,113,114,121,122,123,126,127,128,129,133,138,139,140,141,142,147,148,149,150,151,152,156,157,166,167,169,170,174,175,177,182,189,190,261,262,263,265,270,283,284,285,286,287,288,289,305", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "581,649,726,788,856,933,993,1294,1347,1425,1507,1731,1905,2001,2151,2506,2792,3802,4043,4133,4202,4354,4437,4520,4681,4981,5068,5144,5238,5317,5413,5506,5595,5679,5775,5922,6177,6281,7052,7122,7187,8805,9464,9538,9656,9751,9811,9914,10009,10076,10166,10254,10311,10889,10938,11014,11276,11344,11415,11482,12006,12386,12463,12555,12634,12681,12975,13042,13100,13160,13325,13490,13802,13868,14740,14815,14991,15076,15431,15509,15662,16266,16791,16876,24185,24238,24370,24517,25057,27136,27202,27272,27336,27408,27489,27556,28900", "endColumns": "67,76,61,67,76,59,62,52,77,81,80,95,95,82,81,76,79,93,89,68,86,82,82,100,77,86,75,93,78,95,92,88,83,95,72,78,103,96,69,64,682,658,73,117,94,59,102,94,66,89,87,56,77,48,75,95,67,70,66,65,45,76,91,78,46,47,66,57,59,164,164,117,65,82,74,99,84,86,77,87,76,102,84,84,52,131,47,52,68,65,69,63,71,80,66,50,77", "endOffsets": "644,721,783,851,928,988,1051,1342,1420,1502,1583,1822,1996,2079,2228,2578,2867,3891,4128,4197,4284,4432,4515,4616,4754,5063,5139,5233,5312,5408,5501,5590,5674,5770,5843,5996,6276,6373,7117,7182,7865,9459,9533,9651,9746,9806,9909,10004,10071,10161,10249,10306,10384,10933,11009,11105,11339,11410,11477,11543,12047,12458,12550,12629,12676,12724,13037,13095,13155,13320,13485,13603,13863,13946,14810,14910,15071,15158,15504,15592,15734,16364,16871,16956,24233,24365,24413,24565,25121,27197,27267,27331,27403,27484,27551,27602,28973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,351,418,479,546,613,677,745,815,875,934,1007,1071,1141,1204,1279,1343,1432,1506,1591,1682,1790,1878,1926,1974,2055,2119,2191,2260,2359,2445,2537", "endColumns": "64,77,66,85,66,60,66,66,63,67,69,59,58,72,63,69,62,74,63,88,73,84,90,107,87,47,47,80,63,71,68,98,85,91,92", "endOffsets": "115,193,260,346,413,474,541,608,672,740,810,870,929,1002,1066,1136,1199,1274,1338,1427,1501,1586,1677,1785,1873,1921,1969,2050,2114,2186,2255,2354,2440,2532,2625"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,116,117,131,143,144,146,153,154,163,164,172,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1588,1827,2084,2233,2319,2386,2872,2939,3076,3140,3208,3278,3338,3397,3470,3593,3663,3896,4759,4823,5848,6001,6086,10455,10563,11864,12729,12777,12911,13608,13680,14433,14532,15246,15338", "endColumns": "64,77,66,85,66,60,66,66,63,67,69,59,58,72,63,69,62,74,63,88,73,84,90,107,87,47,47,80,63,71,68,98,85,91,92", "endOffsets": "1648,1900,2146,2314,2381,2442,2934,3001,3135,3203,3273,3333,3392,3465,3529,3658,3721,3966,4818,4907,5917,6081,6172,10558,10646,11907,12772,12853,12970,13675,13744,14527,14613,15333,15426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,326,422,635,683,750,852,921,1171,1236,1330,1396,1451,1515,1590,1681,1997,2072,2138,2191,2244,2339,2471,2585,2642,2726,2802,2885,2950,3050,3319,3397,3477,3538,3598,3660,3720,3791,3871,3971,4064,4157,4255,4352,4425,4504,4592,4793,4988,5102,5224,5280,5952,6049,6111", "endColumns": "113,156,95,212,47,66,101,68,249,64,93,65,54,63,74,90,315,74,65,52,52,94,131,113,56,83,75,82,64,99,268,77,79,60,59,61,59,70,79,99,92,92,97,96,72,78,87,200,194,113,121,55,671,96,61,54", "endOffsets": "164,321,417,630,678,745,847,916,1166,1231,1325,1391,1446,1510,1585,1676,1992,2067,2133,2186,2239,2334,2466,2580,2637,2721,2797,2880,2945,3045,3314,3392,3472,3533,3593,3655,3715,3786,3866,3966,4059,4152,4250,4347,4420,4499,4587,4788,4983,5097,5219,5275,5947,6044,6106,6161"}, "to": {"startLines": "85,86,87,89,93,94,95,96,97,98,99,115,118,120,124,125,130,136,137,145,155,158,159,160,161,162,168,171,176,178,179,180,181,184,186,187,191,195,196,198,200,212,237,238,239,240,241,259,266,267,268,269,271,272,273,290", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6378,6492,6649,6839,7870,7918,7985,8087,8156,8406,8471,10389,10651,10825,11110,11185,11548,12245,12320,12858,13749,13951,14046,14178,14292,14349,14915,15163,15597,15739,15839,16108,16186,16428,16605,16665,16961,17529,17600,17771,17980,19589,21936,22034,22131,22204,22283,23897,24570,24765,24879,25001,25126,25798,25895,27607", "endColumns": "113,156,95,212,47,66,101,68,249,64,93,65,54,63,74,90,315,74,65,52,52,94,131,113,56,83,75,82,64,99,268,77,79,60,59,61,59,70,79,99,92,92,97,96,72,78,87,200,194,113,121,55,671,96,61,54", "endOffsets": "6487,6644,6740,7047,7913,7980,8082,8151,8401,8466,8560,10450,10701,10884,11180,11271,11859,12315,12381,12906,13797,14041,14173,14287,14344,14428,14986,15241,15657,15834,16103,16181,16261,16484,16660,16722,17016,17595,17675,17866,18068,19677,22029,22126,22199,22278,22366,24093,24760,24874,24996,25052,25793,25890,25952,27657"}}]}]}
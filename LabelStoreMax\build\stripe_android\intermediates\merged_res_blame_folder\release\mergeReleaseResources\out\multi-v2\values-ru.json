{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1383,1453", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1378,1448,1571"}, "to": {"startLines": "50,51,72,73,75,85,86,146,147,148,149,151,152,156,480,481,482", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4630,4723,7264,7362,7545,8415,8497,13307,13395,13477,13548,13698,13782,14121,46569,46653,46723", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "4718,4801,7357,7459,7632,8492,8582,13390,13472,13543,13613,13777,13864,14188,46648,46718,46841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "483,484", "startColumns": "4,4", "startOffsets": "46846,46934", "endColumns": "87,90", "endOffsets": "46929,47020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,222,299,408,514,597,680,822,931,1035,1110,1253,1310,1429,1502,1680,1893,2043,2134,2232,2327,2423,2626,2739,2836,3086,3184,3316,3557,3796,3901,4013,4216,4308,4370,4438,4529,4622,4725,4803,4902,4997,5101,5316,5390,5459,5534,5648,5732,5817,5888,5972,6046,6123,6271,6363,6471,6569,6643,6724,6793,6879,6942,7046,7181,7292,7426,7490,7583,7681,7819,7890,7982,8093,8152,8215,8317,8425,8611,8907,9218,9323,9396,9493,9566,9696,9814,9910,10004,10076,10162,10241,10331,10462,10597,10662,10738,10796,10982,11049,11112,11190,11262,11340,11423,11540,11664,11773,11862,11951,12039,12118", "endColumns": "76,89,76,108,105,82,82,141,108,103,74,142,56,118,72,177,212,149,90,97,94,95,202,112,96,249,97,131,240,238,104,111,202,91,61,67,90,92,102,77,98,94,103,214,73,68,74,113,83,84,70,83,73,76,147,91,107,97,73,80,68,85,62,103,134,110,133,63,92,97,137,70,91,110,58,62,101,107,185,295,310,104,72,96,72,129,117,95,93,71,85,78,89,130,134,64,75,57,185,66,62,77,71,77,82,116,123,108,88,88,87,78,156", "endOffsets": "127,217,294,403,509,592,675,817,926,1030,1105,1248,1305,1424,1497,1675,1888,2038,2129,2227,2322,2418,2621,2734,2831,3081,3179,3311,3552,3791,3896,4008,4211,4303,4365,4433,4524,4617,4720,4798,4897,4992,5096,5311,5385,5454,5529,5643,5727,5812,5883,5967,6041,6118,6266,6358,6466,6564,6638,6719,6788,6874,6937,7041,7176,7287,7421,7485,7578,7676,7814,7885,7977,8088,8147,8210,8312,8420,8606,8902,9213,9318,9391,9488,9561,9691,9809,9905,9999,10071,10157,10236,10326,10457,10592,10657,10733,10791,10977,11044,11107,11185,11257,11335,11418,11535,11659,11768,11857,11946,12034,12113,12270"}, "to": {"startLines": "173,174,175,245,257,258,259,276,289,291,292,323,341,343,346,350,351,352,355,357,359,360,361,362,363,364,365,366,367,368,369,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,418,422,432,433,434,435,436,437,438,439,440,449,450,451,452,453,454,455,456,457,458,459,460,461,462,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15450,15527,15617,21445,23351,23457,23540,25549,26813,26971,27075,29684,31551,31690,31934,32248,32426,32639,32943,33141,33338,33433,33529,33732,33845,33942,34192,34290,34422,34663,34902,35108,35220,35423,35515,35577,35645,35736,35829,35932,36010,36109,36204,36308,36523,36597,36666,36741,36855,36939,37024,37095,37179,37253,37330,37954,38046,38154,38252,38326,38407,38476,38562,38625,38729,38864,38975,39109,39173,39266,39364,39502,39760,40095,41762,41821,41884,41986,42094,42280,42576,42887,42992,43616,43713,43786,43916,44034,44130,44224,44296,44382,44461,44551,44682,44817,44882,45032,45090,45276,45343,45406,45484,45556,45634,45717,45834,45958,46067,46156,46245,46333,46412", "endColumns": "76,89,76,108,105,82,82,141,108,103,74,142,56,118,72,177,212,149,90,97,94,95,202,112,96,249,97,131,240,238,104,111,202,91,61,67,90,92,102,77,98,94,103,214,73,68,74,113,83,84,70,83,73,76,147,91,107,97,73,80,68,85,62,103,134,110,133,63,92,97,137,70,91,110,58,62,101,107,185,295,310,104,72,96,72,129,117,95,93,71,85,78,89,130,134,64,75,57,185,66,62,77,71,77,82,116,123,108,88,88,87,78,156", "endOffsets": "15522,15612,15689,21549,23452,23535,23618,25686,26917,27070,27145,29822,31603,31804,32002,32421,32634,32784,33029,33234,33428,33524,33727,33840,33937,34187,34285,34417,34658,34897,35002,35215,35418,35510,35572,35640,35731,35824,35927,36005,36104,36199,36303,36518,36592,36661,36736,36850,36934,37019,37090,37174,37248,37325,37473,38041,38149,38247,38321,38402,38471,38557,38620,38724,38859,38970,39104,39168,39261,39359,39497,39568,39847,40201,41816,41879,41981,42089,42275,42571,42882,42987,43060,43708,43781,43911,44029,44125,44219,44291,44377,44456,44546,44677,44812,44877,44953,45085,45271,45338,45401,45479,45551,45629,45712,45829,45953,46062,46151,46240,46328,46407,46564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5882", "endColumns": "156", "endOffsets": "6034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,272,354,450,515,586,641,712,801,875,980,1085,1173,1255,1340,1430,1532,1634,1708,1807,1900,1990,2105,2190,2289,2370,2476,2550,2643,2748,2842,2936,3037,3103,3174,3290,3399,3470,3534,4277,4954,5028,5134,5241,5296,5385,5471,5540,5627,5729,5785,5875,5924,6001,6108,6173,6244,6311,6377,6426,6506,6598,6673,6720,6774,6848,6906,6985,7164,7319,7452,7519,7613,7692,7789,7872,7953,8029,8118,8200,8308,8395,8489,8545,8682,8732,8787,8876,8943,9012,9082,9151,9239,9310,9361", "endColumns": "68,77,69,81,95,64,70,54,70,88,73,104,104,87,81,84,89,101,101,73,98,92,89,114,84,98,80,105,73,92,104,93,93,100,65,70,115,108,70,63,742,676,73,105,106,54,88,85,68,86,101,55,89,48,76,106,64,70,66,65,48,79,91,74,46,53,73,57,78,178,154,132,66,93,78,96,82,80,75,88,81,107,86,93,55,136,49,54,88,66,68,69,68,87,70,50,73", "endOffsets": "119,197,267,349,445,510,581,636,707,796,870,975,1080,1168,1250,1335,1425,1527,1629,1703,1802,1895,1985,2100,2185,2284,2365,2471,2545,2638,2743,2837,2931,3032,3098,3169,3285,3394,3465,3529,4272,4949,5023,5129,5236,5291,5380,5466,5535,5622,5724,5780,5870,5919,5996,6103,6168,6239,6306,6372,6421,6501,6593,6668,6715,6769,6843,6901,6980,7159,7314,7447,7514,7608,7687,7784,7867,7948,8024,8113,8195,8303,8390,8484,8540,8677,8727,8782,8871,8938,9007,9077,9146,9234,9305,9356,9430"}, "to": {"startLines": "166,167,168,169,170,171,172,176,177,178,179,182,184,185,187,192,196,211,214,215,216,218,219,220,222,226,227,228,229,230,231,232,233,234,235,237,240,241,247,248,249,260,261,262,263,264,265,266,267,268,269,270,271,278,279,280,283,284,285,286,290,295,296,297,298,299,304,305,306,307,308,309,313,314,324,325,327,328,332,333,335,340,347,348,419,420,421,423,428,441,442,443,444,445,446,447,463", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14919,14988,15066,15136,15218,15314,15379,15694,15749,15820,15909,16126,16311,16416,16571,16927,17226,18290,18557,18659,18733,18900,18993,19083,19258,19568,19667,19748,19854,19928,20021,20126,20220,20314,20415,20557,20814,20930,21766,21837,21901,23623,24300,24374,24480,24587,24642,24731,24817,24886,24973,25075,25131,25755,25804,25881,26156,26221,26292,26359,26922,27291,27371,27463,27538,27585,27893,27967,28025,28104,28283,28438,28770,28837,29827,29906,30085,30168,30532,30608,30782,31443,32007,32094,39852,39908,40045,40206,40798,43065,43132,43201,43271,43340,43428,43499,44958", "endColumns": "68,77,69,81,95,64,70,54,70,88,73,104,104,87,81,84,89,101,101,73,98,92,89,114,84,98,80,105,73,92,104,93,93,100,65,70,115,108,70,63,742,676,73,105,106,54,88,85,68,86,101,55,89,48,76,106,64,70,66,65,48,79,91,74,46,53,73,57,78,178,154,132,66,93,78,96,82,80,75,88,81,107,86,93,55,136,49,54,88,66,68,69,68,87,70,50,73", "endOffsets": "14983,15061,15131,15213,15309,15374,15445,15744,15815,15904,15978,16226,16411,16499,16648,17007,17311,18387,18654,18728,18827,18988,19078,19193,19338,19662,19743,19849,19923,20016,20121,20215,20309,20410,20476,20623,20925,21034,21832,21896,22639,24295,24369,24475,24582,24637,24726,24812,24881,24968,25070,25126,25216,25799,25876,25983,26216,26287,26354,26420,26966,27366,27458,27533,27580,27634,27962,28020,28099,28278,28433,28566,28832,28926,29901,29998,30163,30244,30603,30692,30859,31546,32089,32183,39903,40040,40090,40256,40882,43127,43196,43266,43335,43423,43494,43545,45027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,141,244,311,378,442,529", "endColumns": "85,102,66,66,63,86,71", "endOffsets": "136,239,306,373,437,524,596"}, "to": {"startLines": "159,160,161,162,163,164,165", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14373,14459,14562,14629,14696,14760,14847", "endColumns": "85,102,66,66,63,86,71", "endOffsets": "14454,14557,14624,14691,14755,14842,14914"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,195,259,344,409,481,539,616,693,761,821", "endColumns": "79,59,63,84,64,71,57,76,76,67,59,73", "endOffsets": "130,190,254,339,404,476,534,611,688,756,816,890"}, "to": {"startLines": "181,191,193,194,195,199,207,210,213,217,221,225", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16046,16867,17012,17076,17161,17452,18018,18213,18480,18832,19198,19494", "endColumns": "79,59,63,84,64,71,57,76,76,67,59,73", "endOffsets": "16121,16922,17071,17156,17221,17519,18071,18285,18552,18895,19253,19563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,359,461,673,719,786,885,960,1228,1288,1380,1459,1513,1577,1653,1745,2082,2155,2223,2276,2329,2408,2523,2634,2701,2780,2862,2946,3031,3150,3444,3533,3610,3692,3752,3817,3877,3949,4031,4138,4237,4338,4467,4565,4639,4718,4814,5001,5212,5343,5475,5538,6245,6349,6413", "endColumns": "128,174,101,211,45,66,98,74,267,59,91,78,53,63,75,91,336,72,67,52,52,78,114,110,66,78,81,83,84,118,293,88,76,81,59,64,59,71,81,106,98,100,128,97,73,78,95,186,210,130,131,62,706,103,63,65", "endOffsets": "179,354,456,668,714,781,880,955,1223,1283,1375,1454,1508,1572,1648,1740,2077,2150,2218,2271,2324,2403,2518,2629,2696,2775,2857,2941,3026,3145,3439,3528,3605,3687,3747,3812,3872,3944,4026,4133,4232,4333,4462,4560,4634,4713,4809,4996,5207,5338,5470,5533,6240,6344,6408,6474"}, "to": {"startLines": "242,243,244,246,250,251,252,253,254,255,256,272,275,277,281,282,287,293,294,302,312,316,317,318,319,320,326,329,334,336,337,338,339,342,344,345,349,353,354,356,358,370,395,396,397,398,399,417,424,425,426,427,429,430,431,448", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21039,21168,21343,21554,22644,22690,22757,22856,22931,23199,23259,25221,25495,25691,25988,26064,26425,27150,27223,27776,28717,29023,29102,29217,29328,29395,30003,30249,30697,30864,30983,31277,31366,31608,31809,31869,32188,32789,32861,33034,33239,35007,37478,37607,37705,37779,37858,39573,40261,40472,40603,40735,40887,41594,41698,43550", "endColumns": "128,174,101,211,45,66,98,74,267,59,91,78,53,63,75,91,336,72,67,52,52,78,114,110,66,78,81,83,84,118,293,88,76,81,59,64,59,71,81,106,98,100,128,97,73,78,95,186,210,130,131,62,706,103,63,65", "endOffsets": "21163,21338,21440,21761,22685,22752,22851,22926,23194,23254,23346,25295,25544,25750,26059,26151,26757,27218,27286,27824,28765,29097,29212,29323,29390,29469,30080,30328,30777,30978,31272,31361,31438,31685,31864,31929,32243,32856,32938,33136,33333,35103,37602,37700,37774,37853,37949,39755,40467,40598,40730,40793,41589,41693,41757,43611"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "71,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "7156,7955,8060,8172", "endColumns": "107,104,111,104", "endOffsets": "7259,8055,8167,8272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,198,265,349,418,479,551,615,680,766,836,908,971,1045,1109,1182,1246,1334,1399,1485,1561,1651,1747,1861,1942,1993,2044,2130,2194,2266,2340,2456,2550,2651", "endColumns": "62,79,66,83,68,60,71,63,64,85,69,71,62,73,63,72,63,87,64,85,75,89,95,113,80,50,50,85,63,71,73,115,93,100,97", "endOffsets": "113,193,260,344,413,474,546,610,675,761,831,903,966,1040,1104,1177,1241,1329,1394,1480,1556,1646,1742,1856,1937,1988,2039,2125,2189,2261,2335,2451,2545,2646,2744"}, "to": {"startLines": "180,183,186,188,189,190,197,198,200,201,202,203,204,205,206,208,209,212,223,224,236,238,239,273,274,288,300,301,303,310,311,321,322,330,331", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15983,16231,16504,16653,16737,16806,17316,17388,17524,17589,17675,17745,17817,17880,17954,18076,18149,18392,19343,19408,20481,20628,20718,25300,25414,26762,27639,27690,27829,28571,28643,29474,29590,30333,30434", "endColumns": "62,79,66,83,68,60,71,63,64,85,69,71,62,73,63,72,63,87,64,85,75,89,95,113,80,50,50,85,63,71,73,115,93,100,97", "endOffsets": "16041,16306,16566,16732,16801,16862,17383,17447,17584,17670,17740,17812,17875,17949,18013,18144,18208,18475,19403,19489,20552,20713,20809,25409,25490,26808,27685,27771,27888,28638,28712,29585,29679,30429,30527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4883,4990,5156,5282,5392,5534,5663,5778,6039,6220,6327,6490,6616,6783,6941,7010,7070", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "4985,5151,5277,5387,5529,5658,5773,5877,6215,6322,6485,6611,6778,6936,7005,7065,7151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,213", "endColumns": "76,80,77", "endOffsets": "127,208,286"}, "to": {"startLines": "52,74,78", "startColumns": "4,4,4", "startOffsets": "4806,7464,7778", "endColumns": "76,80,77", "endOffsets": "4878,7540,7851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,13869", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,13946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1211,1277,1376,1453,1516,1634,1695,1760,1817,1887,1948,2002,2118,2175,2237,2291,2365,2493,2581,2668,2771,2863,2949,3086,3170,3255,3389,3480,3556,3610,3661,3727,3799,3877,3948,4030,4110,4186,4263,4340,4447,4536,4609,4699,4794,4868,4949,5042,5097,5178,5244,5330,5415,5477,5541,5604,5676,5774,5873,5968,6060,6118,6173,6253,6347,6423", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1206,1272,1371,1448,1511,1629,1690,1755,1812,1882,1943,1997,2113,2170,2232,2286,2360,2488,2576,2663,2766,2858,2944,3081,3165,3250,3384,3475,3551,3605,3656,3722,3794,3872,3943,4025,4105,4181,4258,4335,4442,4531,4604,4694,4789,4863,4944,5037,5092,5173,5239,5325,5410,5472,5536,5599,5671,5769,5868,5963,6055,6113,6168,6248,6342,6418,6497"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,76,77,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,150,154,155,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3226,3304,3388,3486,4304,4401,4538,7637,7712,7856,8338,8587,8650,8768,8829,8894,8951,9021,9082,9136,9252,9309,9371,9425,9499,9627,9715,9802,9905,9997,10083,10220,10304,10389,10523,10614,10690,10744,10795,10861,10933,11011,11082,11164,11244,11320,11397,11474,11581,11670,11743,11833,11928,12002,12083,12176,12231,12312,12378,12464,12549,12611,12675,12738,12810,12908,13007,13102,13194,13252,13618,13951,14045,14193", "endLines": "7,35,36,37,38,39,47,48,49,76,77,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,150,154,155,157", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "426,3221,3299,3383,3481,3572,4396,4533,4625,7707,7773,7950,8410,8645,8763,8824,8889,8946,9016,9077,9131,9247,9304,9366,9420,9494,9622,9710,9797,9900,9992,10078,10215,10299,10384,10518,10609,10685,10739,10790,10856,10928,11006,11077,11159,11239,11315,11392,11469,11576,11665,11738,11828,11923,11997,12078,12171,12226,12307,12373,12459,12544,12606,12670,12733,12805,12903,13002,13097,13189,13247,13302,13693,14040,14116,14267"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "83,485", "startColumns": "4,4", "startOffsets": "8277,47025", "endColumns": "60,78", "endOffsets": "8333,47099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "40,41,42,43,44,45,46,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3577,3675,3777,3878,3979,4084,4187,14272", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "3670,3772,3873,3974,4079,4182,4299,14368"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "315", "startColumns": "4", "startOffsets": "28931", "endColumns": "91", "endOffsets": "29018"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1383,1453", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1378,1448,1571"}, "to": {"startLines": "50,51,72,73,75,85,86,146,147,148,149,151,152,156,480,481,482", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4630,4723,7264,7362,7545,8415,8497,13307,13395,13477,13548,13698,13782,14121,46569,46653,46723", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "4718,4801,7357,7459,7632,8492,8582,13390,13472,13543,13613,13777,13864,14188,46648,46718,46841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "483,484", "startColumns": "4,4", "startOffsets": "46846,46934", "endColumns": "87,90", "endOffsets": "46929,47020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,222,299,408,514,597,680,822,931,1035,1110,1253,1310,1429,1502,1680,1893,2043,2134,2232,2327,2423,2626,2739,2836,3086,3184,3316,3557,3796,3901,4013,4216,4308,4370,4438,4529,4622,4725,4803,4902,4997,5101,5316,5390,5459,5534,5648,5732,5817,5888,5972,6046,6123,6271,6363,6471,6569,6643,6724,6793,6879,6942,7046,7181,7292,7426,7490,7583,7681,7819,7890,7982,8093,8152,8215,8317,8425,8611,8907,9218,9323,9396,9493,9566,9696,9814,9910,10004,10076,10162,10241,10331,10462,10597,10662,10738,10796,10982,11049,11112,11190,11262,11340,11423,11540,11664,11773,11862,11951,12039,12118", "endColumns": "76,89,76,108,105,82,82,141,108,103,74,142,56,118,72,177,212,149,90,97,94,95,202,112,96,249,97,131,240,238,104,111,202,91,61,67,90,92,102,77,98,94,103,214,73,68,74,113,83,84,70,83,73,76,147,91,107,97,73,80,68,85,62,103,134,110,133,63,92,97,137,70,91,110,58,62,101,107,185,295,310,104,72,96,72,129,117,95,93,71,85,78,89,130,134,64,75,57,185,66,62,77,71,77,82,116,123,108,88,88,87,78,156", "endOffsets": "127,217,294,403,509,592,675,817,926,1030,1105,1248,1305,1424,1497,1675,1888,2038,2129,2227,2322,2418,2621,2734,2831,3081,3179,3311,3552,3791,3896,4008,4211,4303,4365,4433,4524,4617,4720,4798,4897,4992,5096,5311,5385,5454,5529,5643,5727,5812,5883,5967,6041,6118,6266,6358,6466,6564,6638,6719,6788,6874,6937,7041,7176,7287,7421,7485,7578,7676,7814,7885,7977,8088,8147,8210,8312,8420,8606,8902,9213,9318,9391,9488,9561,9691,9809,9905,9999,10071,10157,10236,10326,10457,10592,10657,10733,10791,10977,11044,11107,11185,11257,11335,11418,11535,11659,11768,11857,11946,12034,12113,12270"}, "to": {"startLines": "173,174,175,245,257,258,259,276,289,291,292,323,341,343,346,350,351,352,355,357,359,360,361,362,363,364,365,366,367,368,369,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,418,422,432,433,434,435,436,437,438,439,440,449,450,451,452,453,454,455,456,457,458,459,460,461,462,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15450,15527,15617,21445,23351,23457,23540,25549,26813,26971,27075,29684,31551,31690,31934,32248,32426,32639,32943,33141,33338,33433,33529,33732,33845,33942,34192,34290,34422,34663,34902,35108,35220,35423,35515,35577,35645,35736,35829,35932,36010,36109,36204,36308,36523,36597,36666,36741,36855,36939,37024,37095,37179,37253,37330,37954,38046,38154,38252,38326,38407,38476,38562,38625,38729,38864,38975,39109,39173,39266,39364,39502,39760,40095,41762,41821,41884,41986,42094,42280,42576,42887,42992,43616,43713,43786,43916,44034,44130,44224,44296,44382,44461,44551,44682,44817,44882,45032,45090,45276,45343,45406,45484,45556,45634,45717,45834,45958,46067,46156,46245,46333,46412", "endColumns": "76,89,76,108,105,82,82,141,108,103,74,142,56,118,72,177,212,149,90,97,94,95,202,112,96,249,97,131,240,238,104,111,202,91,61,67,90,92,102,77,98,94,103,214,73,68,74,113,83,84,70,83,73,76,147,91,107,97,73,80,68,85,62,103,134,110,133,63,92,97,137,70,91,110,58,62,101,107,185,295,310,104,72,96,72,129,117,95,93,71,85,78,89,130,134,64,75,57,185,66,62,77,71,77,82,116,123,108,88,88,87,78,156", "endOffsets": "15522,15612,15689,21549,23452,23535,23618,25686,26917,27070,27145,29822,31603,31804,32002,32421,32634,32784,33029,33234,33428,33524,33727,33840,33937,34187,34285,34417,34658,34897,35002,35215,35418,35510,35572,35640,35731,35824,35927,36005,36104,36199,36303,36518,36592,36661,36736,36850,36934,37019,37090,37174,37248,37325,37473,38041,38149,38247,38321,38402,38471,38557,38620,38724,38859,38970,39104,39168,39261,39359,39497,39568,39847,40201,41816,41879,41981,42089,42275,42571,42882,42987,43060,43708,43781,43911,44029,44125,44219,44291,44377,44456,44546,44677,44812,44877,44953,45085,45271,45338,45401,45479,45551,45629,45712,45829,45953,46062,46151,46240,46328,46407,46564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5882", "endColumns": "156", "endOffsets": "6034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,272,354,450,515,586,641,712,801,875,980,1085,1173,1255,1340,1430,1532,1634,1708,1807,1900,1990,2105,2190,2289,2370,2476,2550,2643,2748,2842,2936,3037,3103,3174,3290,3399,3470,3534,4277,4954,5028,5134,5241,5296,5385,5471,5540,5627,5729,5785,5875,5924,6001,6108,6173,6244,6311,6377,6426,6506,6598,6673,6720,6774,6848,6906,6985,7164,7319,7452,7519,7613,7692,7789,7872,7953,8029,8118,8200,8308,8395,8489,8545,8682,8732,8787,8876,8943,9012,9082,9151,9239,9310,9361", "endColumns": "68,77,69,81,95,64,70,54,70,88,73,104,104,87,81,84,89,101,101,73,98,92,89,114,84,98,80,105,73,92,104,93,93,100,65,70,115,108,70,63,742,676,73,105,106,54,88,85,68,86,101,55,89,48,76,106,64,70,66,65,48,79,91,74,46,53,73,57,78,178,154,132,66,93,78,96,82,80,75,88,81,107,86,93,55,136,49,54,88,66,68,69,68,87,70,50,73", "endOffsets": "119,197,267,349,445,510,581,636,707,796,870,975,1080,1168,1250,1335,1425,1527,1629,1703,1802,1895,1985,2100,2185,2284,2365,2471,2545,2638,2743,2837,2931,3032,3098,3169,3285,3394,3465,3529,4272,4949,5023,5129,5236,5291,5380,5466,5535,5622,5724,5780,5870,5919,5996,6103,6168,6239,6306,6372,6421,6501,6593,6668,6715,6769,6843,6901,6980,7159,7314,7447,7514,7608,7687,7784,7867,7948,8024,8113,8195,8303,8390,8484,8540,8677,8727,8782,8871,8938,9007,9077,9146,9234,9305,9356,9430"}, "to": {"startLines": "166,167,168,169,170,171,172,176,177,178,179,182,184,185,187,192,196,211,214,215,216,218,219,220,222,226,227,228,229,230,231,232,233,234,235,237,240,241,247,248,249,260,261,262,263,264,265,266,267,268,269,270,271,278,279,280,283,284,285,286,290,295,296,297,298,299,304,305,306,307,308,309,313,314,324,325,327,328,332,333,335,340,347,348,419,420,421,423,428,441,442,443,444,445,446,447,463", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14919,14988,15066,15136,15218,15314,15379,15694,15749,15820,15909,16126,16311,16416,16571,16927,17226,18290,18557,18659,18733,18900,18993,19083,19258,19568,19667,19748,19854,19928,20021,20126,20220,20314,20415,20557,20814,20930,21766,21837,21901,23623,24300,24374,24480,24587,24642,24731,24817,24886,24973,25075,25131,25755,25804,25881,26156,26221,26292,26359,26922,27291,27371,27463,27538,27585,27893,27967,28025,28104,28283,28438,28770,28837,29827,29906,30085,30168,30532,30608,30782,31443,32007,32094,39852,39908,40045,40206,40798,43065,43132,43201,43271,43340,43428,43499,44958", "endColumns": "68,77,69,81,95,64,70,54,70,88,73,104,104,87,81,84,89,101,101,73,98,92,89,114,84,98,80,105,73,92,104,93,93,100,65,70,115,108,70,63,742,676,73,105,106,54,88,85,68,86,101,55,89,48,76,106,64,70,66,65,48,79,91,74,46,53,73,57,78,178,154,132,66,93,78,96,82,80,75,88,81,107,86,93,55,136,49,54,88,66,68,69,68,87,70,50,73", "endOffsets": "14983,15061,15131,15213,15309,15374,15445,15744,15815,15904,15978,16226,16411,16499,16648,17007,17311,18387,18654,18728,18827,18988,19078,19193,19338,19662,19743,19849,19923,20016,20121,20215,20309,20410,20476,20623,20925,21034,21832,21896,22639,24295,24369,24475,24582,24637,24726,24812,24881,24968,25070,25126,25216,25799,25876,25983,26216,26287,26354,26420,26966,27366,27458,27533,27580,27634,27962,28020,28099,28278,28433,28566,28832,28926,29901,29998,30163,30244,30603,30692,30859,31546,32089,32183,39903,40040,40090,40256,40882,43127,43196,43266,43335,43423,43494,43545,45027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,141,244,311,378,442,529", "endColumns": "85,102,66,66,63,86,71", "endOffsets": "136,239,306,373,437,524,596"}, "to": {"startLines": "159,160,161,162,163,164,165", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14373,14459,14562,14629,14696,14760,14847", "endColumns": "85,102,66,66,63,86,71", "endOffsets": "14454,14557,14624,14691,14755,14842,14914"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,195,259,344,409,481,539,616,693,761,821", "endColumns": "79,59,63,84,64,71,57,76,76,67,59,73", "endOffsets": "130,190,254,339,404,476,534,611,688,756,816,890"}, "to": {"startLines": "181,191,193,194,195,199,207,210,213,217,221,225", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16046,16867,17012,17076,17161,17452,18018,18213,18480,18832,19198,19494", "endColumns": "79,59,63,84,64,71,57,76,76,67,59,73", "endOffsets": "16121,16922,17071,17156,17221,17519,18071,18285,18552,18895,19253,19563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,359,461,673,719,786,885,960,1228,1288,1380,1459,1513,1577,1653,1745,2082,2155,2223,2276,2329,2408,2523,2634,2701,2780,2862,2946,3031,3150,3444,3533,3610,3692,3752,3817,3877,3949,4031,4138,4237,4338,4467,4565,4639,4718,4814,5001,5212,5343,5475,5538,6245,6349,6413", "endColumns": "128,174,101,211,45,66,98,74,267,59,91,78,53,63,75,91,336,72,67,52,52,78,114,110,66,78,81,83,84,118,293,88,76,81,59,64,59,71,81,106,98,100,128,97,73,78,95,186,210,130,131,62,706,103,63,65", "endOffsets": "179,354,456,668,714,781,880,955,1223,1283,1375,1454,1508,1572,1648,1740,2077,2150,2218,2271,2324,2403,2518,2629,2696,2775,2857,2941,3026,3145,3439,3528,3605,3687,3747,3812,3872,3944,4026,4133,4232,4333,4462,4560,4634,4713,4809,4996,5207,5338,5470,5533,6240,6344,6408,6474"}, "to": {"startLines": "242,243,244,246,250,251,252,253,254,255,256,272,275,277,281,282,287,293,294,302,312,316,317,318,319,320,326,329,334,336,337,338,339,342,344,345,349,353,354,356,358,370,395,396,397,398,399,417,424,425,426,427,429,430,431,448", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21039,21168,21343,21554,22644,22690,22757,22856,22931,23199,23259,25221,25495,25691,25988,26064,26425,27150,27223,27776,28717,29023,29102,29217,29328,29395,30003,30249,30697,30864,30983,31277,31366,31608,31809,31869,32188,32789,32861,33034,33239,35007,37478,37607,37705,37779,37858,39573,40261,40472,40603,40735,40887,41594,41698,43550", "endColumns": "128,174,101,211,45,66,98,74,267,59,91,78,53,63,75,91,336,72,67,52,52,78,114,110,66,78,81,83,84,118,293,88,76,81,59,64,59,71,81,106,98,100,128,97,73,78,95,186,210,130,131,62,706,103,63,65", "endOffsets": "21163,21338,21440,21761,22685,22752,22851,22926,23194,23254,23346,25295,25544,25750,26059,26151,26757,27218,27286,27824,28765,29097,29212,29323,29390,29469,30080,30328,30777,30978,31272,31361,31438,31685,31864,31929,32243,32856,32938,33136,33333,35103,37602,37700,37774,37853,37949,39755,40467,40598,40730,40793,41589,41693,41757,43611"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "71,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "7156,7955,8060,8172", "endColumns": "107,104,111,104", "endOffsets": "7259,8055,8167,8272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,198,265,349,418,479,551,615,680,766,836,908,971,1045,1109,1182,1246,1334,1399,1485,1561,1651,1747,1861,1942,1993,2044,2130,2194,2266,2340,2456,2550,2651", "endColumns": "62,79,66,83,68,60,71,63,64,85,69,71,62,73,63,72,63,87,64,85,75,89,95,113,80,50,50,85,63,71,73,115,93,100,97", "endOffsets": "113,193,260,344,413,474,546,610,675,761,831,903,966,1040,1104,1177,1241,1329,1394,1480,1556,1646,1742,1856,1937,1988,2039,2125,2189,2261,2335,2451,2545,2646,2744"}, "to": {"startLines": "180,183,186,188,189,190,197,198,200,201,202,203,204,205,206,208,209,212,223,224,236,238,239,273,274,288,300,301,303,310,311,321,322,330,331", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15983,16231,16504,16653,16737,16806,17316,17388,17524,17589,17675,17745,17817,17880,17954,18076,18149,18392,19343,19408,20481,20628,20718,25300,25414,26762,27639,27690,27829,28571,28643,29474,29590,30333,30434", "endColumns": "62,79,66,83,68,60,71,63,64,85,69,71,62,73,63,72,63,87,64,85,75,89,95,113,80,50,50,85,63,71,73,115,93,100,97", "endOffsets": "16041,16306,16566,16732,16801,16862,17383,17447,17584,17670,17740,17812,17875,17949,18013,18144,18208,18475,19403,19489,20552,20713,20809,25409,25490,26808,27685,27771,27888,28638,28712,29585,29679,30429,30527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4883,4990,5156,5282,5392,5534,5663,5778,6039,6220,6327,6490,6616,6783,6941,7010,7070", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "4985,5151,5277,5387,5529,5658,5773,5877,6215,6322,6485,6611,6778,6936,7005,7065,7151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,213", "endColumns": "76,80,77", "endOffsets": "127,208,286"}, "to": {"startLines": "52,74,78", "startColumns": "4,4,4", "startOffsets": "4806,7464,7778", "endColumns": "76,80,77", "endOffsets": "4878,7540,7851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,13869", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,13946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1211,1277,1376,1453,1516,1634,1695,1760,1817,1887,1948,2002,2118,2175,2237,2291,2365,2493,2581,2668,2771,2863,2949,3086,3170,3255,3389,3480,3556,3610,3661,3727,3799,3877,3948,4030,4110,4186,4263,4340,4447,4536,4609,4699,4794,4868,4949,5042,5097,5178,5244,5330,5415,5477,5541,5604,5676,5774,5873,5968,6060,6118,6173,6253,6347,6423", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1206,1272,1371,1448,1511,1629,1690,1755,1812,1882,1943,1997,2113,2170,2232,2286,2360,2488,2576,2663,2766,2858,2944,3081,3165,3250,3384,3475,3551,3605,3656,3722,3794,3872,3943,4025,4105,4181,4258,4335,4442,4531,4604,4694,4789,4863,4944,5037,5092,5173,5239,5325,5410,5472,5536,5599,5671,5769,5868,5963,6055,6113,6168,6248,6342,6418,6497"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,76,77,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,150,154,155,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3226,3304,3388,3486,4304,4401,4538,7637,7712,7856,8338,8587,8650,8768,8829,8894,8951,9021,9082,9136,9252,9309,9371,9425,9499,9627,9715,9802,9905,9997,10083,10220,10304,10389,10523,10614,10690,10744,10795,10861,10933,11011,11082,11164,11244,11320,11397,11474,11581,11670,11743,11833,11928,12002,12083,12176,12231,12312,12378,12464,12549,12611,12675,12738,12810,12908,13007,13102,13194,13252,13618,13951,14045,14193", "endLines": "7,35,36,37,38,39,47,48,49,76,77,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,150,154,155,157", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "426,3221,3299,3383,3481,3572,4396,4533,4625,7707,7773,7950,8410,8645,8763,8824,8889,8946,9016,9077,9131,9247,9304,9366,9420,9494,9622,9710,9797,9900,9992,10078,10215,10299,10384,10518,10609,10685,10739,10790,10856,10928,11006,11077,11159,11239,11315,11392,11469,11576,11665,11738,11828,11923,11997,12078,12171,12226,12307,12373,12459,12544,12606,12670,12733,12805,12903,13002,13097,13189,13247,13302,13693,14040,14116,14267"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "83,485", "startColumns": "4,4", "startOffsets": "8277,47025", "endColumns": "60,78", "endOffsets": "8333,47099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "40,41,42,43,44,45,46,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3577,3675,3777,3878,3979,4084,4187,14272", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "3670,3772,3873,3974,4079,4182,4299,14368"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "315", "startColumns": "4", "startOffsets": "28931", "endColumns": "91", "endOffsets": "29018"}}]}]}
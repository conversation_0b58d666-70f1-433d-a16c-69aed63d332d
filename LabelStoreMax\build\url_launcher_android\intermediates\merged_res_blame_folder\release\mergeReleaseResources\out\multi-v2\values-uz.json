{"logs": [{"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-release-26:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,173,282,392", "endColumns": "117,108,109,105", "endOffsets": "168,277,387,493"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "791,909,1018,1128", "endColumns": "117,108,109,105", "endOffsets": "904,1013,1123,1229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,1234", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,1330"}}]}, {"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-mergeReleaseResources-24:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,173,282,392", "endColumns": "117,108,109,105", "endOffsets": "168,277,387,493"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "791,909,1018,1128", "endColumns": "117,108,109,105", "endOffsets": "904,1013,1123,1229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,1234", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,1330"}}]}]}
package com.velvete.ly

import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugins.GeneratedPluginRegistrant

class MainActivity : FlutterFragmentActivity() {
    // You might have existing method overrides here, e.g., configureFlutterEngine
    // If there were any methods in your previous MainActivity, attempt to re-add them here.
    // For now, this basic structure is sufficient to resolve ClassNotFoundException.

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        GeneratedPluginRegistrant.registerWith(flutterEngine)
    }
}

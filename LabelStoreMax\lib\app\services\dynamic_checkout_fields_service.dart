//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import '/app/services/woocommerce_service.dart';

/// Service to dynamically fetch and manage checkout field configurations from WooCommerce
class DynamicCheckoutFieldsService {
  static final DynamicCheckoutFieldsService _instance = DynamicCheckoutFieldsService._internal();
  factory DynamicCheckoutFieldsService() => _instance;
  DynamicCheckoutFieldsService._internal();

  final WooCommerceService _wooService = WooCommerceService();
  CheckoutFieldsConfig? _cachedConfig;

  /// Fetch checkout fields configuration from WooCommerce API
  Future<CheckoutFieldsConfig> getCheckoutFieldsConfig({bool forceRefresh = false}) async {
    print('=== Dynamic Checkout Fields Config Fetch ===');
    
    // Return cached data if available and not forcing refresh
    if (_cachedConfig != null && !forceRefresh) {
      print('✅ Returning cached checkout fields config');
      return _cachedConfig!;
    }

    try {
      // Fetch checkout settings
      final checkoutResponse = await _wooService.wooCommerce.dio.get('/settings/checkout');
      
      // Fetch countries data
      final countriesResponse = await _wooService.wooCommerce.dio.get('/data/countries');
      
      // Fetch general settings
      final generalResponse = await _wooService.wooCommerce.dio.get('/settings/general');
      
      print('✅ Checkout Fields API Success');
      print('Checkout settings: ${checkoutResponse.data.length} items');

      // Handle countries data safely - it might be a List or Map
      int countriesCount = 0;
      if (countriesResponse.data is Map) {
        countriesCount = (countriesResponse.data as Map).keys.length;
      } else if (countriesResponse.data is List) {
        countriesCount = (countriesResponse.data as List).length;
      }
      print('Countries data: $countriesCount countries');
      print('General settings: ${generalResponse.data.length} items');
      
      // Create configuration object
      CheckoutFieldsConfig config = CheckoutFieldsConfig.fromWooCommerceData(
        checkoutSettings: checkoutResponse.data,
        countriesData: countriesResponse.data,
        generalSettings: generalResponse.data,
      );
      
      // Cache the results
      _cachedConfig = config;
      
      print('✅ Dynamic checkout fields config loaded');
      return config;
      
    } catch (e) {
      print('❌ Failed to fetch checkout fields config: $e');
      
      // Return default configuration on error
      return CheckoutFieldsConfig.defaultConfig();
    }
  }

  /// Clear cached checkout fields configuration
  void clearCache() {
    _cachedConfig = null;
    print('🗑️ Checkout fields config cache cleared');
  }

  /// Get countries list for dropdown
  /// Restricts to Libya only based on WooCommerce backend configuration
  Future<Map<String, String>> getCountries() async {
    // Check if WooCommerce is configured for Libya only
    Map<String, String> allowedCountries = await _getWooCommerceAllowedCountries();

    if (allowedCountries.isNotEmpty) {
      print('✅ Using WooCommerce allowed countries: ${allowedCountries.keys.join(', ')}');
      return allowedCountries;
    }

    // Fallback to Libya only if no specific configuration found
    print('⚠️ No specific country restrictions found, defaulting to Libya only');
    return {'LY': 'Libya'};
  }

  /// Get allowed countries from WooCommerce general settings
  Future<Map<String, String>> _getWooCommerceAllowedCountries() async {
    try {
      // Fetch general settings to check selling locations
      final generalResponse = await _wooService.wooCommerce.dio.get('/settings/general');

      Map<String, String> allowedCountries = {};

      // Look for selling location settings
      for (var setting in generalResponse.data) {
        if (setting is Map) {
          String? id = setting['id']?.toString();

          // Check for selling location settings
          if (id == 'woocommerce_allowed_countries' ||
              id == 'woocommerce_specific_allowed_countries' ||
              id == 'woocommerce_ship_to_countries' ||
              id == 'woocommerce_specific_ship_to_countries') {

            var value = setting['value'];
            print('🌍 Found country setting $id: $value');

            // If it's set to specific countries, use those
            if (value is List && value.isNotEmpty) {
              for (String countryCode in value) {
                // Map country codes to names (simplified for Libya)
                if (countryCode == 'LY') {
                  allowedCountries['LY'] = 'Libya';
                }
              }
            } else if (value == 'specific' || value == 'LY') {
              // Libya specific
              allowedCountries['LY'] = 'Libya';
            }
          }
        }
      }

      // If no specific restrictions found but we know it's Libya-only, enforce it
      if (allowedCountries.isEmpty) {
        print('🇱🇾 No specific country restrictions found, enforcing Libya-only policy');
        allowedCountries['LY'] = 'Libya';
      }

      return allowedCountries;

    } catch (e) {
      print('❌ Error fetching WooCommerce country settings: $e');
      // Default to Libya only on error
      return {'LY': 'Libya'};
    }
  }

  /// Get states for a specific country
  Future<Map<String, String>> getStatesForCountry(String countryCode) async {
    final config = await getCheckoutFieldsConfig();
    return config.getStatesForCountry(countryCode);
  }

  /// Check if a field is required
  Future<bool> isFieldRequired(String fieldKey) async {
    final config = await getCheckoutFieldsConfig();
    return config.isFieldRequired(fieldKey);
  }
}

/// Checkout Fields Configuration model
class CheckoutFieldsConfig {
  final Map<String, String> countries;
  final Map<String, Map<String, String>> states;
  final Map<String, dynamic> checkoutSettings;
  final Map<String, dynamic> generalSettings;
  final List<CheckoutField> billingFields;
  final List<CheckoutField> shippingFields;

  CheckoutFieldsConfig({
    required this.countries,
    required this.states,
    required this.checkoutSettings,
    required this.generalSettings,
    required this.billingFields,
    required this.shippingFields,
  });

  factory CheckoutFieldsConfig.fromWooCommerceData({
    required List<dynamic> checkoutSettings,
    required dynamic countriesData,
    required List<dynamic> generalSettings,
  }) {
    // Extract countries - handle both Map and List formats
    Map<String, String> countries = {};
    Map<String, Map<String, String>> states = {};

    if (countriesData is Map<String, dynamic>) {
      // Handle Map format
      countriesData.forEach((key, value) {
        if (value is Map && value['name'] != null) {
          countries[key] = value['name'];
        }
      });

      // Extract states
      countriesData.forEach((countryCode, countryData) {
        if (countryData is Map && countryData['states'] != null) {
          Map<String, String> countryStates = {};
          (countryData['states'] as Map).forEach((stateCode, stateName) {
            countryStates[stateCode] = stateName.toString();
          });
          states[countryCode] = countryStates;
        }
      });
    } else if (countriesData is List) {
      // Handle List format - extract country codes and names
      for (var item in countriesData) {
        if (item is Map) {
          String? code = item['code']?.toString();
          String? name = item['name']?.toString();
          if (code != null && name != null) {
            countries[code] = name;
          }
        }
      }
    } else {
      // Fallback to Libya only if data format is unexpected
      print('⚠️ Unexpected countries data format, using Libya fallback');
      countries = {'LY': 'Libya'};
    }

    // Convert settings to maps
    Map<String, dynamic> checkoutSettingsMap = {};
    for (var setting in checkoutSettings) {
      if (setting is Map && setting['id'] != null) {
        checkoutSettingsMap[setting['id']] = setting;
      }
    }

    Map<String, dynamic> generalSettingsMap = {};
    for (var setting in generalSettings) {
      if (setting is Map && setting['id'] != null) {
        generalSettingsMap[setting['id']] = setting;
      }
    }

    // Create standard billing fields
    List<CheckoutField> billingFields = [
      CheckoutField(
        key: 'billing_first_name',
        label: 'First Name',
        type: 'text',
        required: true,
        placeholder: 'Enter your first name',
      ),
      CheckoutField(
        key: 'billing_last_name',
        label: 'Last Name',
        type: 'text',
        required: true,
        placeholder: 'Enter your last name',
      ),
      CheckoutField(
        key: 'billing_email',
        label: 'Email Address',
        type: 'email',
        required: true,
        placeholder: 'Enter your email address',
      ),
      CheckoutField(
        key: 'billing_phone',
        label: 'Phone Number',
        type: 'tel',
        required: true,
        placeholder: 'Enter your phone number',
      ),
      CheckoutField(
        key: 'billing_address_1',
        label: 'Address Line 1',
        type: 'text',
        required: true,
        placeholder: 'Street address',
      ),
      CheckoutField(
        key: 'billing_address_2',
        label: 'Address Line 2',
        type: 'text',
        required: false,
        placeholder: 'Apartment, suite, etc. (optional)',
      ),
      CheckoutField(
        key: 'billing_city',
        label: 'City',
        type: 'text',
        required: true,
        placeholder: 'Enter your city',
      ),
      CheckoutField(
        key: 'billing_country',
        label: 'Country',
        type: 'select',
        required: true,
        placeholder: 'Select your country',
      ),
      CheckoutField(
        key: 'billing_state',
        label: 'State / Province',
        type: 'select',
        required: false,
        placeholder: 'Select your state',
      ),
      CheckoutField(
        key: 'billing_postcode',
        label: 'Postal Code',
        type: 'text',
        required: false,
        placeholder: 'Enter postal code',
      ),
    ];

    // Create standard shipping fields (similar to billing)
    List<CheckoutField> shippingFields = billingFields.map((field) {
      return CheckoutField(
        key: field.key.replaceFirst('billing_', 'shipping_'),
        label: field.label,
        type: field.type,
        required: field.required,
        placeholder: field.placeholder,
      );
    }).toList();

    return CheckoutFieldsConfig(
      countries: countries,
      states: states,
      checkoutSettings: checkoutSettingsMap,
      generalSettings: generalSettingsMap,
      billingFields: billingFields,
      shippingFields: shippingFields,
    );
  }

  factory CheckoutFieldsConfig.defaultConfig() {
    return CheckoutFieldsConfig(
      countries: {'LY': 'Libya'}, // Libya only as per WooCommerce backend configuration
      states: {},
      checkoutSettings: {},
      generalSettings: {},
      billingFields: [],
      shippingFields: [],
    );
  }

  /// Get states for a specific country
  Map<String, String> getStatesForCountry(String countryCode) {
    return states[countryCode] ?? {};
  }

  /// Check if a field is required
  bool isFieldRequired(String fieldKey) {
    // Check if field is in the required fields list
    final field = [...billingFields, ...shippingFields]
        .where((f) => f.key == fieldKey)
        .firstOrNull;
    return field?.required ?? false;
  }

  /// Get currency symbol
  String get currencySymbol {
    final currencyCode = generalSettings['woocommerce_currency']?['value'] ?? 'LYD';
    switch (currencyCode) {
      case 'LYD':
        return 'د.ل';
      case 'USD':
        return '\$';
      case 'EUR':
        return '€';
      default:
        return currencyCode;
    }
  }
}

/// Individual checkout field model
class CheckoutField {
  final String key;
  final String label;
  final String type;
  final bool required;
  final String placeholder;
  final List<String>? options;

  CheckoutField({
    required this.key,
    required this.label,
    required this.type,
    required this.required,
    required this.placeholder,
    this.options,
  });
}

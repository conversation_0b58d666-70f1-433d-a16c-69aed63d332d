<dependencies>
  <compile
      roots="__local_aars__:C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app\intermediates\flutter\release\libs.jar:unspecified@jar,:@@:app_badge_plus::release,:@@:flutter_timezone::release,:@@:google_sign_in_android::release,:@@:shared_preferences_android::release,:@@:stripe_android::release,:@@:firebase_messaging::release,:@@:firebase_core::release,:@@:flutter_facebook_auth::release,:@@:flutter_inappwebview_android::release,:@@:flutter_local_notifications::release,:@@:flutter_plugin_android_lifecycle::release,:@@:flutter_secure_storage::release,:@@:fluttertoast::release,:@@:geocoding_android::release,:@@:geolocator_android::release,:@@:google_maps_flutter_android::release,:@@:image_picker_android::release,:@@:package_info_plus::release,:@@:path_provider_android::release,:@@:razorpay_flutter::release,:@@:sign_in_with_apple::release,:@@:sqflite_android::release,:@@:url_launcher_android::release,io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,com.razorpay:checkout:1.6.41@aar,com.razorpay:standard-core:1.6.52@aar,com.google.android.gms:play-services-auth:21.3.0@aar,com.google.android.gms:play-services-wallet:19.4.0@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.android.gms:play-services-auth-base:18.0.10@aar,com.google.android.gms:play-services-fido:21.0.0@aar,com.google.android.gms:play-services-identity:18.1.0@aar,com.google.android.gms:play-services-maps:18.2.0@aar,com.google.android.gms:play-services-base:18.5.0@aar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment:1.8.6@aar,androidx.fragment:fragment:1.8.6@aar,androidx.activity:activity:1.9.3@aar,androidx.loader:loader:1.1.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar,androidx.core:core-ktx:1.16.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.core:core:1.16.0@aar,androidx.core:core:1.16.0@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar,androidx.lifecycle:lifecycle-common-java8:2.8.7@jar,androidx.lifecycle:lifecycle-process:2.8.7@aar,androidx.window:window:1.2.0@aar,androidx.window:window-java:1.2.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.0@jar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.collection:collection-jvm:1.4.2@jar,androidx.annotation:annotation-jvm:1.9.1@jar,androidx.core:core-viewtree:1.0.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.10@jar,io.flutter:armeabi_v7a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,io.flutter:arm64_v8a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,io.flutter:x86_64_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,org.jetbrains:annotations:23.0.0@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,org.jspecify:jspecify:1.0.0@jar,com.getkeepsafe.relinker:relinker:1.4.5@aar">
    <dependency
        name="__local_aars__:C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app\intermediates\flutter\release\libs.jar:unspecified@jar"
        simpleName="__local_aars__:C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app\intermediates\flutter\release\libs.jar"/>
    <dependency
        name=":@@:app_badge_plus::release"
        simpleName="artifacts::app_badge_plus"/>
    <dependency
        name=":@@:flutter_timezone::release"
        simpleName="artifacts::flutter_timezone"/>
    <dependency
        name=":@@:google_sign_in_android::release"
        simpleName="artifacts::google_sign_in_android"/>
    <dependency
        name=":@@:shared_preferences_android::release"
        simpleName="artifacts::shared_preferences_android"/>
    <dependency
        name=":@@:stripe_android::release"
        simpleName="artifacts::stripe_android"/>
    <dependency
        name=":@@:firebase_messaging::release"
        simpleName="artifacts::firebase_messaging"/>
    <dependency
        name=":@@:firebase_core::release"
        simpleName="artifacts::firebase_core"/>
    <dependency
        name=":@@:flutter_facebook_auth::release"
        simpleName="artifacts::flutter_facebook_auth"/>
    <dependency
        name=":@@:flutter_inappwebview_android::release"
        simpleName="artifacts::flutter_inappwebview_android"/>
    <dependency
        name=":@@:flutter_local_notifications::release"
        simpleName="artifacts::flutter_local_notifications"/>
    <dependency
        name=":@@:flutter_plugin_android_lifecycle::release"
        simpleName="artifacts::flutter_plugin_android_lifecycle"/>
    <dependency
        name=":@@:flutter_secure_storage::release"
        simpleName="artifacts::flutter_secure_storage"/>
    <dependency
        name=":@@:fluttertoast::release"
        simpleName="artifacts::fluttertoast"/>
    <dependency
        name=":@@:geocoding_android::release"
        simpleName="artifacts::geocoding_android"/>
    <dependency
        name=":@@:geolocator_android::release"
        simpleName="artifacts::geolocator_android"/>
    <dependency
        name=":@@:google_maps_flutter_android::release"
        simpleName="artifacts::google_maps_flutter_android"/>
    <dependency
        name=":@@:image_picker_android::release"
        simpleName="artifacts::image_picker_android"/>
    <dependency
        name=":@@:package_info_plus::release"
        simpleName="artifacts::package_info_plus"/>
    <dependency
        name=":@@:path_provider_android::release"
        simpleName="artifacts::path_provider_android"/>
    <dependency
        name=":@@:razorpay_flutter::release"
        simpleName="artifacts::razorpay_flutter"/>
    <dependency
        name=":@@:sign_in_with_apple::release"
        simpleName="artifacts::sign_in_with_apple"/>
    <dependency
        name=":@@:sqflite_android::release"
        simpleName="artifacts::sqflite_android"/>
    <dependency
        name=":@@:url_launcher_android::release"
        simpleName="artifacts::url_launcher_android"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="com.razorpay:checkout:1.6.41@aar"
        simpleName="com.razorpay:checkout"/>
    <dependency
        name="com.razorpay:standard-core:1.6.52@aar"
        simpleName="com.razorpay:standard-core"/>
    <dependency
        name="com.google.android.gms:play-services-auth:21.3.0@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="com.google.android.gms:play-services-wallet:19.4.0@aar"
        simpleName="com.google.android.gms:play-services-wallet"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.android.gms:play-services-fido:21.0.0@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="com.google.android.gms:play-services-identity:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-identity"/>
    <dependency
        name="com.google.android.gms:play-services-maps:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-maps"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.8.6@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.9.3@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.2@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="io.flutter:armeabi_v7a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:armeabi_v7a_release"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="io.flutter:x86_64_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:x86_64_release"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
  </compile>
  <package
      roots="__local_aars__:C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app\intermediates\flutter\release\libs.jar:unspecified@jar,:@@:app_badge_plus::release,:@@:flutter_timezone::release,:@@:google_sign_in_android::release,:@@:package_info_plus::release,:@@:shared_preferences_android::release,:@@:stripe_android::release,:@@:flutter_facebook_auth::release,com.facebook.android:facebook-login:18.0.3@aar,:@@:flutter_local_notifications::release,:@@:geolocator_android::release,:@@:image_picker_android::release,:@@:url_launcher_android::release,:@@:firebase_messaging::release,:@@:firebase_core::release,:@@:flutter_inappwebview_android::release,:@@:google_maps_flutter_android::release,:@@:flutter_plugin_android_lifecycle::release,:@@:flutter_secure_storage::release,:@@:razorpay_flutter::release,:@@:fluttertoast::release,:@@:geocoding_android::release,:@@:path_provider_android::release,:@@:sign_in_with_apple::release,:@@:sqflite_android::release,io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,androidx.webkit:webkit:1.12.0@aar,com.stripe:financial-connections:21.6.0@aar,com.facebook.android:facebook-common:18.0.3@aar,com.stripe:stripe-android:21.6.0@aar,com.stripe:paymentsheet:21.6.0@aar,com.stripe:payments-ui-core:21.6.0@aar,com.stripe:payments-core:21.6.0@aar,com.stripe:stripe-ui-core:21.6.0@aar,com.stripe:payments-model:21.6.0@aar,com.stripe:stripe-core:21.6.0@aar,androidx.browser:browser:1.8.0@aar,androidx.legacy:legacy-support-v4:1.0.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.media:media:1.1.0@aar,androidx.credentials:credentials-play-services-auth:1.5.0@aar,com.google.android.libraries.identity.googleid:googleid:1.1.1@aar,androidx.credentials:credentials:1.5.0@aar,androidx.credentials:credentials:1.5.0@aar,androidx.biometric:biometric:1.1.0@aar,androidx.preference:preference:1.2.1@aar,androidx.navigation:navigation-common:2.7.7@aar,androidx.navigation:navigation-runtime:2.7.7@aar,androidx.navigation:navigation-common-ktx:2.7.7@aar,androidx.navigation:navigation-runtime-ktx:2.7.7@aar,androidx.navigation:navigation-compose:2.7.7@aar,androidx.activity:activity-compose:1.9.3@aar,com.stripe:stripe-3ds2-android:6.2.0@aar,com.google.accompanist:accompanist-systemuicontroller:0.34.0@aar,com.google.pay.button:compose-pay-button:0.1.3@aar,com.google.accompanist:accompanist-themeadapter-appcompat:0.34.0@aar,com.google.accompanist:accompanist-themeadapter-material:0.34.0@aar,com.google.accompanist:accompanist-themeadapter-material3:0.34.0@aar,androidx.compose.material3:material3:1.0.1@aar,androidx.compose.material:material-ripple-android:1.6.8@aar,androidx.compose.material:material-icons-core-android:1.6.8@aar,androidx.compose.material:material-android:1.6.8@aar,com.google.accompanist:accompanist-themeadapter-core:0.34.0@aar,com.google.accompanist:accompanist-flowlayout:0.34.0@aar,androidx.compose.animation:animation-core-android:1.6.8@aar,androidx.compose.animation:animation-android:1.6.8@aar,androidx.compose.foundation:foundation-layout-android:1.6.8@aar,androidx.compose.foundation:foundation-android:1.6.8@aar,androidx.compose.ui:ui-unit-android:1.6.8@aar,androidx.compose.ui:ui-graphics-android:1.6.8@aar,androidx.compose.ui:ui-geometry-android:1.6.8@aar,androidx.compose.ui:ui-text-android:1.6.8@aar,androidx.compose.ui:ui-util-android:1.6.8@aar,androidx.compose.ui:ui-tooling-preview-android:1.6.8@aar,com.razorpay:checkout:1.6.41@aar,com.razorpay:standard-core:1.6.52@aar,com.google.android.gms:play-services-auth:21.3.0@aar,com.facebook.android:facebook-core:18.0.3@aar,com.google.firebase:firebase-messaging:24.1.2@aar,com.google.android.gms:play-services-stats:17.0.2@aar,com.google.android.material:material:1.12.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.transition:transition:1.5.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,com.google.android.gms:play-services-wallet:19.4.0@aar,com.google.android.gms:play-services-maps:18.2.0@aar,com.github.bumptech.glide:glide:4.12.0@aar,com.google.android.gms:play-services-location:21.2.0@aar,com.google.android.gms:play-services-auth-blockstore:16.4.0@aar,com.google.android.gms:play-services-fido:21.0.0@aar,com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.android.gms:play-services-auth-base:18.0.10@aar,com.google.android.gms:play-services-identity:18.1.0@aar,com.google.android.gms:play-services-base:18.5.0@aar,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.android.gms:play-services-cloud-messaging:17.2.0@aar,androidx.window:window-java:1.2.0@aar,androidx.window:window:1.2.0@aar,androidx.constraintlayout:constraintlayout:2.2.0@aar,com.stripe:hcaptcha:21.6.0@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-livedata:2.8.7@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar,androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-process:2.8.7@aar,androidx.lifecycle:lifecycle-common-java8:2.8.7@jar,androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar,androidx.compose.runtime:runtime-saveable-android:1.7.1@aar,androidx.compose.runtime:runtime-android:1.7.1@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1@jar,androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar,androidx.datastore:datastore-preferences-proto:1.1.3@jar,androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar,androidx.datastore:datastore-core-okio-jvm:1.1.3@jar,androidx.datastore:datastore-core-android:1.1.3@aar,androidx.datastore:datastore-preferences-android:1.1.3@aar,androidx.datastore:datastore-android:1.1.3@aar,com.stripe:attestation:21.6.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.10.1@jar,com.google.android.play:integrity:1.4.0@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.recyclerview:recyclerview:1.3.2@aar,androidx.viewpager2:viewpager2:1.1.0-beta02@aar,androidx.fragment:fragment:1.8.6@aar,androidx.fragment:fragment:1.8.6@aar,androidx.loader:loader:1.1.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7@aar,androidx.compose.ui:ui-android:1.6.8@aar,androidx.compose.ui:ui-viewbinding:1.6.8@aar,androidx.fragment:fragment-ktx:1.8.6@aar,androidx.activity:activity-ktx:1.9.3@aar,androidx.activity:activity:1.9.3@aar,androidx.viewpager:viewpager:1.0.0@aar,com.facebook.android:facebook-bolts:18.0.3@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.16.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.customview:customview:1.1.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.autofill:autofill:1.0.0@aar,androidx.core:core:1.16.0@aar,androidx.core:core:1.16.0@aar,com.airbnb.android:showkase-annotation:1.0.0-beta18@jar,com.squareup.okio:okio-jvm:3.4.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.1.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.0@jar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.security:security-crypto:1.1.0-alpha06@aar,com.google.crypto.tink:tink-android:1.9.0@jar,androidx.exifinterface:exifinterface:1.3.7@aar,androidx.profileinstaller:profileinstaller:1.4.0@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,com.github.bumptech.glide:gifdecoder:4.12.0@aar,androidx.cardview:cardview:1.0.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,com.google.firebase:firebase-components:18.0.0@aar,com.google.firebase:firebase-datatransport:18.2.0@aar,com.google.android.datatransport:transport-backend-cct:3.1.9@aar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.android.datatransport:transport-runtime:3.1.9@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.android.datatransport:transport-api:3.1.0@aar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.collection:collection-ktx:1.4.2@jar,androidx.collection:collection-jvm:1.4.2@jar,androidx.databinding:viewbinding:8.8.1@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.window.extensions.core:core:1.0.0@aar,androidx.constraintlayout:constraintlayout-core:1.1.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.annotation:annotation-jvm:1.9.1@jar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.core:core-viewtree:1.0.0@aar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.8.0@jar,org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.8.0@jar,org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21@jar,dev.drewhamilton.poko:poko-annotations-jvm:0.18.2@jar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.10@jar,io.flutter:armeabi_v7a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,io.flutter:arm64_v8a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,io.flutter:x86_64_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,org.jetbrains:annotations:23.0.0@jar,com.google.code.gson:gson:2.12.0@jar,com.google.maps.android:android-maps-utils:3.6.0@aar,com.getkeepsafe.relinker:relinker:1.4.5@aar,com.google.guava:listenablefuture:1.0@jar,com.google.errorprone:error_prone_annotations:2.36.0@jar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.dagger:dagger:2.55@jar,org.jspecify:jspecify:1.0.0@jar,com.github.bumptech.glide:disklrucache:4.12.0@jar,com.github.bumptech.glide:annotations:4.12.0@jar,com.google.firebase:firebase-annotations:16.2.0@jar,com.android.installreferrer:installreferrer:1.0@aar,com.google.zxing:core:3.3.3@jar,com.google.android.instantapps:instantapps:1.1.0@aar,com.jakewharton:disklrucache:2.0.2@jar,jakarta.inject:jakarta.inject-api:2.0.1@jar,javax.inject:javax.inject:1@jar,org.bouncycastle:bcprov-jdk15to18:1.79@jar,com.nimbusds:nimbus-jose-jwt:9.46@jar,com.google.android.play:core-common:2.0.4@aar">
    <dependency
        name="__local_aars__:C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app\intermediates\flutter\release\libs.jar:unspecified@jar"
        simpleName="__local_aars__:C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app\intermediates\flutter\release\libs.jar"/>
    <dependency
        name=":@@:app_badge_plus::release"
        simpleName="artifacts::app_badge_plus"/>
    <dependency
        name=":@@:flutter_timezone::release"
        simpleName="artifacts::flutter_timezone"/>
    <dependency
        name=":@@:google_sign_in_android::release"
        simpleName="artifacts::google_sign_in_android"/>
    <dependency
        name=":@@:package_info_plus::release"
        simpleName="artifacts::package_info_plus"/>
    <dependency
        name=":@@:shared_preferences_android::release"
        simpleName="artifacts::shared_preferences_android"/>
    <dependency
        name=":@@:stripe_android::release"
        simpleName="artifacts::stripe_android"/>
    <dependency
        name=":@@:flutter_facebook_auth::release"
        simpleName="artifacts::flutter_facebook_auth"/>
    <dependency
        name="com.facebook.android:facebook-login:18.0.3@aar"
        simpleName="com.facebook.android:facebook-login"/>
    <dependency
        name=":@@:flutter_local_notifications::release"
        simpleName="artifacts::flutter_local_notifications"/>
    <dependency
        name=":@@:geolocator_android::release"
        simpleName="artifacts::geolocator_android"/>
    <dependency
        name=":@@:image_picker_android::release"
        simpleName="artifacts::image_picker_android"/>
    <dependency
        name=":@@:url_launcher_android::release"
        simpleName="artifacts::url_launcher_android"/>
    <dependency
        name=":@@:firebase_messaging::release"
        simpleName="artifacts::firebase_messaging"/>
    <dependency
        name=":@@:firebase_core::release"
        simpleName="artifacts::firebase_core"/>
    <dependency
        name=":@@:flutter_inappwebview_android::release"
        simpleName="artifacts::flutter_inappwebview_android"/>
    <dependency
        name=":@@:google_maps_flutter_android::release"
        simpleName="artifacts::google_maps_flutter_android"/>
    <dependency
        name=":@@:flutter_plugin_android_lifecycle::release"
        simpleName="artifacts::flutter_plugin_android_lifecycle"/>
    <dependency
        name=":@@:flutter_secure_storage::release"
        simpleName="artifacts::flutter_secure_storage"/>
    <dependency
        name=":@@:razorpay_flutter::release"
        simpleName="artifacts::razorpay_flutter"/>
    <dependency
        name=":@@:fluttertoast::release"
        simpleName="artifacts::fluttertoast"/>
    <dependency
        name=":@@:geocoding_android::release"
        simpleName="artifacts::geocoding_android"/>
    <dependency
        name=":@@:path_provider_android::release"
        simpleName="artifacts::path_provider_android"/>
    <dependency
        name=":@@:sign_in_with_apple::release"
        simpleName="artifacts::sign_in_with_apple"/>
    <dependency
        name=":@@:sqflite_android::release"
        simpleName="artifacts::sqflite_android"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.webkit:webkit:1.12.0@aar"
        simpleName="androidx.webkit:webkit"/>
    <dependency
        name="com.stripe:financial-connections:21.6.0@aar"
        simpleName="com.stripe:financial-connections"/>
    <dependency
        name="com.facebook.android:facebook-common:18.0.3@aar"
        simpleName="com.facebook.android:facebook-common"/>
    <dependency
        name="com.stripe:stripe-android:21.6.0@aar"
        simpleName="com.stripe:stripe-android"/>
    <dependency
        name="com.stripe:paymentsheet:21.6.0@aar"
        simpleName="com.stripe:paymentsheet"/>
    <dependency
        name="com.stripe:payments-ui-core:21.6.0@aar"
        simpleName="com.stripe:payments-ui-core"/>
    <dependency
        name="com.stripe:payments-core:21.6.0@aar"
        simpleName="com.stripe:payments-core"/>
    <dependency
        name="com.stripe:stripe-ui-core:21.6.0@aar"
        simpleName="com.stripe:stripe-ui-core"/>
    <dependency
        name="com.stripe:payments-model:21.6.0@aar"
        simpleName="com.stripe:payments-model"/>
    <dependency
        name="com.stripe:stripe-core:21.6.0@aar"
        simpleName="com.stripe:stripe-core"/>
    <dependency
        name="androidx.browser:browser:1.8.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.media:media:1.1.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.credentials:credentials-play-services-auth:1.5.0@aar"
        simpleName="androidx.credentials:credentials-play-services-auth"/>
    <dependency
        name="com.google.android.libraries.identity.googleid:googleid:1.1.1@aar"
        simpleName="com.google.android.libraries.identity.googleid:googleid"/>
    <dependency
        name="androidx.credentials:credentials:1.5.0@aar"
        simpleName="androidx.credentials:credentials"/>
    <dependency
        name="androidx.biometric:biometric:1.1.0@aar"
        simpleName="androidx.biometric:biometric"/>
    <dependency
        name="androidx.preference:preference:1.2.1@aar"
        simpleName="androidx.preference:preference"/>
    <dependency
        name="androidx.navigation:navigation-common:2.7.7@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.7.7@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-common-ktx:2.7.7@aar"
        simpleName="androidx.navigation:navigation-common-ktx"/>
    <dependency
        name="androidx.navigation:navigation-runtime-ktx:2.7.7@aar"
        simpleName="androidx.navigation:navigation-runtime-ktx"/>
    <dependency
        name="androidx.navigation:navigation-compose:2.7.7@aar"
        simpleName="androidx.navigation:navigation-compose"/>
    <dependency
        name="androidx.activity:activity-compose:1.9.3@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="com.stripe:stripe-3ds2-android:6.2.0@aar"
        simpleName="com.stripe:stripe-3ds2-android"/>
    <dependency
        name="com.google.accompanist:accompanist-systemuicontroller:0.34.0@aar"
        simpleName="com.google.accompanist:accompanist-systemuicontroller"/>
    <dependency
        name="com.google.pay.button:compose-pay-button:0.1.3@aar"
        simpleName="com.google.pay.button:compose-pay-button"/>
    <dependency
        name="com.google.accompanist:accompanist-themeadapter-appcompat:0.34.0@aar"
        simpleName="com.google.accompanist:accompanist-themeadapter-appcompat"/>
    <dependency
        name="com.google.accompanist:accompanist-themeadapter-material:0.34.0@aar"
        simpleName="com.google.accompanist:accompanist-themeadapter-material"/>
    <dependency
        name="com.google.accompanist:accompanist-themeadapter-material3:0.34.0@aar"
        simpleName="com.google.accompanist:accompanist-themeadapter-material3"/>
    <dependency
        name="androidx.compose.material3:material3:1.0.1@aar"
        simpleName="androidx.compose.material3:material3"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.6.8@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.6.8@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.material:material-android:1.6.8@aar"
        simpleName="androidx.compose.material:material-android"/>
    <dependency
        name="com.google.accompanist:accompanist-themeadapter-core:0.34.0@aar"
        simpleName="com.google.accompanist:accompanist-themeadapter-core"/>
    <dependency
        name="com.google.accompanist:accompanist-flowlayout:0.34.0@aar"
        simpleName="com.google.accompanist:accompanist-flowlayout"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.6.8@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.6.8@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.6.8@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.6.8@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="com.razorpay:checkout:1.6.41@aar"
        simpleName="com.razorpay:checkout"/>
    <dependency
        name="com.razorpay:standard-core:1.6.52@aar"
        simpleName="com.razorpay:standard-core"/>
    <dependency
        name="com.google.android.gms:play-services-auth:21.3.0@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="com.facebook.android:facebook-core:18.0.3@aar"
        simpleName="com.facebook.android:facebook-core"/>
    <dependency
        name="com.google.firebase:firebase-messaging:24.1.2@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="com.google.android.gms:play-services-wallet:19.4.0@aar"
        simpleName="com.google.android.gms:play-services-wallet"/>
    <dependency
        name="com.google.android.gms:play-services-maps:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-maps"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.12.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="com.google.android.gms:play-services-location:21.2.0@aar"
        simpleName="com.google.android.gms:play-services-location"/>
    <dependency
        name="com.google.android.gms:play-services-auth-blockstore:16.4.0@aar"
        simpleName="com.google.android.gms:play-services-auth-blockstore"/>
    <dependency
        name="com.google.android.gms:play-services-fido:21.0.0@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02@aar"
        simpleName="com.google.android.gms:play-services-identity-credentials"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.android.gms:play-services-identity:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-identity"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.2.0@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.stripe:hcaptcha:21.6.0@aar"
        simpleName="com.stripe:hcaptcha"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.7.1@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.7.1@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-external-protobuf"/>
    <dependency
        name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-proto"/>
    <dependency
        name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-core-jvm"/>
    <dependency
        name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
        simpleName="androidx.datastore:datastore-core-okio-jvm"/>
    <dependency
        name="androidx.datastore:datastore-core-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-core-android"/>
    <dependency
        name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-preferences-android"/>
    <dependency
        name="androidx.datastore:datastore-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-android"/>
    <dependency
        name="com.stripe:attestation:21.6.0@aar"
        simpleName="com.stripe:attestation"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.10.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.play:integrity:1.4.0@aar"
        simpleName="com.google.android.play:integrity"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.2@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.fragment:fragment:1.8.6@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.compose.ui:ui-viewbinding:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-viewbinding"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.8.6@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.activity:activity-ktx:1.9.3@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.9.3@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="com.facebook.android:facebook-bolts:18.0.3@aar"
        simpleName="com.facebook.android:facebook-bolts"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.autofill:autofill:1.0.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="com.airbnb.android:showkase-annotation:1.0.0-beta18@jar"
        simpleName="com.airbnb.android:showkase-annotation"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.4.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.1.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.security:security-crypto:1.1.0-alpha06@aar"
        simpleName="androidx.security:security-crypto"/>
    <dependency
        name="com.google.crypto.tink:tink-android:1.9.0@jar"
        simpleName="com.google.crypto.tink:tink-android"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.7@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.12.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.2.0@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.2@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.2@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.databinding:viewbinding:8.8.1@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.window.extensions.core:core:1.0.0@aar"
        simpleName="androidx.window.extensions.core:core"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-core:1.1.0@jar"
        simpleName="androidx.constraintlayout:constraintlayout-core"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.8.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.8.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="dev.drewhamilton.poko:poko-annotations-jvm:0.18.2@jar"
        simpleName="dev.drewhamilton.poko:poko-annotations-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="io.flutter:armeabi_v7a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:armeabi_v7a_release"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="io.flutter:x86_64_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:x86_64_release"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.code.gson:gson:2.12.0@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.google.maps.android:android-maps-utils:3.6.0@aar"
        simpleName="com.google.maps.android:android-maps-utils"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.36.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.dagger:dagger:2.55@jar"
        simpleName="com.google.dagger:dagger"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.12.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.12.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="com.android.installreferrer:installreferrer:1.0@aar"
        simpleName="com.android.installreferrer:installreferrer"/>
    <dependency
        name="com.google.zxing:core:3.3.3@jar"
        simpleName="com.google.zxing:core"/>
    <dependency
        name="com.google.android.instantapps:instantapps:1.1.0@aar"
        simpleName="com.google.android.instantapps:instantapps"/>
    <dependency
        name="com.jakewharton:disklrucache:2.0.2@jar"
        simpleName="com.jakewharton:disklrucache"/>
    <dependency
        name="jakarta.inject:jakarta.inject-api:2.0.1@jar"
        simpleName="jakarta.inject:jakarta.inject-api"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="org.bouncycastle:bcprov-jdk15to18:1.79@jar"
        simpleName="org.bouncycastle:bcprov-jdk15to18"/>
    <dependency
        name="com.nimbusds:nimbus-jose-jwt:9.46@jar"
        simpleName="com.nimbusds:nimbus-jose-jwt"/>
    <dependency
        name="com.google.android.play:core-common:2.0.4@aar"
        simpleName="com.google.android.play:core-common"/>
  </package>
</dependencies>

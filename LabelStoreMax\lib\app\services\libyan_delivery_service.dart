//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import '/app/models/libyan_city.dart';
import '/app/models/checkout_session.dart';
import '/app/models/shipping_type.dart';
import '/app/services/woocommerce_service.dart';

/// Service to handle Libyan city-based delivery cost calculations
class LibyanDeliveryService {
  static final LibyanDeliveryService _instance = LibyanDeliveryService._internal();
  factory LibyanDeliveryService() => _instance;
  LibyanDeliveryService._internal();

  /// Get delivery cost based on selected Libyan city
  double getDeliveryCostForCity(String cityName) {
    print('=== Getting Delivery Cost for City: $cityName ===');
    
    // Try to find city by Arabic name first
    LibyanCity? city = LibyanCitiesData.findCityByArabicName(cityName);
    
    // If not found, try English name
    city ??= LibyanCitiesData.findCityByName(cityName);
    
    if (city != null) {
      print('✅ Found city: ${city.getDisplayName()} - Cost: ${city.deliveryCost} LYD');
      return city.deliveryCost;
    } else {
      print('❌ City not found: $cityName, using default cost');
      return 15.0; // Default delivery cost
    }
  }

  /// Get delivery cost from checkout session
  double getDeliveryCostFromCheckoutSession() {
    print('=== Getting Delivery Cost from Checkout Session ===');
    
    final checkoutSession = CheckoutSession.getInstance;
    
    // Try to get city from billing address
    String? city = checkoutSession.billingDetails?.billingAddress?.city;
    
    // If no billing city, try shipping address
    city ??= checkoutSession.billingDetails?.shippingAddress?.city;
    
    if (city != null && city.isNotEmpty) {
      return getDeliveryCostForCity(city);
    } else {
      print('⚠️ No city found in checkout session, using default cost');
      return 15.0; // Default delivery cost
    }
  }

  /// Create a custom shipping method with Libyan city delivery cost
  ShippingType createLibyanCityShippingMethod(String cityName) {
    print('=== Creating Libyan City Shipping Method ===');

    final deliveryCost = getDeliveryCostForCity(cityName);
    final city = LibyanCitiesData.findCityByName(cityName) ??
                 LibyanCitiesData.findCityByArabicName(cityName);

    String methodTitle = 'توصيل إلى ${city?.getDisplayName() ?? cityName}'; // Arabic: "Delivery to [City]"

    Map<String, dynamic> shippingMethodData = {
      'method_id': 'libyan_city_delivery',
      'title': methodTitle,
      'cost': deliveryCost.toString(),
      'city_name': cityName,
      'city_arabic': city?.nameArabic ?? cityName,
    };

    return ShippingType.fromDynamicMethod(shippingMethodData);
  }

  /// Create a custom shipping method with dynamic cost from API
  ShippingType createLibyanCityShippingMethodWithDynamicCost(String cityName, double dynamicCost) {
    print('=== Creating Libyan City Shipping Method with Dynamic Cost ===');

    final city = LibyanCitiesData.findCityByName(cityName) ??
                 LibyanCitiesData.findCityByArabicName(cityName);

    String methodTitle = 'توصيل إلى ${city?.getDisplayName() ?? cityName}'; // Arabic: "Delivery to [City]"

    Map<String, dynamic> shippingMethodData = {
      'method_id': 'v_shipping_by_city', // Use the same method ID as WooCommerce
      'title': methodTitle,
      'cost': dynamicCost.toString(),
      'city_name': cityName,
      'city_arabic': city?.nameArabic ?? cityName,
    };

    print('✅ Updated shipping cost to ${dynamicCost} LYD for $cityName');
    return ShippingType.fromDynamicMethod(shippingMethodData);
  }

  /// Update shipping cost based on selected city using dynamic API
  Future<void> updateShippingCostForCity(String cityName) async {
    print('=== Updating Shipping Cost for City: $cityName ===');

    final checkoutSession = CheckoutSession.getInstance;

    try {
      // Fetch dynamic shipping cost from the new API
      final wooCommerceService = WooCommerceService();
      final dynamicCost = await wooCommerceService.fetchDynamicShippingCost(cityName);

      print('🔍 Dynamic API returned cost: $dynamicCost LYD for $cityName');

      // Update existing shipping method if it exists
      if (checkoutSession.shippingType != null) {
        print('🔄 Updating existing shipping method cost...');
        print('🔍 Dynamic cost type: ${dynamicCost.runtimeType}');
        print('🔍 Dynamic cost value: $dynamicCost');

        // CRITICAL FIX: Ensure the dynamic cost is properly assigned as a double converted to string
        final costAsDouble = (dynamicCost as num).toDouble();
        final costAsString = costAsDouble.toString();

        print('🔍 Before update - CheckoutSession cost: ${checkoutSession.shippingType!.cost}');

        // Update the cost in the existing ShippingType (expects String)
        checkoutSession.shippingType!.cost = costAsString;

        print('🔍 After update - CheckoutSession cost: ${checkoutSession.shippingType!.cost}');

        // CRITICAL: Ensure ShippingType.object is properly populated for order creation
        if (checkoutSession.shippingType!.object != null) {
          var shippingObject = checkoutSession.shippingType!.object;
          print('🔍 Shipping object type: ${shippingObject.runtimeType}');

          if (shippingObject.runtimeType.toString() == 'ShippingMethod') {
            // Update the settings to include the dynamic cost - use costAsDouble for accurate calculation
            (shippingObject as dynamic).settings['cost'] = {
              'value': costAsDouble.toString(),
              'id': 'cost',
              'label': 'Cost',
              'type': 'text',
            };
            print('✅ Updated ShippingMethod object settings with dynamic cost: $costAsDouble');
          }

          // Also update CustomShippingMethod objects
          if (shippingObject.runtimeType.toString() == 'CustomShippingMethod') {
            (shippingObject as dynamic).cost = costAsDouble.toString();
            print('✅ Updated CustomShippingMethod object cost: $costAsDouble');
          }
        } else {
          // CRITICAL FIX: Create a proper shipping object if it's null (fixes "Shipping is required" error)
          print('⚠️ ShippingType.object is null - creating CustomShippingMethod object');

          final city = LibyanCitiesData.findCityByName(cityName) ??
                       LibyanCitiesData.findCityByArabicName(cityName);

          CustomShippingMethod shippingMethodForSession = CustomShippingMethod(
            title: "التوصيل (درب السبيل)", // Arabic: "Delivery (Darb Al-Sabil)"
            methodId: "v_shipping_by_city",
            cost: costAsString,
            description: 'توصيل إلى ${city?.getDisplayName() ?? cityName}',
          );

          checkoutSession.shippingType!.object = shippingMethodForSession;
          print('✅ Created CustomShippingMethod object for CheckoutSession');
        }

        print('✅ Updated existing shipping method cost to $costAsDouble LYD');
      } else {
        print('🔄 Creating new shipping method with dynamic cost...');

        // CRITICAL FIX: Ensure the dynamic cost is properly converted to double
        final costAsDouble = (dynamicCost as num).toDouble();

        // Create custom shipping method with dynamic cost
        final cityShippingMethod = createLibyanCityShippingMethodWithDynamicCost(cityName, costAsDouble);

        // Update checkout session with the new shipping method
        checkoutSession.shippingType = cityShippingMethod;

        print('✅ Created new shipping method with cost $costAsDouble LYD');
        print('🔍 New shipping method cost in CheckoutSession: ${checkoutSession.shippingType?.cost}');
      }

    } catch (e) {
      print('❌ Failed to fetch dynamic shipping cost: $e');
      print('🔄 Falling back to static cost...');

      // Fallback to static cost from LibyanCitiesData
      final staticCost = getDeliveryCostForCity(cityName);
      final staticCostAsString = staticCost.toString();

      if (checkoutSession.shippingType != null) {
        checkoutSession.shippingType!.cost = staticCostAsString;
        print('✅ Updated with fallback static cost: $staticCostAsString LYD');
      } else {
        final cityShippingMethod = createLibyanCityShippingMethod(cityName);
        checkoutSession.shippingType = cityShippingMethod;
        print('✅ Created new shipping method with fallback cost: $staticCostAsString LYD');
      }
    }
  }

  /// Get all available cities with their delivery costs
  List<LibyanCity> getAllCitiesWithCosts() {
    return LibyanCitiesData.getAllCities();
  }

  /// Get cities by region
  List<LibyanCity> getCitiesByRegion(String region) {
    return LibyanCitiesData.getCitiesByRegion(region);
  }

  /// Search cities by name (Arabic or English)
  List<LibyanCity> searchCities(String query) {
    return LibyanCitiesData.searchCities(query);
  }

  /// Get all regions
  List<String> getAllRegions() {
    return LibyanCitiesData.getAllRegions();
  }

  /// Validate if a city exists in the Libyan cities list
  bool isCityValid(String cityName) {
    return LibyanCitiesData.findCityByName(cityName) != null ||
           LibyanCitiesData.findCityByArabicName(cityName) != null;
  }

  /// Get formatted delivery cost string
  String getFormattedDeliveryCost(String cityName) {
    final cost = getDeliveryCostForCity(cityName);
    return '${cost.toStringAsFixed(0)} د.ل'; // LYD symbol in Arabic
  }

  /// Calculate total shipping cost including any additional fees
  double calculateTotalShippingCost(String cityName, {double additionalFees = 0.0}) {
    final baseCost = getDeliveryCostForCity(cityName);
    final totalCost = baseCost + additionalFees;
    
    print('Base cost: $baseCost LYD, Additional fees: $additionalFees LYD, Total: $totalCost LYD');
    return totalCost;
  }

  /// Get delivery estimate based on city region
  String getDeliveryEstimate(String cityName) {
    final city = LibyanCitiesData.findCityByName(cityName) ?? 
                 LibyanCitiesData.findCityByArabicName(cityName);
    
    if (city == null) return '2-3 أيام عمل'; // Default: 2-3 business days
    
    switch (city.region) {
      case 'Tripoli':
        return '1-2 أيام عمل'; // 1-2 business days
      case 'Benghazi':
      case 'Misrata':
        return '2-3 أيام عمل'; // 2-3 business days
      case 'Sabha':
        return '3-5 أيام عمل'; // 3-5 business days
      default:
        return '2-4 أيام عمل'; // 2-4 business days
    }
  }

  /// Check if express delivery is available for a city
  bool isExpressDeliveryAvailable(String cityName) {
    final city = LibyanCitiesData.findCityByName(cityName) ?? 
                 LibyanCitiesData.findCityByArabicName(cityName);
    
    // Express delivery only available in major cities
    return city?.region == 'Tripoli' || city?.region == 'Benghazi' || city?.region == 'Misrata';
  }

  /// Get express delivery cost (additional fee)
  double getExpressDeliveryCost() {
    return 10.0; // Additional 10 LYD for express delivery
  }
}
